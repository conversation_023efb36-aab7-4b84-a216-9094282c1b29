/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const TraceSourceService = {
    /**
     * 优质溯源-灌溉数据
     * @returns Promise 
     */
    'traceSourceHighQualityIrrigationPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2a97ee83593a0562fbbbab3207121da42e1a8d55')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityIrrigationPicList`, 'json', 'GET');
    }, 
    /**
     * 补充灌溉数据
     * @param {*} id 溯源Id
     * @param {*} irrigationList 灌溉数据
     * @returns Promise 
     */
    'supplementIrrigation': function (id, irrigationList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'de05aee2bc9ee960381b3b34ae35bffe6e5fd974', {id, irrigationList})
            : execute(concreteModuleName, `/TraceSourceService/supplementIrrigation`, 'json', 'POST', { id, irrigationList });
    }, 
    /**
     * 优质溯源-生长环境图片
     * @returns Promise 
     */
    'traceSourceHighQualityGrowingEnvironmentPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2d152db6f17b15541bea4e896352db1b48673a98')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityGrowingEnvironmentPicList`, 'json', 'GET');
    }, 
    /**
     * 补充溯源档案生长照片
     * @param {*} id 溯源Id
     * @param {*} picUrl 生长环境照片信息
     * @returns Promise 
     */
    'supplementTraceSourceGrowthImages': function (id, picUrl) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd2e576044a689cf11bc914852152be34cc7f1205', {id, picUrl})
            : execute(concreteModuleName, `/TraceSourceService/supplementTraceSourceGrowthImages`, 'json', 'POST', { id, picUrl });
    }, 
    /**
     * 优质溯源-宣传视频
     * @returns Promise 
     */
    'traceSourceHighQualityVideoPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e0b73a5e8de8b063bbd8301e3f5b0a331c563d0a')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityVideoPicList`, 'json', 'GET');
    }, 
    /**
     * 优质溯源-认证文件
     * @returns Promise 
     */
    'traceSourceHighQualityCertificationPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6b906037a35c0b9497c7f5d4b25a1b2a3ce167b4')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityCertificationPicList`, 'json', 'GET');
    }, 
    /**
     * 补充视频
     * @param {*} id 溯源Id
     * @param {*} video 视频
     * @param {*} videoCover 视频封面
     * @returns Promise 
     */
    'supplementVideo': function (id, video, videoCover) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c80dcf6515daab9ba7165106af557942d0bbb44e', {id, video, videoCover})
            : execute(concreteModuleName, `/TraceSourceService/supplementVideo`, 'json', 'POST', { id, video, videoCover });
    }, 
    /**
     * 补充溯源档案认证文件
     * @param {*} id 溯源Id
     * @param {*} traceSourceCertificationPo 认证文件
     * @returns Promise 
     */
    'supplementTraceSourceCertification': function (id, traceSourceCertificationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e9ea7d59921ef853dcf432fcb08c785412513f23', {id, traceSourceCertificationPo})
            : execute(concreteModuleName, `/TraceSourceService/supplementTraceSourceCertification`, 'json', 'POST', { id, traceSourceCertificationPo });
    }, 
    /**
     * 补充水质报告
     * @param {*} id 溯源Id
     * @param {*} waterList 水质报告
     * @returns Promise 
     */
    'supplementWaterQuality': function (id, waterList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5d0b2b0f8b1e09fe837e78c0b084356a40890fe1', {id, waterList})
            : execute(concreteModuleName, `/TraceSourceService/supplementWaterQuality`, 'json', 'POST', { id, waterList });
    }, 
    /**
     * 优质溯源-水质环境
     * @returns Promise 
     */
    'traceSourceHighQualityWaterPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'baa38dbeff1c22678aed45d8f9803a3afb061915')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityWaterPicList`, 'json', 'GET');
    }
}

export default TraceSourceService

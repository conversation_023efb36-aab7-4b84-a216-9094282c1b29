<template>
  <div class="traceRecordAddDialog dialogStyle">
    <el-dialog
      :title="title"
      width="763px"
      center
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="cancel">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="addFrom">
        <el-form ref="form" :model="ruleForm" :rules="rules" label-width="120px">
          <div class="addFrom_title">
            <img src="../../assets/image/centralControlPlatform/point2.png" alt="" class="addFrom_title_icon">
            <div class="addFrom_title_text">基本信息</div>
          </div>
          <el-form-item prop="plantId" label="种植批次：" class="form_select formStyle">
            <el-select v-model="ruleForm.plantId" @change="plantBatchChange" popper-class="selectStyle_list" placeholder="请选择种植批次">
              <el-option
                      v-for="item in plantBatchData"
                      :key="item.batchId"
                      :label="item.batchId"
                      :value="item.batchId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="finishId" label="收获批次：" class="form_select formStyle">
            <el-select v-model="ruleForm.finishId" @change="finishBatchChange" popper-class="selectStyle_list" placeholder="请选择收获批次">
              <el-option
                      v-for="item in finishBatchData"
                      :key="item.harvestBatchId"
                      :label="item.harvestBatchId"
                      :value="item.harvestBatchId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="crop" label="作物类型：" class="form_select formStyle">
            <el-select v-model="ruleForm.crop" disabled popper-class="selectStyle_list" placeholder="请选择作物类型">
              <el-option
                      v-for="item in cropData"
                      :key="item.value"
                      :label="item.key"
                      :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="areaId" label="产出区域：" class="form_select formStyle">
            <el-select v-model="ruleForm.areaId" disabled popper-class="selectStyle_list" placeholder="请选择产出区域">
              <el-option
                      v-for="item in areaData"
                      :key="item.areaId"
                      :label="item.areaName"
                      :value="item.areaId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="name" label="档案名称：" class="form_select formStyle">
            <el-input v-model="ruleForm.name" placeholder="请输入档案名称"></el-input>
          </el-form-item>
          <el-form-item prop="variety" label="作物品种：" class="form_select formStyle">
            <el-input v-model="ruleForm.variety" placeholder="请输入作物品种"></el-input>
          </el-form-item>
          <el-form-item prop="yield" label="作物产量：" class="form_select formStyle">
            <el-input v-model="ruleForm.yield" placeholder="请输入本批次作物总产量">
              <template slot="append">kg</template>
            </el-input>
          </el-form-item>
          <el-form-item prop="listedTime" label="上市时间：" class="form_select formStyle">
            <el-date-picker
                    v-model="ruleForm.listedTime"
                    align="right"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                    popper-class='datePickerStyle'
                    >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="synopsis" label="产品简介：" class="form_textArea formStyle">
            <el-input type="textarea" v-model="ruleForm.synopsis" placeholder="请输入产品简介"></el-input>
          </el-form-item>
          <el-form-item prop="photoList" label="宣传图片：" class="formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    list-type="picture-card"
                    :file-list="fileList"
                    :limit="3"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <img src="../../assets/image/centralControlPlatform/upload-add.png" alt="">
            </el-upload>
          </el-form-item>
          <div class="addFrom_title">
            <img src="../../assets/image/centralControlPlatform/point2.png" alt="" class="addFrom_title_icon">
            <div class="addFrom_title_text">档案信息</div>
          </div>
          <el-form-item label="生长环境图片：" class="formStyle" :class="{hide:hideUploadSZ}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    list-type="picture-card"
                    :file-list="fileListSZ"
                    :limit="6"
                    :on-exceed="handleExceedSZ"
                    :on-change="handleChangeSZ"
                    :auto-upload="false"
                    :on-remove="handleRemoveSZ">
              <img src="../../assets/image/centralControlPlatform/upload-add.png" alt="">
            </el-upload>
          </el-form-item>
          <el-form-item label="相关视频：" class="form_upload formStyle">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileListVideo"
                    :limit="1"
                    :on-exceed="handleExceedVideo"
                    :on-change="handleChangeVideo"
                    :auto-upload="false"
                    :on-remove="handleRemoveVideo">
              <el-input readonly placeholder="点击选择视频文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item label="水质报告：" class="form_upload formStyle">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileListSZReport"
                    :limit="3"
                    :on-exceed="handleExceedSZReport"
                    :on-change="handleChangeSZReport"
                    :auto-upload="false"
                    :on-remove="handleRemoveSZReport">
              <el-input readonly placeholder="点击选择文件/图片"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item label="灌溉数据：" class="form_item2 formStyle">
            <img src="../../assets/image/centralControlPlatform/add-icon1.png" alt="" class="form_item2_add" @click="dataAdd()">
            <div class="form_item2_list" v-for="(item,index) in irrigationList" :key="index">
              <div class="form_item2_list_date formStyle">
                <el-date-picker
                        v-model="irrigationList[index].showTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd"
                        popper-class='datePickerStyle'
                >
                </el-date-picker>
              </div>
              <div class="form_item2_list_input">
                <el-input v-model="irrigationList[index].value" placeholder="请输入灌溉量"></el-input>
              </div>
              <div class="form_item2_list_unit">m³</div>
              <img src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="form_item2_list_delete" @click="dataDelete(index)">
            </div>
          </el-form-item>
          <div class="addFrom_title">
            <img src="../../assets/image/centralControlPlatform/point2.png" alt="" class="addFrom_title_icon">
            <div class="addFrom_title_text">认证文件</div>
          </div>
          <el-form-item label="农产品检测报告：" class="form_upload formStyle">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileListNCP"
                    :limit="1"
                    :on-exceed="handleExceedNCP"
                    :on-change="handleChangeNCP"
                    :auto-upload="false"
                    :on-remove="handleRemoveNCP">
              <el-input readonly placeholder="点击选择文件/图片"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item label="有机农产品认证：" class="form_upload formStyle">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileListYJ"
                    :limit="1"
                    :on-exceed="handleExceedYJ"
                    :on-change="handleChangeYJ"
                    :auto-upload="false"
                    :on-remove="handleRemoveYJ">
              <el-input readonly placeholder="点击选择文件/图片"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item label="绿色农产品认证：" class="form_upload formStyle">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileListLS"
                    :limit="1"
                    :on-exceed="handleExceedLS"
                    :on-change="handleChangeLS"
                    :auto-upload="false"
                    :on-remove="handleRemoveLS">
              <el-input readonly placeholder="点击选择文件/图片"></el-input>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="submit_button searchButtonStyle" @click="cancel()">
          <el-button>取消</el-button>
        </div>
        <div class="submit_button submitButtonStyle" @click="submit('form')">
          <el-button>提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import BatchCommonService from '../../jaxrs/concrete/com.zny.ia.api.BatchCommonService'
  import cropData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类'
  import TraceSourceCommonService from "../../jaxrs/concrete/com.zny.ia.api.TraceSourceCommonService";
export default {
  name:'traceRecordAddDialog',
  data(){
    return{
      title:"溯源建档",
      dialogVisible:false,
      traceId:'',
      plantBatchData:[],
      finishBatchData:[],
      areaData:[],
      cropData: cropData._toArray(),
      ruleForm:{
        plantId:'',
        finishId:'',
        areaId:'',
        crop:'',
        name:'',
        variety:'',
        yield:'',
        listedTime:'',
        synopsis:'',
        //宣传图片
        imagesPath:[],
        litPicUrl:[],
        photoList:[],
        //生长环境
        imagesPathSZ:[],
        litPicUrlSZ:[],
        photoListSZ:[],
        //相关视频
        imagesPathVideo:[],
        litPicUrlVideo:[],
        photoListVideo:[],
        videoCover:[],
        oldVideoCover:[],
        //水质报告
        imagesPathSZReport:[],
        litPicUrlSZReport:[],
        photoListSZReport:[],
        //农产品检测报告
        imagesPathNCP:[],
        litPicUrlNCP:[],
        photoListNCP:[],
        //有机农产品检测报告
        imagesPathYJ:[],
        litPicUrlYJ:[],
        photoListYJ:[],
        //绿色农产品检测报告
        imagesPathLS:[],
        litPicUrlLS:[],
        photoListLS:[],
      },
      rules:{
        plantId: [
          { required: true, message: '请选择种植批次', trigger: 'change' },
        ],
        finishId: [
          { required: true, message: '请选择收获批次', trigger: 'change' },
        ],
        areaId: [
          { required: true, message: '请选择产出区域', trigger: 'change' },
        ],
        crop: [
          { required: true, message: '请选择作物类型', trigger: 'change' },
        ],
        name: [
          { required: true, message: '请输入档案名称', trigger: 'blur' },
        ],
        variety: [
          { required: true, message: '请输入作物品种', trigger: 'blur' },
        ],
        yield: [
          { required: true, message: '请输入本次作物总产量', trigger: 'blur' },
        ],
        listedTime: [
          { required: true, message: '请选择上市时间', trigger: 'change' },
        ],
        synopsis: [
          { required: true, message: '请输入产品简介', trigger: 'blur' },
        ],
        photoList:[
          { required: true, message: '请上传图片', trigger: 'blur' },
        ],
      },
      //灌溉数据
      irrigationList:[],
      //宣传图片
      fileList:[],
      hideUpload:false,
      //生长环境
      fileListSZ:[],
      hideUploadSZ:false,
      //相关视频
      fileListVideo:[],
      //水质报告
      fileListSZReport:[],
      //农产品检测报告
      fileListNCP:[],
      //有机农产品检测报告
      fileListYJ:[],
      //绿色农产品检测报告
      fileListLS:[],
    }
  },
  mounted(){
    this.listen('traceRecordAddDialogOpen', (id) => {
      this.traceId=id
      this.dialogVisible=true
      this.getPlantBatch()
      if(id){
        this.getDetail(id)
      }
    })
  },
  methods:{
    //获取种植批次
    getPlantBatch(){
      BatchCommonService.haveHarvestBatch()
      .then(res=>{
        this.plantBatchData=res
      })
    },
    //种植批次选择
    plantBatchChange(val){
      var data = this.plantBatchData.filter(item=>item.batchId==val).map(item=>{
        return item
      })
      this.finishBatchData=data[0].harvestBatchVoList
      this.ruleForm.crop=data[0].crop
    },
    //收获批次选择
    finishBatchChange(val){
      var data=this.finishBatchData.filter(item=>item.harvestBatchId==val).map(item=>{
        return item
      })
      this.areaData.push(data[0].areaVo)
      this.ruleForm.areaId=data[0].areaVo.areaId
    },
    //获取详情
    getDetail(id){
      TraceSourceCommonService.traceSourceDetail(id)
      .then(res=>{
        this.traceId=res.id
        this.areaData.push(res.areaVo)
        this.ruleForm.plantId=res.batchIdVo.batchId
        this.ruleForm.finishId=res.batchIdVo.finishId
        this.ruleForm.areaId=res.areaVo.areaId
        this.ruleForm.crop=res.crop
        this.ruleForm.name=res.name
        this.ruleForm.variety=res.variety
        this.ruleForm.yield=res.yield
        this.ruleForm.listedTime=res.listedTime
        this.ruleForm.synopsis=res.synopsis
        this.irrigationList=res.irrigationList
        //宣传图片
        if(res.propagandaPics.length>0){
          this.ruleForm.litPicUrl=res.propagandaPics
          let arr=res.propagandaPics
          for(let i=0;i<arr.length;i++){
            let obj={
              url:arr[i]
            }
            this.fileList.push(obj)
          }
          this.ruleForm.photoList=this.fileList
          this.hideUpload = this.fileList.length >= 3
        }
        //生长环境
        if(res.growingEnvironmentPics.length>0){
          this.ruleForm.litPicUrlSZ=res.growingEnvironmentPics
          let arr=res.growingEnvironmentPics
          for(let i=0;i<arr.length;i++){
            let obj={
              url:arr[i]
            }
            this.fileListSZ.push(obj)
          }
          this.ruleForm.photoListSZ=this.fileListSZ
          this.hideUploadSZ = this.fileListSZ.length >= 6
        }
        //相关视频
        if(res.video){
          this.ruleForm.litPicUrlVideo.push({url:res.video,name:res.videoName})
          let obj={
            url:res.video,
            name:res.videoName,
            videoCover:res.videoCover
          }
          this.fileListVideo.push(obj)
          this.ruleForm.photoListVideo=this.fileListVideo
        }
        if(res.videoCover){
          this.ruleForm.oldVideoCover.push({url:res.videoCover})
        }
        //水质报告
        if(res.waterReportList.length>0){
          this.ruleForm.litPicUrlSZReport=res.waterReportList.map((item)=>{return {url:item.reportUrl,name:item.reportName}})
          let arr=res.waterReportList
          for(let i=0;i<arr.length;i++){
            let obj={
              url:arr[i].reportUrl,
              name:arr[i].reportName
            }
            this.fileListSZReport.push(obj)
          }
          this.ruleForm.photoListSZReport=this.fileListSZReport
        }
        //农产品检测报告
        if(res.traceSourceCertificationVo.examiningReport){
          this.ruleForm.litPicUrlNCP.push(
                  {
                    url:res.traceSourceCertificationVo.examiningReport,
                    name:res.traceSourceCertificationVo.examiningReportName
                  }
          )
          let obj={
            url:res.traceSourceCertificationVo.examiningReport,
            name:res.traceSourceCertificationVo.examiningReportName
          }
          this.fileListNCP.push(obj)
          this.ruleForm.photoListNCP=this.fileListNCP
        }
        //有机农产品检测报告
        if(res.traceSourceCertificationVo.organicCropReport){
          this.ruleForm.litPicUrlYJ.push(
                  {
                    url:res.traceSourceCertificationVo.organicCropReport,
                    name:res.traceSourceCertificationVo.organicCropReportName
                  }
          )
          let obj={
            url:res.traceSourceCertificationVo.organicCropReport,
            name:res.traceSourceCertificationVo.organicCropReportName
          }
          this.fileListYJ.push(obj)
          this.ruleForm.photoListYJ=this.fileListYJ
        }
        //绿色农产品认证
        if(res.traceSourceCertificationVo.greenCropReport){
          this.ruleForm.litPicUrlLS.push(
                  {
                    url:res.traceSourceCertificationVo.greenCropReport,
                    name:res.traceSourceCertificationVo.greenCropReportName
                  }
          )
          let obj={
            url:res.traceSourceCertificationVo.greenCropReport,
            name:res.traceSourceCertificationVo.greenCropReportName
          }
          this.fileListLS.push(obj)
          this.ruleForm.photoListLS=this.fileListLS
        }
      })
    },
    //处理上传的文件
    updatePicProperties(fileList) {
      this.ruleForm.imagesPath = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrl = fileList.filter(e => !e._type).map(e => e.url)
    },
    // 移除文件
    handleRemove(files,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 3
      this.updatePicProperties(fileList)
    },
    // 上传文件个数限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传 3 张图片`);
    },
    handleChange(file,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 3
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicProperties(fileList)
      })
    },
    //生长图片
    updatePicPropertiesSZ(fileList) {
      this.ruleForm.imagesPathSZ = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrlSZ = fileList.filter(e => !e._type).map(e => e.url)
    },
    handleRemoveSZ(files,fileList){
      this.ruleForm.photoListSZ=fileList
      this.hideUploadSZ = fileList.length >= 6
      this.updatePicPropertiesSZ(fileList)
    },
    handleExceedSZ(files, fileList) {
      this.$message.warning(`当前限制上传 6 张图片`);
    },
    handleChangeSZ(file,fileList){
      this.ruleForm.photoListSZ=fileList
      this.hideUploadSZ = fileList.length >= 6
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesSZ(fileList)
      })
    },
    //相关视频
     updatePicPropertiesVideo(fileList) {
        this.ruleForm.imagesPathVideo = fileList.filter(f => f._type).map(f => {
         return {type: f._type, content: f._content,name:f.name}
        });
        for(var i=0;i<fileList.length;i++){
          var that = this
          if(fileList[i]._type){
            let dataURL=URL.createObjectURL(fileList[i].raw)
            let video = document.createElement("video")
            video.setAttribute('crossOrigin', 'anonymous')//处理跨域
            video.setAttribute('src', dataURL)
            video.setAttribute('autoplay', 'autoplay')
            video.addEventListener('canplaythrough', function () {
              video.setAttribute('width', video.videoWidth)
              video.setAttribute('height', video.videoHeight)
              let canvas = document.createElement("canvas"),
                      width = video.width, //canvas的尺寸和图片一样
                      height = video.height
              canvas.width = width
              canvas.height = height
              canvas.getContext("2d").drawImage(video, 0, 0, width, height) //绘制canvas
              dataURL = canvas.toDataURL('image/jpeg') //转换为base64
              //转换成file
              const arr = dataURL.split(",")
              const mime = arr[0].match(/:(.*?);/)[1]
              const bstr = window.atob(arr[1])
              let n = bstr.length
              const u8arr = new Uint8Array(n)
              while (n--) {
                u8arr[n] = bstr.charCodeAt(n)
              }
              const FILE = new File([u8arr], 'cover'+Date.now()+'.jpeg', { type: mime })
              let content = dataURL.slice(dataURL.indexOf(",") + 1);
              let type = FILE.name.slice(FILE.name.lastIndexOf(".") + 1);
              var array= {type: type, content: content,name:FILE.name}
              that.ruleForm.videoCover.push(array)
            })
          }
        }
      //  console.log(this.ruleForm.videoCover)
        this.ruleForm.oldVideoCover=fileList.filter(e => !e._type).map(e => {
            return {url:e.url}
          }
        )
      //  console.log(this.ruleForm.oldVideoCover)
        this.ruleForm.litPicUrlVideo = fileList.filter(e => !e._type).map(e => {
          return {url:e.url,name:e.name}
        }
          )
    },
    handleRemoveVideo(files,fileList){
      this.ruleForm.photoListVideo=fileList
      this.updatePicPropertiesVideo(fileList)
    },
    handleExceedVideo(files, fileList) {
      this.$message.warning(`当前限制上传 1 个文件`);
    },
    handleChangeVideo(file,fileList){
      this.ruleForm.photoListVideo=fileList
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (['mp4'].indexOf( file._type) == -1) {
        this.$message({
          message: '请上传.mp4类型文件',
          type: 'warning'
        });
        //删除不正确的文件
        fileList.pop()
        return false;
      }
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesVideo(fileList)
      })
    },
    //水质报告
    updatePicPropertiesSZReport(fileList) {
      this.ruleForm.imagesPathSZReport = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrlSZReport = fileList.filter(e => !e._type).map(e =>{return {url:e.url,name:e.name}})
    },
    handleRemoveSZReport(files,fileList){
      this.ruleForm.photoListSZReport=fileList
      this.updatePicPropertiesSZReport(fileList)
    },
    handleExceedSZReport(files, fileList) {
      this.$message.warning(`当前限制上传 3 个文件`);
    },
    handleChangeSZReport(file,fileList){
      this.ruleForm.photoListSZReport=fileList
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (['pdf','png','jpg'].indexOf( file._type) == -1) {
        this.$message({
          message: '上传文件类型有误',
          type: 'warning'
        });
        //删除不正确的文件
        fileList.pop()
        return false;
      }
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesSZReport(fileList)
      })
    },
    //农产品检测报告
    updatePicPropertiesNCP(fileList) {
      this.ruleForm.imagesPathNCP = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrlNCP = fileList.filter(e => !e._type).map(e => {return {url:e.url,name:e.name}})
    },
    handleRemoveNCP(files,fileList){
      this.ruleForm.photoListNCP=fileList
      this.updatePicPropertiesNCP(fileList)
    },
    handleExceedNCP(files, fileList) {
      this.$message.warning(`当前限制上传 1 个文件`);
    },
    handleChangeNCP(file,fileList){
      this.ruleForm.photoListNCP=fileList
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (['pdf','png','jpg'].indexOf( file._type) == -1) {
        this.$message({
          message: '上传文件类型有误',
          type: 'warning'
        });
        //删除不正确的文件
        fileList.pop()
        return false;
      }
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesNCP(fileList)
      })
    },
    //有机农产品检测报告
    updatePicPropertiesYJ(fileList) {
      this.ruleForm.RepoimagesPathYJ = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrlYJ = fileList.filter(e => !e._type).map(e =>{return {url:e.url,name:e.name}})
    },
    handleRemoveYJ(files,fileList){
      this.ruleForm.photoListYJ=fileList
      this.updatePicPropertiesYJ(fileList)
    },
    handleExceedYJ(files, fileList) {
      this.$message.warning(`当前限制上传 1 个文件`);
    },
    handleChangeYJ(file,fileList){
      this.ruleForm.photoListYJ=fileList
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (['pdf','png','jpg'].indexOf( file._type) == -1) {
        this.$message({
          message: '上传文件类型有误',
          type: 'warning'
        });
        //删除不正确的文件
        fileList.pop()
        return false;
      }
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesYJ(fileList)
      })
    },
    //绿色农产品检测报告
    updatePicPropertiesLS(fileList) {
      this.ruleForm.RepoimagesPathLS = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrlLS = fileList.filter(e => !e._type).map(e => {return {url:e.url,name:e.name}})
    },
    handleRemoveLS(files,fileList){
      this.ruleForm.photoListLS=fileList
      this.updatePicPropertiesLS(fileList)
    },
    handleExceedLS(files, fileList) {
      this.$message.warning(`当前限制上传 1 个文件`);
    },
    handleChangeLS(file,fileList){
      this.ruleForm.photoListLS=fileList
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      if (['pdf','png','jpg'].indexOf( file._type) == -1) {
        this.$message({
          message: '上传文件类型有误',
          type: 'warning'
        });
        //删除不正确的文件
        fileList.pop()
        return false;
      }
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicPropertiesLS(fileList)
      })
    },
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        if (file) {
          reader.readAsDataURL(file);
        } else {

        }
        reader.onload = function() {
          imgResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(imgResult);
        };
      });
    },
    //灌溉数据添加
    dataAdd(){
      var obj={
        showTime:'',
        value:0,
      }
      this.irrigationList.push(obj)
    },
    //灌溉数据删除
    dataDelete(index){
      this.irrigationList.splice(index,1)
    },
    //提交
    submit(formName){
      var that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          // 认证文件信息
          var rZFiles={}
          rZFiles.agricultural=that.ruleForm.imagesPathNCP[0]
          rZFiles.green=that.ruleForm.imagesPathLS[0]
          rZFiles.organic=that.ruleForm.imagesPathYJ[0]
          //灌溉数据信息
          if(this.irrigationList.length>0){
            for(var i=0;i<this.irrigationList.length;i++){
              if(this.irrigationList[i].showTime==''){
                this.irrigationList.splice(i,1)
              }
            }
          }
          if(this.traceId){
            //编辑
            var params1={
              id:that.traceId,
              batchId:that.ruleForm.plantId,
              finishId:that.ruleForm.finishId,
              areaId:that.ruleForm.areaId,
              crop:that.ruleForm.crop,
              name:that.ruleForm.name,
              variety:that.ruleForm.variety,
              yield:that.ruleForm.yield,
              listedTime:that.ruleForm.listedTime,
              synopsis:that.ruleForm.synopsis,
              propagandaPics:that.ruleForm.imagesPath,
              oldPropagandaPics:that.ruleForm.litPicUrl,
              growingEnvironmentPics:that.ruleForm.imagesPathSZ,
              oldGrowingEnvironmentPics:that.ruleForm.litPicUrlSZ,
              video:that.ruleForm.imagesPathVideo[0],
              videoCover:that.ruleForm.videoCover[0],
              oldVideo:that.ruleForm.litPicUrlVideo[0],
              oldVideoCover:that.ruleForm.oldVideoCover[0],
              waterReportList:that.ruleForm.imagesPathSZReport,
              oldWaterPics:that.ruleForm.litPicUrlSZReport,
              traceSourceCertificationPo:rZFiles,
              oldAgricultural:that.ruleForm.litPicUrlNCP[0],
              oldOrganic:that.ruleForm.litPicUrlYJ[0],
              oldGreen:that.ruleForm.litPicUrlLS[0],
              irrigationList:that.irrigationList
            }
            TraceSourceCommonService.changeTraceSourceInformation(params1)
            .then(()=>{
              // console.log(res)
              that.$message({
                message:'提交成功',
                type:'success'
              })
              that.cancel()
            })
          }else{
            //添加
            var params={
              batchId:that.ruleForm.plantId,
              finishId:that.ruleForm.finishId,
              areaId:that.ruleForm.areaId,
              crop:that.ruleForm.crop,
              name:that.ruleForm.name,
              variety:that.ruleForm.variety,
              yield:that.ruleForm.yield,
              listedTime:that.ruleForm.listedTime,
              synopsis:that.ruleForm.synopsis,
              propagandaPics:that.ruleForm.imagesPath,
              growingEnvironmentPics:that.ruleForm.imagesPathSZ,
              video:that.ruleForm.imagesPathVideo[0],
              videoCover:that.ruleForm.videoCover[0],
              waterReportList:that.ruleForm.imagesPathSZReport,
              traceSourceCertificationPo:rZFiles,
              irrigationList:that.irrigationList,
            }
            TraceSourceCommonService.traceSourceBuild(params)
            .then(()=>{
              // console.log(res)
              that.$message({
                message:'提交成功',
                type:'success'
              })
              that.cancel()
            })
          }
        }
      })
    },
    //取消
    cancel(){
      var that=this
      that.dialogVisible=false
      Object.assign(that.$data,that.$options.data())
    }
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/traceRecordAddDialog.less';
</style>
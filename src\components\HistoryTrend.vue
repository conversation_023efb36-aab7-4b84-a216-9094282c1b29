<template>
    <div class="history-trend">
        <div class="legend">
            <div class="legend-item">
                <span class="legend-line temperature"></span>
                <span class="legend-text ">温度</span>
            </div>
            <div class="legend-item">
                <span class="legend-line humidity"></span>
                <span class="legend-text ">湿度</span>
            </div>
            <div class="legend-item">
                <span class="legend-line light"></span>
                <span class="legend-text ">光照强度</span>
            </div>
        </div>
        <div class="chart-container" ref="chartContainer"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
    name: 'HistoryTrend',
    props: {
        chartData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            chart: null,
            defaultChartData: {},
            tooltipTimer: null, // 轮播定时器
            tooltipDelay: 2000 // 轮播间隔时间，单位毫秒
        }
    },
    computed: {
        // 使用传入的数据或默认数据
        displayChartData() {
            return Object.keys(this.chartData).length > 0 ? this.chartData : this.defaultChartData;
        },
        // 获取数据点的总数
        dataPointCount() {
            return this.displayChartData.dates ? this.displayChartData.dates.length : 0;
        }
    },
    watch: {
        // 监听传入数据的变化，更新图表
        chartData: {
            handler(newVal) {
                if (newVal && Object.keys(newVal).length > 0) {
                    this.updateChart();
                }
            },
            deep: true
        }
    },
    mounted() {
        this.initChart()
        window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
        // 清除定时器
        if (this.tooltipTimer) {
            clearInterval(this.tooltipTimer);
        }
        
        if (this.chart) {
            this.chart.dispose()
        }
        window.removeEventListener('resize', this.handleResize)
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$refs.chartContainer)
            this.updateChart()
            
            // 初始化后启动tooltip轮播
            this.$nextTick(this.startTooltipAutoplay)
        },
        updateChart() {
            if (!this.chart) return;
            
            const option = this.getChartOption()
            this.chart.setOption(option, true) // 使用true参数清除之前的配置
            
            // 数据更新后重新启动轮播
            this.restartTooltipAutoplay()
        },
        getChartOption() {
            return {
                grid: {
                    left: 20,
                    right: 20,
                    bottom: 30,
                    top: 20,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: this.displayChartData.dates,
                    axisLine: {
                        lineStyle: {
                            color: '#4CCAEE'
                        }
                    },
                    axisLabel: {
                        color: '#DFEEF3',
                        interval:0,
                        fontSize: 12,
                        margin: 8
                    },
                    axisTick: {
                        show: false
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        position: 'left',
                        min: 0,
                        max: 40,
                        interval: 10,
                        axisLine: { show: false },
                        axisTick: { show: false },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(76, 202, 238, 0.2)',
                                type: 'dashed'
                            }
                        }
                    },
                    
                    {
                        type: 'value',
                        position: 'right',
                        min: 0,
                        max: 800,
                        interval: 200,
                        axisLine: { show: false },
                        axisTick: { show: false },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12
                        },
                        splitLine: { show: false }
                    }
                ],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'line',
                        animation: true
                    },
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: '#4CCAEE',
                    borderWidth: 1,
                    textStyle: { fontSize: 14 },
                    formatter: function(params) {
                        let result = '<div style="width: 120px;">'
                        params.forEach(param => {
                            let unit = ''
                            let color = ''
                            if (param.seriesName === '温度') {
                                unit = '°C'
                                color = '#FF46A1'
                            } else if (param.seriesName === '湿度') {
                                unit = '%'
                                color = '#4CCAEE'
                            } else if (param.seriesName === '光照强度') {
                                unit = 'lx'
                                color = '#E9A31F'
                            }
                            result += `<div style="display: flex; justify-content: space-between; color:${color}">
                                <span style="text-align: left;">${param.seriesName}</span>
                                <span style="text-align: right; font-weight: bold;">${param.value}${unit}</span>
                            </div>`
                        })
                        result += '</div>'
                        return result
                    }
                },
                legend: { show: false },
                series: [
                    {
                        name: '温度',
                        type: 'line',
                        yAxisIndex: 0,
                        data: this.displayChartData.temperature,
                        lineStyle: { color: '#FF46A1', width: 2 },
                        itemStyle: { color: '#FF46A1' },
                        symbol: 'none',
                        symbolSize: 6,
                        smooth: false
                    },
                    {
                        name: '湿度', 
                        type: 'line',
                        yAxisIndex: 1,
                        data: this.displayChartData.humidity,
                        lineStyle: { color: '#4CCAEE', width: 2 },
                        itemStyle: { color: '#4CCAEE' },
                        symbol: 'none',
                        symbolSize: 6,
                        smooth: false
                    },
                    {
                        name: '光照强度',
                        type: 'line', 
                        yAxisIndex: 1,
                        data: this.displayChartData.lightIntensity,
                        lineStyle: { color: '#E9A31F', width: 2 },
                        itemStyle: { color: '#E9A31F' },
                        symbol: 'none',
                        symbolSize: 6,
                        smooth: false
                    }
                ]
            };
        },
        handleResize() {
            if (this.chart) {
                this.chart.resize()
            }
        },
        
        // 启动tooltip自动轮播
        startTooltipAutoplay() {
            // 清除可能存在的定时器
            if (this.tooltipTimer) {
                clearInterval(this.tooltipTimer);
            }
            
            if (!this.chart || this.dataPointCount === 0) return;
            
            let currentIndex = -1;
            this.tooltipTimer = setInterval(() => {
                // 循环显示每个点的数据
                currentIndex = (currentIndex + 1) % this.dataPointCount;
                
                // 使用ECharts API显示提示框
                this.chart.dispatchAction({
                    type: 'showTip',
                    seriesIndex: 0,
                    dataIndex: currentIndex
                });
            }, this.tooltipDelay);
        },
        
        // 重新启动轮播（数据变化时调用）
        restartTooltipAutoplay() {
            // 延迟执行，确保图表已更新
            this.$nextTick(this.startTooltipAutoplay);
        }
    }
}
</script>

<style lang="less" scoped>
.history-trend {
    width: 100%;
    height: 100%;
    padding: 5px;
    
    .legend {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 25px;
        gap: 20px;
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            
            .legend-line {
                width: 16px;
                height: 2px;
                
                &.temperature {
                    background-color: #FF46A1;
                }
                
                &.humidity {
                    background-color: #4CCAEE;
                }
                
                &.light {
                    background-color: #E9A31F;
                }
            }
            
            .legend-text {
                font-size: 12px;
                font-weight: 400;
                color: #fff;
                
                &.temperature {
                    color: #FF46A1;
                }
                
                &.humidity {
                    color: #4CCAEE;
                }
                
                &.light {
                    color: #E9A31F;
                }
            }
        }
    }
    
    .chart-container {
        width: 100%;
        height: calc(100% - 25px);
        min-height: 240px;
    }
}
</style>

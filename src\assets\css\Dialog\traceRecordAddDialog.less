.traceRecordAddDialog{
  .el-dialog{
    height: 1006px;
    margin-top: 38px !important;
    .el-dialog__body{
      height: 908px;
      .line{
        width: 580px;
        margin: auto;
      }
      .addFrom{
        width: 703px;
        height: 842px;
        overflow-y: scroll;
        margin-top: 20px;
        &_title{
          width: 100%;
          margin-bottom: 10px;
          display: flex;
          &_icon{
            width: 18px;
            height: 18px;
          }
          &_text{
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            line-height: 20px;
            margin-left: 10px;
          }
        }
        .el-form-item{
          margin-bottom: 16px;
          .uploadStyle{
            width: 100%;
            display: flex;
          }
          .el-form-item__label{
            width: 160px!important;
            font-size: 14px;
            font-weight: 400;
            color: #FFFFFF;
          }
          .el-form-item__content{
            margin-left: 160px!important;
          }
          .el-form-item__content{

          }
          .el-select{
            .el-input__icon {
              line-height: 32px;
            }
          }

        }
        .form_select{
          .el-form-item__content{
            width: 440px;
            height: 32px;
          }
        }
        .form_upload{
          .el-form-item__content{
            width: 440px;
            //height: 60px;
            .el-input{
              width: 440px!important;
              height: 32px!important;
            }
          }
        }
        .form_textArea{
          .el-form-item__content{
            width: 440px;
            height: 118px;
            //.el-input{
            //  width: 440px!important;
            //  height: 118px!important;
            //}
          }
        }
        .form_item2{
          &_list{
            margin-top: 16px;
            display: flex;
            align-items: center;
            &_date{
              width: 180px;
              height: 32px;
            }
            &_input{
              width: 180px;
              height: 32px;
              margin-left: 22px;
            }
            &_unit{
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 32px;
              margin-left: 11px;
            }
            &_delete{
              width: 20px;
              height: 20px;
              margin-left: 11px;
            }
          }
          &_list:first-child{
            margin-top: 0;
          }
          &_add{
            width: 20px;
            height: 20px;
          }
        }
      }
      .btnBox{
        height: 75px;
        display: flex;
        justify-content: center;
        align-items: center;
        .submit_button{
          width: 120px;
          .el-button{
            width: 120px;
            height: 42px;
          }
        }
        .submit_button:nth-child(1){
          margin-right: 60px;
        }
      }
    }
  }
}
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const NetMonitorService = {
    /**
     * 悬浮窗设备列表
     * @param {*} areaId 区域id ,为空表示所有
     * @returns Promise 
     */
    'netMonitorList': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'cfca1980d1c8b67ad0705e4ce59276ed9aacd6fc', areaId)
            : execute(concreteModuleName, `/NetMonitorService/netMonitorList`, 'json', 'POST', { areaId });
    }, 
    /**
     * 悬浮窗设备详情
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'netMonitorEqDetail': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd9136e62f1d5febe8e5ee5fc291f06878ecd2756', equipmentId)
            : execute(concreteModuleName, `/NetMonitorService/netMonitorEqDetail`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取地图上设备点信息
     * @param {*} areaId 区域id ,为空表示所有
     * @returns Promise 
     */
    'findEqInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0a33f9a3bac48f15c8fbc9e9cf8c4bc73356ec94', areaId)
            : execute(concreteModuleName, `/NetMonitorService/findEqInfo`, 'json', 'POST', { areaId });
    }
}

export default NetMonitorService

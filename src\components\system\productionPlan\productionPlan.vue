<template>
  <div class="productionPlan systemDialogStyle">
    <div class="productionPlan_tabList">
      <div class="tabList_item" v-for="(item,index) in tabList" :key="index">
        <div :class="{active:tabActive==item.areaId}" @click="tabClick(item)">
          {{item.areaName}}
        </div>
      </div>
    </div>
    <div class="productionPlan_con">
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="planTypeId" clearable placeholder="请选择计划类型">
            <el-option
              v-for="item in planTypeData"
              :key="item.value"
              :label="item.key"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
                  v-model="time"
                  :clearable="false"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="timeChange"
          >
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2" @click="searchData()">
          <el-button>查询</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2" @click="exportData()">
          <el-button>下载</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="addPlan">新增计划</el-button>
        </div>
      </div>
      <div class="containerBox">
        <div class="containerBox_left">
          <div class="tableHeader">
            <div>计划日期：{{startTime}}~{{endTime}}</div>
            <div>今日时间：{{today}}</div>
          </div>
          <div class="tableBox systemTableStyle">
            <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                    height="100%">
              <el-table-column
                      type="index"
                      label="序号"
                      align="center"
                      width="60">
              </el-table-column>
              <el-table-column
                      prop="type"
                      align="center"
                      label="计划类型">
                <template slot-scope="scope">
                  <div class="tableActive_text" @click="planInfoDialogOpen(scope.row.planId)">{{scope.row.planTypeId | productionPlanTypeFormat}}</div>
                </template>
              </el-table-column>
              <el-table-column
                      align="center"
                      label="状态">
                <template slot-scope="scope">
                  <div>{{scope.row.state | productionPlanFormat}}</div>
                </template>
              </el-table-column>
              <el-table-column
                      align="center"
                      label="区域数量">
                <template slot-scope="scope">
                  <div >
                    <el-tooltip popper-class="tooltipSystemStyle" placement="top" effect="light">
                      <div slot="content">
                        <div class="item">
                          区域
                        </div>
                        <br/>
                        <div class="item2">
                          <div class="item2_list" v-for="(item,index) in scope.row.areaNameList" :key="index">
                            {{item}}
                          </div>
                        </div>
                        <br/>
                      </div>
                      <div class="tableActive_text">{{scope.row.areaNum}}</div>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                      prop="startTime"
                      align="center"
                      label="开始时间">
              </el-table-column>
              <el-table-column
                      prop="planDay"
                      align="center"
                      label="计划天数">
              </el-table-column>
              <el-table-column
                      prop="endTime"
                      align="center"
                      label="结束时间">
              </el-table-column>
              <el-table-column
                      align="center"
                      label="操作">
                <template slot-scope="scope">
                  <el-button @click="del(scope.row)" v-if="scope.row.isDelete" type="text" size="small">删除</el-button>
                  <el-button type="text" size="small" v-if="scope.row.isUpdate" @click="editHandle(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pageBox systemPageChange">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
            </el-pagination>
          </div>
        </div>
        <div class="containerBox_right">
          <div class="planInfo">
            <div class="planInfo_header">
              <span class="planInfo_header_text1">本周相关计划</span>
              <span class="planInfo_header_text2">{{currentWeekStartTime}} 至 {{currentWeekEndTime}}</span>
            </div>
            <div class="planInfo_container">
              <div class="planCardNodata" v-if="currentWeekPlanData.length==0">
                本周暂无计划，<span class="nodataActive" @click="addPlan">去制定~</span>
              </div>
              <div v-else class="planCard" v-for="(item,index) in currentWeekPlanData" :key="index" @click="planInfoDialogOpen(item.id)">
                <div class="planCard_item1">
                  <div class="planCard_item1_text1">
                    <span style="margin-right: 4px" v-for="(Item,Index) in item.areaNameList" :key="Index">{{Item}}</span>
                  </div>
                  <div class="planCard_item1_text2" :class="{'planCard_item1_text3':item.state==2}">{{item.state | productionPlanFormat}}</div>
                </div>
                <div class="planCard_item2">
                  <div class="planCard_item2_text1">{{item.title}}</div>
                  <div class="planCard_item2_text2">{{item.startTime}} 至 {{item.endTime}}</div>
                </div>
                <div class="planCard_item3">
                  {{item.detailedInformation}}
                </div>
                <div class="planCard_item4">
                  <div class="planCard_item4_image" v-for="(picItem,picIndex) in item.picList" :key="picIndex">
                    <img :src=picItem alt="">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="addPlanDialog">
      <el-dialog
              :title="title"
              v-if="addDialog"
              :visible.sync="addDialog"
              width="23%"
              center
              :modal-append-to-body="false"
              :show-close="false"
              :close-on-click-modal="false"
              :close-on-press-escape="false">
        <div class="close" @click="addCancel()">
          <img src="../../../assets/image/managementSystem/close.png" alt="">
        </div>
        <div class="formBox">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px" label-position="top">
            <el-form-item label="工作标题" prop="title">
              <el-input class="systemFormStyle" clearable placeholder="请输入工作标题" v-model="ruleForm.title"></el-input>
            </el-form-item>
            <el-form-item label="起止时间" prop="time">
              <div class="systemFormStyle">
                <el-date-picker
                        v-model="ruleForm.time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item label="生产计划类型" prop="planTypeId">
              <div class="systemFormStyle">
                <el-select v-model="ruleForm.planTypeId" placeholder="请选择生产计划类型">
                  <el-option
                          v-for="item in planTypeData"
                          :key="item.value"
                          :label="item.key"
                          :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="选择区域" prop="areaIdList">
              <div class="">
                <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
<!--                <div style="margin: 15px 0;"></div>-->
                <el-checkbox-group v-model="ruleForm.areaIdList" @change="handleCheckedAreaChange">
                  <el-checkbox v-for="(item,index) in areaData" :label="item.areaId" :key="index">{{item.areaName}}</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            <el-form-item label="详细内容" prop="detailedInformation">
              <div class="textAreaInput systemFormStyle">
                <el-input class="systemFormStyle" type="textarea" clearable placeholder="请输入工作详细计划内容" v-model="ruleForm.detailedInformation"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="上传图片">
              <div class="uploadBox systemFormStyle">
<!--                <el-upload-->
<!--                        class="upload-demo"-->
<!--                        drag-->
<!--                        action="https://jsonplaceholder.typicode.com/posts/"-->
<!--                        :file-list="fileList"-->
<!--                        :limit="6"-->
<!--                        :on-exceed="handleExceed"-->
<!--                        :on-change="handleChange"-->
<!--                        :auto-upload="false"-->
<!--                        :on-remove="handleRemove"-->
<!--                       >-->
<!--                  <i class="el-icon-upload"></i>-->
<!--                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
<!--                </el-upload>-->
                <el-upload
                        class="uploadCard125"
                        :class="{hide:hideUpload}"
                        action=""
                        list-type="picture-card"
                        :file-list="fileList"
                        :limit="6"
                        :on-exceed="handleExceed"
                        :on-change="handleChange"
                        :auto-upload="false"
                        :on-remove="handleRemove">
                  <img src="../../../assets/image/centralControlPlatform/upload-add2.png" alt="">
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="btnBox">
          <div class="btnItem systemResetButtonStyle2" @click="addCancel()">
            <el-button>取消</el-button>
          </div>
          <div class="btnItem systemSearchButtonStyle2" @click="addSubmit('ruleForm')">
            <el-button >确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="planInfoDialog">
      <el-dialog
              :title=planDetail.title
              :visible.sync="planInfoDialog"
              width="28%"
              center
              :modal-append-to-body="false"
              :show-close="false"
              :close-on-click-modal="false"
              :close-on-press-escape="false">
        <div class="close" @click="planInfoDialog=false">
          <img src="../../../assets/image/managementSystem/close.png" alt="">
        </div>
        <div class="infoContainer">
          <div class="infoContainer_con">
            {{planDetail.detailedInformation}}
          </div>
          <el-divider></el-divider>
          <div class="infoContainer_imageList">
            <img v-for="(item,index) in planDetail.picList" :key="index" class="infoContainer_imageList_imageItem" :src=item alt="">
          </div>
          <div class="infoContainer_finished" v-if="planDetail.finishAreaId&&planDetail.finishAreaId.length>0">
            <div class="infoContainer_finished_header">
              已完成区域：
            </div>
            <div class="infoContainer_finished_list" v-for="(Item,Index) in planDetail.finishAreaId" :key="Index">
              <div class="listText">{{Item.areaName}}</div>
              <div class="listText">完成时间{{Item.accomplishTime}}</div>
            </div>
          </div>
        </div>
        <div class="btnBox" v-if="submitAreaData&&submitAreaData.length>0">
          <div class="btnItem systemSearchButtonStyle2" @click="planFinishOpen(planDetail)">
            <el-button>提交完成</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="planSubmitDialog">
      <el-dialog
              title="提交完成"
              v-if="planSubmitDialog"
              :visible.sync="planSubmitDialog"
              width="23%"
              center
              :modal-append-to-body="false"
              :show-close="false"
              :close-on-click-modal="false"
              :close-on-press-escape="false">
        <div class="close" @click="planFinishCancel()">
          <img src="../../../assets/image/managementSystem/close.png" alt="">
        </div>
        <div class="formBox">
          <el-form :model="ruleForm2" :rules="rules2" ref="ruleForm2" label-width="80px" label-position="top">
            <el-form-item label="汇报人" prop="name">
              <el-input class="systemFormStyle" readonly v-model="nickName"></el-input>
            </el-form-item>
            <el-form-item label="完成时间" prop="name">
              <el-input class="systemFormStyle" readonly v-model="finishTime"></el-input>
            </el-form-item>
            <el-form-item label="完成区域" prop="areaId">
              <div class="systemFormStyle">
                <el-select v-model="ruleForm2.areaId" placeholder="请选择完成区域">
                  <el-option
                          v-for="item in submitAreaData"
                          :key="item.areaId"
                          :label="item.areaName"
                          :value="item.areaId">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="施药种类" v-if="submitPlanType==1">
              <img src="../../../assets/image/eppoWisdom/tianjia.png" alt="" class="formAddIcon" @click="manureAdd()">
              <div class="systemFormStyle formArray" v-for="(item,index) in ruleForm2.pesticides" :key="index">
                <el-input class="systemFormStyle" clearable placeholder="请输入施药种类" v-model="ruleForm2.pesticides[index]"></el-input>
                <img src="../../../assets/image/eppoWisdom/jianshao.png" alt="" style="margin-left: 1%" @click="manureDel(index)">
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="btnBox">
          <div class="btnItem systemResetButtonStyle2" @click="planFinishCancel()">
            <el-button>取消</el-button>
          </div>
          <div class="btnItem systemSearchButtonStyle2" @click="planFinishSubmit('ruleForm2')">
            <el-button >确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="delDialog">
      <el-dialog
              title="提示"
              :visible.sync="delDialog"
              width="16%"
              center
              :modal-append-to-body="false"
              :show-close="false"
              :close-on-click-modal="false"
              :close-on-press-escape="false">
        <div class="close" @click="delDialog=false">
          <img src="../../../assets/image/managementSystem/close.png" alt="">
        </div>
        <div class="text">
          <div>确定删除这条数据？</div>
        </div>
        <div class="btnBox">
          <div class="btnItem systemResetButtonStyle2" @click="delDialog=false">
            <el-button>取消</el-button>
          </div>
          <div class="btnItem systemSearchButtonStyle2" @click="delSubmit()">
            <el-button>确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import PlanService from '../../../jaxrs/concrete/com.zny.ia.api.PlanService'
  import planType from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$生产计划类型'
  import CommonService from "../../../jaxrs/concrete/com.zny.ia.api.CommonService";
  import {gettime} from "../../../js/getTime";
export default {
  name:'productionPlan',
  data(){
    return{
      administrators:"admin",//管理员：admin->总,subAdmin->分管理员
      tabActive:null,
      tabList:[],
      tableData: [],
      areaData:[],
      today:'',
      time:'',
      startTime: '',
      endTime:'',
      planTypeId:"",
      planTypeData:planType._toArray(),
      currentWeekStartTime:'',
      currentWeekEndTime:'',
      currentWeekPlanData:[],
      planId:'',
      // 分页
      pageSize:10,
      currentPage:1,
      total:0,
      pageCount:1,
      // 新增生产计划弹窗
      title:"",
      addDialog:false,
      checkAll: false,
      isIndeterminate: true,
      ruleForm:{
        title:'',
        time:[],
        startTime: '',
        endTime:'',
        planTypeId:null,
        areaIdList:[],
        detailedInformation:'',
        imagesPath:[],
        litPicUrl:"",
        photoList:[],
      },
      rules:{
        title: [
          { required: true, message: '请输入工作标题', trigger: 'blur' },
        ],
        time: [
          { type: 'array', required: true, message: '请选择起止时间', trigger: 'change' }
        ],
        planTypeId: [
          { required: true, message: '请选择生产计划类型', trigger: 'change' },
        ],
        areaIdList: [
          { type: 'array', required: true, message: '请选择区域', trigger: 'change' }
        ],
        detailedInformation: [
          { required: true, message: '请输入工作标题', trigger: 'blur' },
        ],
      },
      fileList:[],
      hideUpload:false,
      //计划详情
      planInfoDialog:false,
      planDetail:[],
      //删除弹窗
      delDialog:false,
      //提交完成
      planSubmitDialog:false,
      submitPlanType:'',
      submitAreaData:[],
      ruleForm2:{
        areaId:'',
        pesticides:[],
      },
      rules2:{
        areaId: [
          { required: true, message: '请选择完成区域', trigger: 'change' },
        ],
      },
      nickName:localStorage.getItem('nickname'),
      finishTime:gettime.currentdate(),
      //导出数据
      excelData:[]
    }
  },
  mounted(){
    this.getAreaData()
    this.today=gettime.currentMonthDay()
    this.startTime=gettime.getHalfMonthBefore()
    this.endTime=gettime.getHalfMonthAfter()
    this.currentWeekStartTime=gettime.getMonday('s',-1)
    this.currentWeekEndTime=gettime.getMonday('e',-1)
    this.getCurrentWeekPlanData()
  },
  methods:{
    //获取当前用户下的所有区域信息
    getAreaData(){
      CommonService.allAreaOfCurrentUserManage().then(res => {
        if(this.administrators=='admin'){
          this.areaData=[...res]
          this.tabList=[...res]
          var obj={}
          obj.areaId=null
          obj.areaName='基地总览'
          this.tabList.unshift(obj)
        }else {
          this.tabList = res
        }
        this.tabActive=this.tabList[0].areaId
        this.forList()
      })
    },
    // tab 列表点击
    tabClick(item){
      this.tabActive=item.areaId
      this.forList()
    },
    //查询
    searchData(){
      this.currentPage=1
      this.startTime=this.time[0]
      this.endTime=this.time[1]
      this.forList()
    },
    //获取列表
    forList(){
      var params={
        pageSize:this.pageSize,
        num:this.currentPage,
        condition:{
          areaId:this.tabActive,
          planTypeId:this.planTypeId.toString()==""?null:this.planTypeId,
          startTime:this.time[0],
          endTime:this.time[1]
        }
      }
      PlanService.listPlan(params)
      .then((res)=>{
        this.tableData=res.list
        this.pageCount=res.count
        this.total=res.total
      })
    },
    //获取本周相关计划
    getCurrentWeekPlanData(){
      var param={
        startTime:this.currentWeekStartTime,
        endTime:this.currentWeekEndTime,
      }
      PlanService.correlationPlan(param)
      .then(res=>{
        this.currentWeekPlanData=res
      })
    },
    //时间改变
    timeChange(val){
      if(val==null||val==undefined||val==""){
        this.time=[]
      }else{
        var arr=[]
        for(var i=0;i<val.length;i++){
          var y = val[i].getFullYear();
          var m = val[i].getMonth() + 1;
          m = m < 10 ? ('0' + m) : m;
          var d = val[i].getDate();
          d = d < 10 ? ('0' + d) : d;
          var time= y+'-'+m+'-'+d;
          arr.push(time)
        }
        this.time=arr
      }
    },
    //获取计划详情
    getPlanDetail(){
      PlanService.singlePlan(this.planId)
      .then(res=>{
        this.planDetail=res
      })
    },
    // 生成种植批次
    addPlan(){
      this.title="新增生产计划"
      this.planId=''
      this.addDialog=true
      this.getAreaData()
    },
    //处理上传的文件
    updatePicProperties(fileList) {
      this.ruleForm.imagesPath = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content,name:f.name}
      });
      this.ruleForm.litPicUrl = fileList.filter(e => !e._type).map(e => e.url)
    },
    // 移除文件
    handleRemove(files,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 6
      this.updatePicProperties(fileList)
    },
    // 上传文件个数限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传 6 个文件`);
    },
    handleChange(file,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 6
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicProperties(fileList)
      })
    },
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        if (file) {
          reader.readAsDataURL(file);
        } else {

        }
        reader.onload = function() {
          imgResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(imgResult);
        };
      });
    },
    //区域全选
    handleCheckAllChange(val) {
      this.ruleForm.areaIdList = val ? this.areaData.map(e=> e.areaId) : [];
      this.isIndeterminate = false;
    },
    //区域选择
    handleCheckedAreaChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.areaData.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.areaData.length;
    },
    //提交
    addSubmit(formName){
      var that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.planId==""){
            var params={
              title:this.ruleForm.title,
              planTypeId:this.ruleForm.planTypeId,
              startTime:this.ruleForm.time[0],
              endTime:this.ruleForm.time[1],
              areaIdList:this.ruleForm.areaIdList,
              detailedInformation:this.ruleForm.detailedInformation,
              picList:this.ruleForm.imagesPath,
            }
            PlanService.addPlan(params)
                    .then(()=>{
                      that.$message({
                        message:'提交成功！',
                        type:'success'
                      })
                      that.addCancel()
                      that.forList()
                    })
          }else{
            var params2={
              planId:this.planId,
              title:this.ruleForm.title,
              planTypeId:this.ruleForm.planTypeId,
              startTime:this.ruleForm.time[0],
              endTime:this.ruleForm.time[1],
              areaIdList:this.ruleForm.areaIdList,
              detailedInformation:this.ruleForm.detailedInformation,
              picList:this.ruleForm.imagesPath,
              oldList:this.ruleForm.litPicUrl
            }
            PlanService.updatePlan(params2)
                    .then(()=>{
                      that.$message({
                        message:'提交成功！',
                        type:'success'
                      })
                      that.addCancel()
                      that.forList()
                    })
          }

        }
      })
    },
    //添加弹窗关闭
    addCancel(){
      this.$refs['ruleForm'].resetFields();
      this.ruleForm.imagesPath=[]
      this.ruleForm.litPicUrl=[]
      this.ruleForm.photoList=[]
      this.fileList=[]
      this.addDialog=false
    },
    //编辑
    editHandle(row){
      this.planId=row.planId
      this.addDialog=true
      this.title='编辑生产计划'
      this.getAreaData()
      PlanService.detailPlan(this.planId)
              .then(res=>{
                this.ruleForm.title=res.title
                this.ruleForm.planTypeId=res.planType
                this.ruleForm.time=[res.startTime,res.endTime]
                this.ruleForm.areaIdList=res.areaIdList
                this.ruleForm.detailedInformation=res.detailedInformation
                if(res.picList.length>0){
                  this.ruleForm.litPicUrl=res.picList
                  let arr=res.picList
                  for(let i=0;i<arr.length;i++){
                    let obj={
                      url:arr[i],
                      name:arr[i].substring(arr[i].lastIndexOf("/")+1)
                    }
                    this.fileList.push(obj)
                  }
                  this.ruleForm.photoList=this.fileList
                  this.hideUpload = this.fileList.length >= 6
                }
              })
    },
    //获取计划详情
    planInfoDialogOpen(planId){
      this.planId=planId
      this.planInfoDialog=true
      this.getSubmitAreaData()
      this.getPlanDetail()
    },
    // 大屏植保点击去制定
    openAreaDetails() {
      if (this.$route.query.type) {
        this.addDialog = true
      }
    },
    //删除弹窗
    del(row) {
      this.planId=row.planId
      this.delDialog = true
    },
    //确定删除
    delSubmit(){
      PlanService.deletePlan(this.planId)
      .then(()=>{
        this.$message({
          message:'删除成功！',
          type:'success'
        })
        this.delDialog=false
        this.planId=''
      })
    },
    //提交完成弹窗
    planFinishOpen(item){
      this.planId=item.planId
      this.submitPlanType=item.planType
      this.planSubmitDialog=true
    },
    //获取待完成区域下拉选
    getSubmitAreaData(){
      CommonService.listWaitAreas(this.planId)
      .then(res=>{
        this.submitAreaData=res
      })
    },
    //农药添加
    manureAdd(){
      this.ruleForm2.pesticides.push('')
    },
    //农药删除
    manureDel(index){
      this.ruleForm2.pesticides.splice(index,1)
    },
    //提交完成--提交
    planFinishSubmit(formName){
      for(var i=0;i<this.ruleForm2.pesticides.length;i++){
        if(this.ruleForm2.pesticides[i]==""){
          this.ruleForm2.pesticides.splice(i,1)
        }
      }
      var that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          var params={
            areaId:that.ruleForm2.areaId,
            pesticides:that.ruleForm2.pesticides,
            planId:that.planId
          }
          PlanService.finishPlan(params)
                  .then(()=>{
                    that.$message({
                      message:'提交完成！',
                      type:'success'
                    })
                    that.planFinishCancel()
                    that.planInfoDialog=false
                  })
        }
      })
    },
    //提交完成--取消
    planFinishCancel(){
      this.ruleForm2.pesticides=[]
      this.ruleForm2.areaId=''
      this.planSubmitDialog=false
      this.submitPlanType=""
    },
    handleSizeChange(){

    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.forList()
    },
    //导出
    exportData(){
      var param={
        areaId:this.tabActive,
        planTypeId:this.planTypeId.toString()==""?null:this.planTypeId,
        startTime:this.startTime,
        endTime:this.endTime
      }
      PlanService.downloadPlan(param)
      .then((res)=>{
        this.excelData=res
        this.excelData=this.excelData.map((item)=>{
          item.time=item.startTime+'~'+item.endTime
          item.planType=planType._lableOf(item.planType)
          return item
        })
        this.export2Excel()
      })
    },
    //表格数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const {
          export_json_to_excel_productPlan
        } = require("@/excel/Export2Excel.js");
        var tHeader=[]
        var filterVal = []
        tHeader = ["计划类型","计划标题","计划起止时间","区域","详细内容","完成时间","施肥种类（植保）","汇报人员"]
        filterVal = [
          "planType",
          "title",
          "time",
          "areaName",
          "detailedInformation",
          "accomplishTime",
          "pesticides",
          "reportName",
        ];
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        export_json_to_excel_productPlan(tHeader, data, "生产计划数据表");
      });
    },
    //格式转换，直接复制即可,不需要修改什么
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
  },
}
</script>
<style lang="less">
@import '../../../assets/css/system/productionPlan.less';
@media screen and (min-width: 1280px) and (max-width: 1680px){
  .productionPlan{
    .addPlanDialog{
      .el-dialog{
        width:30% !important;
      }
    }
    .planInfoDialog{
      .el-dialog{
        width: 29% !important;
      }
    }
  } 
}
@media screen and (max-width: 1280px){
  .productionPlan{
    .addPlanDialog{
      .el-dialog{
        width: 35% !important;
      }
    }
    .planInfoDialog{
      .el-dialog{
        width: 33% !important;
      }
    }
  } 
}
</style>

<template>
  <div class="equipmentButton">
    <div class="button" v-show="showWarning" @click="openAlarmSidebar">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon1.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.alarmNum==null">0</div>
        <div v-else>{{total.alarmNum}}</div>
        <div>报警设备</div>
      </div>
    </div>
    <div class="button" v-show="showTotal">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon2.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.totalNum==null">0</div>
        <div v-else>{{total.totalNum}}</div>
        <div>设备总数</div>
      </div>
    </div>
    <div class="button" v-show="showOnline">
      <div class="button_img">
        <img src="../assets/image/centralControlPlatform/icon3.png" alt="">
      </div>
      <div class="button_text">
        <div v-if="total.onlineNum==null">0</div>
        <div v-else>{{total.onlineNum}}</div>
        <div>在线设备</div>
      </div>
    </div>
  </div>
</template>
<script>
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js';
export default {
  props:{
    showWarning: {
      type: Boolean,
      default: true,
    },
    showTotal: {
      type: Boolean,
      default: true,
    },
    showOnline: {
      type: Boolean,
      default: true,
    },
  },
  data(){
    return{
      total:{}
    }
  },
  mounted(){
    this.findEquipmentTotal()
  },
  methods:{
    findEquipmentTotal(){
      CommonService.findEquipmentTotal()
      .then(res=>{
        this.total=res
      })
    },
    // 打开报警设备侧边栏
    openAlarmSidebar(){
      this.zEmit('openAlarmEquipmentSidebar');
    },
  },
}
</script>
<style lang="less">
@import '../assets/css/equipmentButton.less';
</style>
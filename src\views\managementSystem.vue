<template>
    <div class="managementSystem">
        <div class="managementSystem_header">
           后台管理系统
        </div>
        <div class="managementSystem_container">
            <div class="managementSystem_container_left">
                <div class="menuList_item" v-for="(item, index) in menuList" :key="index">
                    <div class="menuList_item_con" :class="{ itemActive: activeIndex == item.url }">
                        <div>
                            <img :src="getImgUrl(item.url)" alt="" />
                        </div>
                        <div @click="menuClick(item)">{{ item.name }}</div>
                    </div>
                </div>
            </div>
            <div class="managementSystem_container_right">
                <router-view />
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            activeIndex: 'productionPlan',
            menuList: [],
        }
    },
    created() {
        this.getMenuList()
        // this.menuListHandle()
        this.openAreaDetails()
    },
    mounted() {
        this.activeIndex = window.location.hash.split("#/")[1];
    },
    methods: {
        // 获取权限列表
        getMenuList(){
            this.menuList=JSON.parse(localStorage.getItem('permissionVoList'))
            // this.menuClick(this.menuList[0])
            const allMenuList = JSON.parse(localStorage.getItem('permissionVoList')) || []
            // 过滤掉生产计划和批次信息管理两个菜单
            this.menuList = allMenuList.filter(item => item.url !== 'productionPlan')
            
            // 修改区域阈值设置为报警阈值设置
            this.menuList.forEach(item => {
                if (item.url === 'regionalThresholdSetting') {
                    item.name = '报警阈值设置';
                }
            });
            
            // 如果当前活动的菜单被移除了，将活动菜单切换到第一个可用菜单
            // if (this.menuList.length > 0 && (this.activeIndex === 'productionPlan' || this.activeIndex === 'batchInforManagement')) {
            //     this.activeIndex = this.menuList[0].url
            //     this.$router.push({ path: this.menuList[0].url })
            // }
        },
        // 根据用户角色处理菜单
        menuListHandle() {
            let role = localStorage.getItem('userRole')
            switch (role) {
                case '0':
                    this.menuList = [
                        { name: '用户设置', url: 'systemSettings', icon: 'systemSettings' },
                        { name: '基地管理', url: 'baseManagement', icon: 'baseManagement' },
                        { name: '设备管理', url: 'deviceManagement', icon: 'deviceManagement' },
                        { name: '视频管理', url: 'videoManagement', icon: 'videoManagement' },
                        // { name: '生产计划', url: 'productionPlan', icon: 'productionPlan' },
                        { name: '报警阈值设置', url: 'regionalThresholdSetting', icon: 'regionalThresholdSetting' },
                        { name: '批次信息管理', url: 'batchInforManagement', icon: 'batchInforManagement' },
                    ]
                    break
                case '1':
                case '2':
                    this.menuList = [
                        { name: '用户设置', url: 'systemSettings', icon: 'systemSettings' },
                        { name: '设备管理', url: 'deviceManagement', icon: 'deviceManagement' },
                        { name: '视频管理', url: 'videoManagement', icon: 'videoManagement' },
                        // { name: '生产计划', url: 'productionPlan', icon: 'productionPlan' },
                        { name: '批次信息管理', url: 'batchInforManagement', icon: 'batchInforManagement' },
                    ]
                    break
            }
        },
        // 列表图片
        getImgUrl(img) {
            return require('@/assets/image/managementSystem/' + img + '.png')
        },
        
        // 大屏植保点击去制定
        openAreaDetails() {
            if (this.$route.query.type) {
                this.activeIndex = 'productionPlan'
            }
        },
        // 列表点击
        menuClick(item) {
            this.activeIndex = item.url
            this.$router.push({ path: item.url })
        },
    },
}
</script>
<style lang="less">
@import '../assets/css/managementSystem.less';
</style>

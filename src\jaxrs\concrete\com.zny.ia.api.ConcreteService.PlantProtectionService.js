/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const PlantProtectionService = {
    /**
     * 智慧植保主页面-植保记录
     * @returns Promise 
     */
    'plantProtectionRecordVoInformation': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '381b31cb8a392456fd0d47174a572ee293ebc894')
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionRecordVoInformation`, 'json', 'GET');
    }, 
    /**
     * 智慧植保主页面-近期植保计划
     * @returns Promise 
     */
    'recentPlantProtectionProgram': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ed1a9108445570df8f8f6cfd2b64ac028c80a677')
            : execute(concreteModuleName, `/PlantProtectionService/recentPlantProtectionProgram`, 'json', 'GET');
    }, 
    /**
     * 智慧植保主页面-区域详情
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'plantProtectionAreaInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6e2f0f7f7599840cfe4de135b94ccfe23230485a', areaId)
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionAreaInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 智慧植保主页面-智慧植保预防
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'plantProtectionPreventionListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ea0663e317542b1b4a117acaf49745119fd2333f', pageRequest)
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionPreventionListInformation`, 'json', 'POST', pageRequest);
    }
}

export default PlantProtectionService

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const LoginService = {
    /**
     * 修改密码
     * @param {*} oldPassword 旧密码
     * @param {*} newPassword 新密码
     * @returns Promise 
     */
    'passwordUpdate': function (oldPassword, newPassword) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5dc997751648fd1659cddf91b2827bfb32a8c1c4', {oldPassword, newPassword})
            : execute(concreteModuleName, `/LoginService/passwordUpdate`, 'json', 'POST', { oldPassword, newPassword });
    }, 
    /**
     * 登录
     * @param {*} loginName 登录名
     * @param {*} password 密码
     * @returns Promise 
     */
    'login': function (loginName, password) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6b5cf9cf7b200b4bde2da9aa3c84ca3324c9cdca', {loginName, password})
            : execute(concreteModuleName, `/LoginService/login`, 'json', 'POST', { loginName, password });
    }, 
    /**
     * 退出登录
     * @returns Promise 
     */
    'logOut': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c49c322e998de6b274c2643274eaed32050a412e')
            : execute(concreteModuleName, `/LoginService/logOut`, 'json', 'GET');
    }
}

export default LoginService

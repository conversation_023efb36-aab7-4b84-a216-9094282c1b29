.home{
  width: 1920px;
  height: 1080px;
  background: url(../image/monitoringCenter/background.png) no-repeat 100% center;
  .header{
    width: 1920px;
    height: 109px;
    position: relative;
    text-align: center;
    &_con{
      width: 1920px;
      height: 109px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      &_bg{

      }
      &_time{
        font-size: 18px;
        font-weight: 400;
        color: #00F4FD;
        position: absolute;
        top: 22px;
        left: 40px;
      }
      &_title{
        position: absolute;
        top: 18px;
        left: 0;
        right: 0;
        margin:0 auto;
        letter-spacing: 3px;
        font-size: 36px;
        font-weight: bold;
        color: #00F4FD;
      }
      &_jump{
        width: 172px;
        height: 42px;
        line-height: 42px;
        font-size: 24px;
        font-weight: 400;
        color: #30DEEA;
        position: absolute;
        top: 10px;
        cursor: pointer;
      }
      &_jumpLeft{
        background: url(../image/monitoringCenter/h_left.png) no-repeat 100% center;
        left:505px
      }
      &_jumpRight{
        background: url(../image/monitoringCenter/h_right.png) no-repeat 100% center;
        right: 505px;
      }
      &_nickname{
        position: absolute;
        top: 20px;
        right: 90px;
        font-size: 16px;
        font-weight: 400;
        color: #DFEEF3;
      }
      &_portrait{
        position: absolute;
        top: 16px;
        right: 40px;
        cursor: pointer;
      }
      &_tips{
        z-index: 105;
        position: absolute;
        top: 62px;
        right: 40px;
        width: 138px;
        height: 114px;
        background: rgba(0,61,66,0.96);
        box-shadow: inset 0px 0px 8px 0px rgba(0,244,253,0.4);
        .tips_con{
          height: 48px;
          line-height: 48px;
          font-size: 16px;
          font-weight: 400;
          color: #DFEEF3;
          cursor: pointer;
        }
        .tips_bottom_line{
          width: 106px;
          height: 1px;
          background: rgba(223,238,243,.5);
          margin: auto;
        }
      }
    }
  }
  .changePassDialog{
    .el-dialog{
      height: 410px;
      margin-top: 30vh !important;
      .el-dialog__body{
        height: 313px;
        .formList{
          .el-form{
            width: 515px;
            margin-top: 55px;
            .el-form-item__label{
              font-size: 14px;
              font-weight: 400;
              color: #FFFFFF;
            }
            .el-form-item__content{
              height: 32px;
              .el-input{
                height: 32px;
              }
            }
          }
        }
        .btnBox{
          display: flex;
          justify-content: center;
          margin-top: 50px;
          .el-button{
            width: 120px;
            height: 42px;
            margin: 0 30px;
          }
        }
      }
    }
  }
  
}
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppCropCaseService = {
    /**
     * 查询往期监控
     * @param {*} po 
     * @returns Promise 
     */
    'findMonitorBackList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '60f49d5836af648cf38794c4ab80fd409dac558e', po)
            : execute(concreteModuleName, `/App/CropCaseService/findMonitorBackList`, 'json', 'POST', po);
    }, 
    /**
     * 苗情实时拍摄
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'realTimeShoot': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c139bab355eb75040fc0fdde0b5bef878bd5fd73', equipmentId)
            : execute(concreteModuleName, `/App/CropCaseService/realTimeShoot`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 查询往期图片
     * @param {*} po 
     * @returns Promise 
     */
    'findPictureBackList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd4a18cda3894d698f9f216a99ad7b6cd2064160a', po)
            : execute(concreteModuleName, `/App/CropCaseService/findPictureBackList`, 'json', 'POST', po);
    }, 
    /**
     * 查询苗情监控列表
     * @param {*} areaId 区域id
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findCropCaseMonitorList': function (areaId, equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '75fe720c8401ee4da6cdcd16b5569310c2a69eda', {areaId, equipmentId})
            : execute(concreteModuleName, `/App/CropCaseService/findCropCaseMonitorList`, 'json', 'POST', { areaId, equipmentId });
    }
}

export default AppCropCaseService

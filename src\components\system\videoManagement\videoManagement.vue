<template>
  <div class="videoManagement systemDialogStyle">
    <div class="videoManagement_con">
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="areaId" placeholder="请选择种植区域">
            <el-option
              v-for="item in areaList"
              :key="item.areaId"
              :label="item.areaName"
              :value="item.areaId">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="equipmentId" placeholder="请选择设备">
            <el-option
              v-for="item in equipmentList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            >
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="100">
          </el-table-column>
          <el-table-column
            prop="areaName"
            align="center" 
            label="区域">
          </el-table-column>
          <el-table-column
            prop="equipmentName"
            align="center" 
            label="设备名称">
          </el-table-column>
          <el-table-column
            align="center" 
            label="时间">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.startTime}} -- {{scoped.row.endTime}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="videoDetails(scoped.row.videoId)">查看</span>
              <!-- <span class="viewData">导出</span> -->
              <span class="viewData">
                <a :href="[scoped.row.videoUrl]" target="_blank" :download="[scoped.row.videoUrl]">导出</a>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    <el-dialog
      class="videoDialog"
      title="视频监控"
      :visible.sync="videoDialog"
      width="63%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="closeDialog">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="videoBox" v-if="showVideo">
        <div class="videoDetails">
          <video id="myVideo"
            class="video-js vjs-big-play-centered"
          >
            <source :src='videoUrl' type="video/mp4">
          </video>
        </div>
        
      </div>
    </el-dialog>
  </div>
</template>
<script>
import VideoService from '../../../jaxrs/concrete/com.zny.ia.api.VideoService.js';
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
export default {
  data(){
    return{
      areaId:"",
      areaList:[],
      equipmentId:"",
      equipmentList:[],
      time:"",
      tableData: [],
      // 分页
      pageSize:10,
      currentPage:1,
      total:0,
      pageCount:1,
      // 视频弹窗
      videoDialog:false,
      showVideo:true,
      videoUrl:"",
      videohtml:'',
      player:null,
    }
  },
  mounted(){
    this.getAreaList()
    this.getVideoEquipmentList()
    this.getVideoList()
  },
  methods:{
    // 获取区域列表
    getAreaList(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        this.areaList=res
      })
    },
    // 获取视频监控设备
    getVideoEquipmentList(){
      CommonService.listVideoEquipment()
      .then(res=>{
        this.equipmentList=res
      })
    },
    // 查询
    search(){
      this.getVideoList()
    },
    // 获取列表数据
    getVideoList(){
      let po={
        num:this.currentPage,
        pageSize:this.pageSize,
        condition:{
          areaId:this.areaId,
          equipmentId:this.equipmentId,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
        }
      }
      VideoService.listVideo(po)
      .then(res=>{
        this.tableData=res.list
        this.pageCount=res.count
        this.count=res.total
      })
    },
    handleSizeChange(){

    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.getVideoList()
    },
    // 查看视频 打开弹窗
    videoDetails(videoId){
      this.videoDialog=true
      VideoService.detailVideo(videoId)
      .then(res=>{
        this.videoUrl=res
        this.showVideo=true
        this.$nextTick(()=>{
          setTimeout(()=> {
            this.player =this.$video(
              'myVideo',
              {
                autoplay: false, //自动播放
                controls: true, //是否显示底部控制栏
                loop: true, //是否循环播放
                muted: true, //设置默认播放音频
                //是否自适应布局,播放器将会有流体体积。换句话说，它将缩放以适应容器。
                // 如果<video>标签有“vjs-fluid”样式时，这个选项会自动设置为true。
                fluid: false,
              },
              function () {

              }
            );
          },500)
        })
      })
    },
    // 视频弹窗关闭
    closeDialog(){
      this.videoDialog=false
      this.showVideo=false
      if(this.player){
        this.player.dispose()
        this.player=null
      }
    },
  },
}
</script>
<style lang="less">
@import '../../../assets/css/system/videoManagement.less';
</style>
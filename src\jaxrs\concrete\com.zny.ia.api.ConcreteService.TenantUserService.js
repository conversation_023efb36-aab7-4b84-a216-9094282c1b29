/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const TenantUserService = {
    /**
     * 删除租户功能集
     * @param {*} tenantId 租户id
     * @returns Promise 
     */
    'deleteAllTenantPermission': function (tenantId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '06e2c9c65936c5bd0aa5865fae30ddb0801086fd', tenantId)
            : execute(concreteModuleName, `/TenantUserService/allTenantPermission`, 'json', 'DELETE', { tenantId });
    }, 
    /**
     * 全量更新租户功能集
     * @param {*} po 
     * @returns Promise 
     */
    'updateTenantPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '96a55e8f1cdee86cadda3c5bb611155c85ee7198', po)
            : execute(concreteModuleName, `/TenantUserService/tenantPermission`, 'json', 'PUT', po);
    }
}

export default TenantUserService

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 在线
     * desc: --
     * value: 1
     */
    在线: 1,
    /**
     * label: 离线
     * desc: --
     * value: 2
     */
    离线: 2,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
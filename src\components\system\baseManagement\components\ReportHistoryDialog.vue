<template>
  <!-- 汇报记录弹窗 -->
  <el-dialog
    class="reportHistoryDialog"
    title="汇报历史记录"
    :visible.sync="dialogVisible"
    width="60%"
    center
    :modal-append-to-body="false"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div class="close" @click="dialogVisible = false">
      <img src="../../../../assets/image/managementSystem/close.png" alt="">
    </div>
    <div class="tableBox systemTableStyle">
      <el-table
        :data="reportHistoryData"
        style="width: 100%"
        height="100%">
        <el-table-column
          type="index" 
          label="序号" 
          align="center" 
          width="90">
        </el-table-column>
        <el-table-column
          prop="coverage"
          align="center" 
          label="智慧农业覆盖率">
        </el-table-column>
        <el-table-column
          prop="annualValue"
          align="center" 
          label="作物年产值/元">
        </el-table-column>
        <el-table-column
          prop="yield"
          align="center" 
          label="作物总产量/公斤">
        </el-table-column>
        <el-table-column
          prop="createDate"
          align="center" 
          label="提交时间">
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import BaseManagementService from '../../../../jaxrs/concrete/com.zny.ia.api.BaseManagementService.js';

export default {
  name: 'ReportHistoryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reportHistoryData: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadReportHistory();
      }
    }
  },
  methods: {
    // 加载汇报历史数据
    loadReportHistory() {
      BaseManagementService.reportHistoryListInformation(1, 100)
      .then(res => {
        this.reportHistoryData = res.list;
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 组件特定样式
</style>

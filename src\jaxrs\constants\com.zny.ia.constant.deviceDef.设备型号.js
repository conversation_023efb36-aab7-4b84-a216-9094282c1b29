import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 虫情检测仪_1
     * desc: --
     * value: 虫情检测仪_1
     */
    虫情检测仪_1: "虫情检测仪_1",
    /**
     * label: 气象站_1
     * desc: --
     * value: 气象站_1
     */
    气象站_1: "气象站_1",
    /**
     * label: 水质检测仪_1
     * desc: --
     * value: 水质检测仪_1
     */
    水质检测仪_1: "水质检测仪_1",
    /**
     * label: 灌排设备_1
     * desc: --
     * value: 灌排设备_1
     */
    灌排设备_1: "灌排设备_1",
    /**
     * label: 土壤检测设备_1
     * desc: --
     * value: 土壤检测设备_1
     */
    土壤检测设备_1: "土壤检测设备_1",
    /**
     * label: 土壤检测设备_2
     * desc: --
     * value: 土壤检测设备_2
     */
    土壤检测设备_2: "土壤检测设备_2",
    /**
     * label: 苗情设备_1
     * desc: --
     * value: 苗情设备_1
     */
    苗情设备_1: "苗情设备_1",
    /**
     * label: 苗情设备_2
     * desc: --
     * value: 苗情设备_2
     */
    苗情设备_2: "苗情设备_2",
    /**
     * label: 控制柜设备_1
     * desc: --
     * value: 控制柜设备_1
     */
    控制柜设备_1: "控制柜设备_1",
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
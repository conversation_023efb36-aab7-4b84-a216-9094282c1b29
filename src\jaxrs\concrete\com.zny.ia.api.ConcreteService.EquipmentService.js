/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const EquipmentService = {
    /**
     * 下载设备列表
     * @returns Promise 
     */
    'downLoadEpList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '87da2864aa2dff0ea2fecfbc492a3bc65d4959f5')
            : execute(concreteModuleName, `/EquipmentService/downLoadEpList`, 'json', 'GET');
    }, 
    /**
     * 其他设备添加,除灌排设备
     * @param {*} po 
     * @returns Promise 
     */
    'insertEquipment': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'eb545026e48989d43e39381e5f28b8908b930f6a', po)
            : execute(concreteModuleName, `/EquipmentService/insertEquipment`, 'json', 'POST', po);
    }, 
    /**
     * 查看设备列表
     * @param {*} po 
     * @returns Promise 
     */
    'eqSelectList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '341fae8bab1971c3272c98d2a7253f9e0c681f21', po)
            : execute(concreteModuleName, `/EquipmentService/eqSelectList`, 'json', 'POST', po);
    }, 
    /**
     * 获取平台支持的设备型号列表
     * @returns Promise 
     */
    'findEquipmentTypeList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c95904f85a73ed69a21728f94eef8a0d5dc5028e')
            : execute(concreteModuleName, `/EquipmentService/findEquipmentTypeList`, 'json', 'GET');
    }, 
    /**
     * 修改设备名称
     * @param {*} equipmentId 设备id
     * @param {*} equipmentName 设备名称
     * @returns Promise 
     */
    'updateEqInfo': function (equipmentId, equipmentName) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c2f55dada777cc282148603bfe2f3595f3ff1c3c', {equipmentId, equipmentName})
            : execute(concreteModuleName, `/EquipmentService/eqInfo`, 'json', 'PUT', { equipmentId, equipmentName });
    }, 
    /**
     * 灌排设备添加
     * @param {*} po 
     * @returns Promise 
     */
    'insertIrrigationEquipment': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ce157b31937bf2c295f9bdbe3149f886efdf794c', po)
            : execute(concreteModuleName, `/EquipmentService/insertIrrigationEquipment`, 'json', 'POST', po);
    }, 
    /**
     * 设置记录间隔
     * @param {*} po 
     * @returns Promise 
     */
    'interRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2e8dc68f0fd8d36537020e152c77d82f8f8f1c17', po)
            : execute(concreteModuleName, `/EquipmentService/interRecord`, 'json', 'POST', po);
    }, 
    /**
     * 设备管理信息查看
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findEqInfo': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7c540eddd3ed7b7890be30445d92f0493305abc3', equipmentId)
            : execute(concreteModuleName, `/EquipmentService/findEqInfo`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取此设备型号支持的指标项列表
     * @param {*} equipmentType 设备型号
     * @returns Promise 
     */
    'findTargetList': function (equipmentType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '62acacf358c54e7a0842c7ebf8103c6e4c2da879', equipmentType)
            : execute(concreteModuleName, `/EquipmentService/findTargetList`, 'json', 'POST', { equipmentType });
    }, 
    /**
     * 获取此设备型号支持的指标类型列表
     * @param {*} equipmentType 设备型号
     * @returns Promise 
     */
    'findTargetTypeList': function (equipmentType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'cda2f2b746f5fda4f57d0c856201749e25535aee', equipmentType)
            : execute(concreteModuleName, `/EquipmentService/findTargetTypeList`, 'json', 'POST', { equipmentType });
    }
}

export default EquipmentService

.InsectSituation{
  &_left{
    &_smartIOTData{
      &_con{
        .handleBox{
          margin-top: 31px;
          &_item{
            width: 180px;
            height: 32px;
            margin-left: 15px;
          }
        }
        .list_title{
          width: 461px;
          height: 36px;
          line-height: 36px;
          background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));
          margin-top: 16px;
        }
        .list_title1{
          display: flex;
          justify-content: space-between;
          font-size: 16px;
          font-weight: bold;
          color: #FEFFFF;
          div:nth-child(1){
            margin-left: 15px;
          }
          div:nth-child(2){
            font-size: 20px;
            color: #00F4DA;
          }
          img{
            vertical-align: middle;
            margin-right: 8px;
          }
        }
        .list_con1{
          display: flex;
          margin-top: 20px;
          .list_item{
            width: 134px;
            height: 70px;
            background: url(../image/centralControlPlatform/border5.png) no-repeat 100% center;
            margin-left: 15px;
            text-align: center;
            font-weight: 400;
            div:first-child{
              font-size: 20px;
              color: #FEFFFF;
              margin: 8px 0 5px 0;
            }
            div:last-child{
              font-size: 14px;
              color: #B8D4FF;
            }
          }
        }
        .list_title2{
          font-size: 16px;
          font-weight: 400;
          color: #FEFFFF;
          div{
            margin-left: 21px;
            span{
              font-size: 14px;
              font-weight: 400;
              color: #00F4DA;
            }
          }
        }
        .list_con2{
          #scroll_box{
            width: 461px;
            height: 65px;
            margin: 8px 0;
            overflow: scroll;
            #scroll1,#scroll2{
              .list_item{
                display: flex;
                justify-content: start;
                font-size: 14px;
                font-weight: 400;
                color: #DFEEF3;
                .item{
                  width: 30%;
                  padding: 8px 0;
                  text-align: center;
                }
              }
              .list_item:nth-child(2n) {
                background: rgba(255,255,255,0.06);
              }
            }
          }
        }
      }
    }
  }
}
<template>
    <div class="meteorologyOfHistoryDialog dialogStyle">
        <el-dialog
            title=""
            :visible.sync="dialogVisible"
            width="90%"
            center
            :show-close="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @open="handleOpen"
        >
            <div class="line"></div>
            <div class="close" @click="dialogVisible = false">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="handleBox">
                <div class="handleBox_item formStyle">
                    <el-select v-model="areaId" clearable placeholder="请选择种植区域" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in areaIdData"
                            :key="item.areaId"
                            :label="item.areaName"
                            :value="item.areaId"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-select v-model="from" clearable placeholder="请选择数据来源" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in fromData"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-date-picker
                        v-model="time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        popper-class="datePickerStyle"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </div>
                <div class="handleBox_item searchButtonStyle">
                    <el-button @click="search">查询</el-button>
                </div>
            </div>
            <div class="tableBox tableStyle">
                <el-table :data="tableData" border style="width: 100%" height="780px">
                    <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                    <el-table-column align="center" label="光照度lx">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.illuminance.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.illuminance.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.illuminance.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.illuminance.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.illuminance.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="温度℃">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.temperature.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.temperature.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.temperature.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.temperature.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.temperature.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="湿度%">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.humidity.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.humidity.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.humidity.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.humidity.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.humidity.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="pm2.5ug/m³">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.pm2_5.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.pm2_5.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.pm2_5.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.pm2_5.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.pm2_5.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="风速m/s">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.windSpeed.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.windSpeed.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.windSpeed.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.windSpeed.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.windSpeed.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="风向">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.windDirection.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.windDirection.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.windDirection.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">
                                        {{ scoped.row.windDirection.value | windDirectionFormat }}
                                    </div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.windDirection.value | windDirectionFormat }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="光和有效辐射量µmol/(㎡·s)">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.photosyntheticRadiation.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">
                                            汇报人：{{ scoped.row.photosyntheticRadiation.loginName }}
                                        </div>
                                        <br />
                                        <div class="item">
                                            汇报时间：{{ scoped.row.photosyntheticRadiation.reportTime }}
                                        </div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.photosyntheticRadiation.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.photosyntheticRadiation.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="太阳总辐射W/㎡">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.solarRadiation.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.solarRadiation.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.solarRadiation.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.solarRadiation.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.solarRadiation.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="降雨量mm">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.precipitation.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.precipitation.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.precipitation.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.precipitation.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.precipitation.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="大气压hap">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.barometricPressure.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.barometricPressure.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.barometricPressure.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.barometricPressure.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.barometricPressure.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="carbonDioxide" label="CO₂ug/m³">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.carbonDioxide.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.carbonDioxide.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.carbonDioxide.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.carbonDioxide.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.carbonDioxide.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="collectTime" label="上报时间"></el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-box pageChange">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                        background>
                    </el-pagination>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import WeatherService from '../../jaxrs/concrete/com.zny.ia.api.WeatherService.js'
import dataSources from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$数据来源.js'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js'

export default {
    data() {
        return {
            dialogVisible: false,
            areaId: null,
            areaIdData: [
                // { label: '玉米种植一区', value: 1 },
                // { label: '玉米种植二区', value: 2 },
            ],
            from: null,
            fromData: dataSources._toArray(), //数据来源
            time: [],
            tableData: [],
            // 分页相关
            currentPage: 1,
            pageSize: 10,
            total: 0,
        }
    },
    mounted() {
        this.listen('meteorologyOfHistoryDialogOpen', () => {
            this.dialogVisible = true
            this.getAreaData()
        })
    },
    methods: {
        // 弹窗打开
        handleOpen() {
            this.getWeatherHistory()
        },
        // 获取所有区域信息
        getAreaData(){
            CommonService.allAreaListInformation()
            .then(res=>{
                this.areaIdData=res
            })
        },
        // 搜索
        search() {
            // console.log(this.from)
            if (this.from == '') {
                this.from = null
            }
            this.currentPage = 1 // 重置到第一页
            this.getWeatherHistory()
        },
        // 获取气象历史数据
        getWeatherHistory() {
            let po = {
                num: this.currentPage,
                pageSize: this.pageSize,
                condition: {
                    areaId: this.areaId,
                    startTime: this.time.length == 0 ? '' : this.time[0],
                    endTime: this.time.length == 0 ? '' : this.time[1],
                    from: this.from,
                },
            }
            WeatherService.weatherHistory(po).then(res => {
                this.tableData = res.list
                this.total = res.total || res.list.length
            })
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val
            this.currentPage = 1
            this.getWeatherHistory()
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val
            this.getWeatherHistory()
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
@import '../../assets/css/Dialog/meteorologyOfHistoryDialog.less';

.meteorologyOfHistoryDialog {
  .pagination-box {
    text-align: center;
  }
}
</style>

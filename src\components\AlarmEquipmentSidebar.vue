<template>
  <div class="alarm-equipment-list" v-if="isVisible">
    <!-- 标题 -->
    <div class="list-header">
      <img src="../assets/image/centralControlPlatform/icon1.png" alt="" />
      <span>报警设备 ({{ alarmEquipmentList.length }})</span>
      <div class="close-btn" @click="closeSidebar">×</div>
    </div>

    <!-- 设备列表 -->
    <div class="equipment-items">
      <div
        v-for="(equipment, index) in alarmEquipmentList"
        :key="index"
        class="equipment-item"
      >
        <div class="equipment-name">{{ equipment.equipmentName }}</div>
      </div>

      <!-- 空状态 -->
      <div v-if="alarmEquipmentList.length === 0" class="empty-text">
        暂无报警设备
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'AlarmEquipmentSidebar',
  data() {
    return {
      isVisible: false,
      alarmEquipmentList: []
    }
  },
  mounted() {
    // 监听打开侧边栏事件
    this.listen('openAlarmEquipmentSidebar', () => {
      this.openSidebar()
    })
  },
  methods: {
    // 打开侧边栏
    openSidebar() {
      this.isVisible = true
      this.loadAlarmEquipmentData()
    },

    // 关闭侧边栏
    closeSidebar() {
      this.isVisible = false
    },

    // 加载报警设备数据
    loadAlarmEquipmentData() {
      // 这里应该调用实际的API获取报警设备数据
      // 暂时使用模拟数据
      this.generateMockAlarmData()
    },

    // 生成模拟报警数据
    generateMockAlarmData() {
      const mockData = [
        {
          equipmentName: '温湿度传感器-001',
          alarmType: '温度过高'
        },
        {
          equipmentName: '土壤传感器-003',
          alarmType: '土壤湿度过低'
        },
        {
          equipmentName: '光照传感器-005',
          alarmType: '光照强度异常'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: '风速传感器-009',
          alarmType: '风速过大'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
        {
          equipmentName: 'CO2传感器-007',
          alarmType: 'CO2浓度过高'
        },
      ]

      this.alarmEquipmentList = mockData
    }
  },
  beforeDestroy() {
    this.removeAllListener();
  }
}
</script>

<style lang="less" scoped>
.alarm-equipment-list {
  position: absolute;
  bottom: 43px;
  left: 537px;
  width: 280px;
  max-height: 400px;
  background: rgba(0, 61, 66, 0.95);
  border: 1px solid rgba(0, 244, 253, 0.3);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);

  .list-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(0, 244, 253, 0.2);
    color: #DFEEF3;
    font-size: 14px;
    font-weight: bold;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .close-btn {
      margin-left: auto;
      cursor: pointer;
      color: rgba(223, 238, 243, 0.6);
      font-size: 18px;
      line-height: 1;
      padding: 2px 6px;
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #DFEEF3;
      }
    }
  }

  .equipment-items {
    max-height: 320px;
    overflow-y: auto;
    padding: 8px;

    .equipment-item {
      padding: 8px 12px;
      margin-bottom: 6px;
      background: rgba(1, 18, 21, 0.6);
      border-radius: 4px;
      border-left: 3px solid #FF4757;

      .equipment-name {
        color: #DFEEF3;
        font-size: 13px;
        font-weight: bold;
        margin-bottom: 2px;
      }

      .alarm-type {
        color: #00F4FD;
        font-size: 11px;
      }
    }

    .empty-text {
      text-align: center;
      padding: 20px;
      color: rgba(223, 238, 243, 0.5);
      font-size: 12px;
    }

    /* 滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 244, 253, 0.3);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 244, 253, 0.5);
      }
    }
  }
}
</style>

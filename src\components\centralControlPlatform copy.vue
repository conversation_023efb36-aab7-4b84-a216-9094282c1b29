<template>
    <div>
        <div>
            <div class="centralControlPlatform">
                <!-- <div class="centralControlPlatform_menuList">
                    <div class="centralControlPlatform_menuList_container">
                        <div class="menuList_item" v-for="(item, index) in menuList" :key="index">
                            <div class="menuList_item_con" :class="{ itemActive: activeIndex == item.url }">
                                <div>
                                    <img
                                        v-if="activeIndex == item.url"
                                        src="../assets/image/centralControlPlatform/activePoint.png"
                                        alt=""
                                    />
                                    <img v-else src="../assets/image/centralControlPlatform/point.png" alt="" />
                                </div>
                                <div @click="menuClick(item)" :class="{ active: activeIndex == item.url }">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="menuLine">
                        <img src="../assets/image/centralControlPlatform/menuLine.png" alt="" />
                    </div>
                </div> -->
                <div class="centralControlPlatform_con">
                    <router-view />
                </div>
            </div>
        </div>
        <!-- 气象、土壤、虫情 报警弹窗 -->
        <AlarmDialog />
        <!-- 气象弹窗 -->
        <!-- <MeteorologicalAlarmDialog /> -->
        <MeteorologyEMDialog />
        <MeteorologyOfHistoryDialog />
        <MeteorologyReportUpdateDialog />
        <!-- 土壤弹窗 -->
        <SoilEMDialog />
        <SoilOfHistoryDialog />
        <SoilReportUpdateDialog />

        <!-- 虫情弹窗 -->
        <InsectSituationEMDialog />
        <!-- <InsectSituationAlarmDialog /> -->
        <InsectSituationOfHistoryDialog />
        <InsectSituationUploadPicturesDialog />
        <!-- 灌排弹窗 -->
        <irrigationDrainageDialog />
        <!-- 苗情弹窗 -->
        <seedlingGrowthMonitorDialog />
        <seedlingGrowthHistoryPhotoDialog />
        <seedlingGrowthUploadPhotoDialog />
        <seedlingGrowthUploadDialog />
        <!-- 溯源弹窗 -->
        <traceRecordUploadDialog />
        <traceRecordAddDialog />
        <!-- 水质弹窗 -->
<!--        <waterMonitorDialogOpen />-->
    </div>
</template>
<script>
// import MeteorologicalAlarmDialog from '../components/Dialog/meteorologicalAlarmDialog.vue'
import MeteorologyEMDialog from '../components/Dialog/meteorologyEMDialog.vue'
import MeteorologyOfHistoryDialog from './Dialog/meteorologyOfHistoryDialog.vue'
import MeteorologyReportUpdateDialog from './Dialog/meteorologyReportUpdateDialog.vue'
import SoilEMDialog from './Dialog/soilEMDialog.vue'
import SoilOfHistoryDialog from './Dialog/soilOfHistoryDialog.vue'
import SoilReportUpdateDialog from './Dialog/soilReportUpdateDialog.vue'
import AlarmDialog from './Dialog/AlarmDialog.vue'
import InsectSituationEMDialog from './Dialog/InsectSituationEMDialog.vue'
// import InsectSituationAlarmDialog from './Dialog/InsectSituationAlarmDialog.vue'
import InsectSituationOfHistoryDialog from './Dialog/InsectSituationOfHistoryDialog.vue'
import InsectSituationUploadPicturesDialog from './Dialog/InsectSituationUploadPicturesDialog.vue'

import irrigationDrainageDialog from '../components/Dialog/irrigationDrainageDialog.vue'
import seedlingGrowthMonitorDialog from '../components/Dialog/seedlingGrowthMonitorDialog.vue'
import seedlingGrowthHistoryPhotoDialog from '../components/Dialog/seedlingGrowthHistoryPhotoDialog.vue'
import seedlingGrowthUploadPhotoDialog from '../components/Dialog/seedlingGrowthUploadPhotoDialog.vue'
import seedlingGrowthUploadDialog from '../components/Dialog/seedlingGrowthUpload.vue'
import traceRecordUploadDialog from '../components/Dialog/traceRecordUploadDialog.vue'
import traceRecordAddDialog from '../components/Dialog/traceRecordAddDialog.vue'
// import waterMonitorDialogOpen from './Dialog/waterMonitorDialogOpen.vue'

export default {
    name: 'centralControlPlatform',
    components: {
        // MeteorologicalAlarmDialog,
        MeteorologyEMDialog,
        MeteorologyOfHistoryDialog,
        MeteorologyReportUpdateDialog,
        SoilEMDialog,
        SoilOfHistoryDialog,
        SoilReportUpdateDialog,
        AlarmDialog,
        InsectSituationEMDialog,
        // InsectSituationAlarmDialog,
        InsectSituationOfHistoryDialog,
        InsectSituationUploadPicturesDialog,
        irrigationDrainageDialog,
        seedlingGrowthMonitorDialog,
        seedlingGrowthHistoryPhotoDialog,
        seedlingGrowthUploadPhotoDialog,
        seedlingGrowthUploadDialog,
        traceRecordUploadDialog,
        traceRecordAddDialog,
        // waterMonitorDialogOpen,
    },
    data() {
        return {
            activeIndex: '/meteorology',
            menuList: [
                // { name: '气象环境监测系统', url: '/meteorology' },
                // { name: '土壤环境监测系统', url: '/soil' },
                // { name: '虫情监测系统', url: '/InsectSituation' },
                // { name: '苗情监测系统', url: '/seedlingGrowth' },
                // { name: '智慧植保系统', url: '/eppoWisdom' },
                // { name: '遥感数字化系统', url: '/remoteSensNumber' },
                // { name: '灌排系统', url: '/irrigationDrainage' },
                // { name: '质量安全追溯系统', url: '/traceSystem' },
                // { name: '水质检测系统', url: '/waterMonitor' },
                // { name: '电商交易系统', url: '' },
            ],
        }
    },
    created(){
        this.getMenuList()
    },
    mounted() {
        this.activeIndex = window.location.hash.split("#")[1];
    },
    methods: {
        // 获取权限列表
        getMenuList(){
            this.menuList=JSON.parse(localStorage.getItem('frontPermissionVoList'))
        },
        menuClick(item) {
            this.activeIndex = item.url
            this.$router.push({ path: item.url })
        },
    },
}
</script>
<style lang="less">
@import '../assets/css/centralControlPlatform.less';
</style>

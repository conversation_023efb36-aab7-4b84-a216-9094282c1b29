import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 山大仁科虫情检测仪
     * desc: --
     * value: 山大仁科虫情检测仪
     */
    山大仁科虫情检测仪: "山大仁科虫情检测仪",
    /**
     * label: 山大仁科气象站
     * desc: --
     * value: 山大仁科气象站
     */
    山大仁科气象站: "山大仁科气象站",
    /**
     * label: 山大仁科水质检测仪
     * desc: --
     * value: 山大仁科水质检测仪
     */
    山大仁科水质检测仪: "山大仁科水质检测仪",
    /**
     * label: 山大仁科灌排设备
     * desc: --
     * value: 山大仁科灌排设备
     */
    山大仁科灌排设备: "山大仁科灌排设备",
    /**
     * label: 山大仁科土壤检测设备
     * desc: --
     * value: 山大仁科土壤检测设备
     */
    山大仁科土壤检测设备: "山大仁科土壤检测设备",
    /**
     * label: 海康威视苗情设备
     * desc: --
     * value: 海康威视苗情设备
     */
    海康威视苗情设备: "海康威视苗情设备",
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
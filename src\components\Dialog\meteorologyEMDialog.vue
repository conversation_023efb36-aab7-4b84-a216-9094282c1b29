<template>
  <div class="meteorologyEMDialog dialogStyle">
    <el-dialog
      :title="title"
      width="74.6%"
      :visible.sync="meteorologyEMDialog"
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open='handleOpen'>
      <div class="line"></div>
      <div class="close" @click="meteorologyEMDialog=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="time">
        数据采集时间：{{collectDateTime || '暂无数据'}}
      </div>
      <div class="handleBox">
        <div class="handleBox_item searchButtonStyle" v-if="areaIsJudge">
          <el-button @click="meteorologyReportUpdateDialogOpen">汇报更新</el-button>
        </div>
        <div class="handleBox_item searchButtonStyle">
          <el-button @click="meteorologyOfHistoryDialogOpen">历史气象记录</el-button>
        </div>
      </div>
      <div class="dataList">
        <div class="list1">
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/icon1.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="basicData.temperature==null">--</div>
              <div v-else>{{basicData.temperature}}℃</div>
              <div>温度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/monitoringCenter/icon2.png" alt="">
            </div>
            <div class="item_text">
              <div>{{basicData.humidity || '--'}}%RH</div>
              <div>湿度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon4.png" alt="">
            </div>
            <div class="item_text">              
              <div>{{typeof basicData.precipitation === 'number' ? basicData.precipitation : '--'}}mm</div>
              <div>雨量</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon5.png" alt="">
            </div>
            <div class="item_text">
              <div v-if="basicData.windLevel==null">--</div>
              <div v-else>{{basicData.windLevel}}级{{basicData.windDirection | windDirectionFormat}}</div>
              <div>风况</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon6.png" alt="">
            </div>
            <div class="item_text">
              <div>{{basicData.illuminance || '--'}}lux</div>
              <div>光照度</div>
            </div>
          </div>
        </div>
        <div class="list2">
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon7.png" alt="">
            </div>
            <div class="item_text">
              <div>{{basicData.carbonDioxide || '--'}}ppm</div>
              <div>CO₂浓度</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon8.png" alt="">
            </div>
            <div class="item_text">
              <div>{{typeof basicData.solarRadiation === 'number' ? basicData.solarRadiation : '--'}}W/㎡</div>
              <div>太阳总辐射</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon9.png" alt="">
            </div>
            <div class="item_text">
              <div>{{typeof basicData.precipitation === 'number' ? basicData.precipitation : '--'}}h</div>
              <div>日照小时数</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon10.png" alt="">
            </div>
            <div class="item_text">
              <div>{{basicData.barometricPressure || '--'}}hpa</div>
              <div>大气压</div>
            </div>
          </div>
          <div class="list_item">
            <div class="item_img">
              <img src="../../assets/image/centralControlPlatform/icon11.png" alt="">
            </div>
            <div class="item_text">
              <div>{{basicData.pm2_5 || '--'}}μg/m³</div>
              <div>PM2.5</div>
            </div>
          </div>
        </div>
      </div>
      <div class="chartList">
        <div class="chartList_left">
          <div class="chartList_item">
            <div class="chartList_item_title">温湿度变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="THTimeCheck(0)" :class="{active1:THTimeActive==0}">
                本天
              </div>
              <div class="checkBtn2 checkBtn" @click="THTimeCheck(1)" :class="{active2:THTimeActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="THTimeCheck(2)" :class="{active2:THTimeActive==2}">
                近30天
              </div>
            </div>
            <div class="chartList_item_chart" style="height:280px" v-if="THTimeActive==0">
              <znyEchart :chartData="THLineOption" :chartType="'line'"></znyEchart>
            </div>
            <div class="chartList_item_temperatureList" v-if="THTimeActive==1||THTimeActive==2">
              <div class="temperatureItem">
                平均温度：<span>{{temperatureAvg || '--'}}℃</span>
              </div>
              <div class="temperatureItem">
                最高温度：<span>{{temperatureMax || '--'}}℃</span>
              </div>
              <div class="temperatureItem">
                最低温度：<span>{{temperatureMin || '--'}}℃</span> 
              </div>
            </div>
            <div class="chartList_item_chart" style="height:218px" v-if="THTimeActive==1||THTimeActive==2">
              <znyEchart :chartData="THLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
          <div class="chartList_item">
            <div class="chartList_item_title">光照度变化曲线</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="IlluminanceCheck(0)" :class="{active1:IlluminanceActive==0}">
                本天
              </div>
              <div class="checkBtn2 checkBtn" @click="IlluminanceCheck(1)" :class="{active2:IlluminanceActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="IlluminanceCheck(2)" :class="{active2:IlluminanceActive==2}">
                近30天
              </div>
            </div>
            <div class="chartList_item_chart" style="height:280px;" v-if="IlluminanceActive==0">
              <znyEchart :chartData="IlluminanceLineOption" :chartType="'line'"></znyEchart>
            </div>
            <div class="chartList_item_IlluminanceList" v-if="IlluminanceActive==1||IlluminanceActive==2">
              <div class="IlluminanceItem">
                平均光照度：<span>{{illuminanceAvg || '--'}}</span> 
              </div>
            </div>
            <div class="chartList_item_chart" style="height:218px" v-if="IlluminanceActive==1||IlluminanceActive==2">
              <znyEchart :chartData="IlluminanceLineOption" :chartType="'line'"></znyEchart>
            </div>
            
          </div>
        </div>
        <div class="chartList_right">
          <div class="chartList_item">
            <div class="chartList_item_title">风向图(速度分布)</div>
            <div class="chartList_item_checkBtn">
              <div class="checkBtn1 checkBtn" @click="windCheck(0)" :class="{active1:windActive==0}">
                本天
              </div>
              <div class="checkBtn2 checkBtn" @click="windCheck(1)" :class="{active2:windActive==1}">
                近七天
              </div>
              <div class="checkBtn2 checkBtn" @click="windCheck(2)" :class="{active2:windActive==2}">
                近30天
              </div>
            </div>
            <div class="chartList_item_chart">
              <znyEchart :chartData="windBarOption" :chartType="'bar'"></znyEchart>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {chart} from '../../js/chart.js';
import WeatherService from '../../jaxrs/concrete/com.zny.ia.api.WeatherService.js';
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
export default {
  data(){
    return{
      collectDateTime:'',
      title:"",
      meteorologyEMDialog:false,
      areaIsJudge:true,//判断当前用户能否操作该区域
      basicData:{},//基础数据
      temperatureAvg:null,
      temperatureMax:null,
      temperatureMin:null,
      THTimeActive:0,//温湿度选择按钮->0:本天,1:近七天,2:近30天
      THLineOption:{//温湿度折线图数据
        show:true,
        option:{},
      },
      illuminanceAvg:null,
      IlluminanceActive:0,//光照度选择按钮->0:本天,1:近七天,2:近30天
      IlluminanceLineOption:{//光照度折线图数据
        show:true,
        option:{},
      },
      windActive:0,//风向选择按钮->0:本天,1:近七天,2:近30天
      windBarOption:{//光照度折线图数据
        show:true,
        option:{},
      },
      areaId:'',
    }
  },
  mounted(){
    // 弹窗打开
    this.listen('meteorologyEMDialogOpen', (block) => {
      this.areaId=block.areaId
      this.title=block.areaName
      this.meteorologyEMDialog=true
      this.areaJudge()
    })
    // 数据上报成功，刷新数据
    this.listen('meteorologyUpdateSuccess', () => {
      this.getWeatherBasicData()
      this.getTemperatureChart()
      this.getIlluminanceChart()
      this.getWindChart()
    })
  },
  methods:{
    // 判断当前用户能否操作该区域
    areaJudge(){
      CommonService.areaJudge(this.areaId)
      .then(res=>{
        // console.log(res);
        this.areaIsJudge=res
      })
    },
    // 弹窗打开时调用
    handleOpen(){
      this.$nextTick(()=>{
        this.getWeatherAreaData(this.areaId)
        this.getTemperatureChart()
        this.getIlluminanceChart()
        this.getWindChart()
      })
    },
    //根据区域ID获取数据采集时间
    async getWeatherAreaData(areaId){
      const res = await WeatherService.weatherAreaData(areaId)
      this.basicData = res // 获取气象基础数据
      this.collectDateTime = res.collectTime //赋正确时间
    },
    // 获取气象基础数据
    getWeatherBasicData(){
      WeatherService.weatherBasicData()
      .then(res=>{
        this.basicData=res
      })
    },
    // 温湿度点击
    THTimeCheck(num){
      this.THTimeActive=num
      this.getTemperatureChart()
    },
    // 获取温湿度图表
    getTemperatureChart(){
      if(this.THTimeActive==0){
        WeatherService.temperatureChartDay(this.areaId)
        .then(res=>{
          chart.THLineToday(res).then(data=>{
            this.THLineOption=data
          })
        })
      }else {
        let po={
          areaId:this.areaId,
          showType:this.THTimeActive
        }
        WeatherService.temperatureChart(po)
        .then(res=>{
          this.temperatureAvg = res.temperatureAvg
          this.temperatureMax = res.temperatureMax
          this.temperatureMin = res.temperatureMin
          if(res.weatherChartVoList==null||res.weatherChartVoList.length==0){
            this.THLineOption={//温湿度折线图数据
              show:false,
              option:{},
            } 
          }else{
            chart.THLine(res.weatherChartVoList).then(data=>{
              this.THLineOption=data
            })
          }
        })
      }
    },
    // 光照度点击
    IlluminanceCheck(num){
      this.IlluminanceActive=num
      this.getIlluminanceChart()
    },
    // 获取光照度图表
    getIlluminanceChart(){
      let po={
        areaId:this.areaId,
        showType:this.IlluminanceActive
      }
      WeatherService.illuminanceChart(po)
      .then(res=>{
        this.illuminanceAvg=res.illuminanceAvg?res.illuminanceAvg:0
        if(res.illuminanceInfo==null||res.illuminanceInfo.length==0){
          this.IlluminanceLineOption={
            show:false,
            option:{},
          } 
        }else{
          chart.IlluminanceLine(res.illuminanceInfo?res.illuminanceInfo:[]).then(data=>{
            this.IlluminanceLineOption=data
          })
        }
      })
    },
    // 风向点击
    windCheck(num){
      this.windActive=num
      this.getWindChart()
    },
    // 获取风向数据
    getWindChart(){
      let po={
        areaId:this.areaId,
        showType:this.windActive
      }
      WeatherService.windChart(po)
      .then(res=>{
        chart.windBar(res).then(data=>{
          this.windBarOption=data
        })
      })
    },
    // 打开历史气象记录弹窗
    meteorologyOfHistoryDialogOpen(){
      this.zEmit('meteorologyOfHistoryDialogOpen');
    },
    // 打开汇报更新弹窗
    meteorologyReportUpdateDialogOpen(){
      this.zEmit('meteorologyReportUpdateDialogOpen',this.areaId);
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologyEMDialog.less';
</style>
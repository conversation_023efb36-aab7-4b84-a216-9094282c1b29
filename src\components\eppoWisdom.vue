<template>
    <div class="soil">
        <div class="soil_left">
            <!-- 区域概况 -->
            <div class="soil_left_smartIOTData">
                <div class="title soil_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>植保记录</span>
                </div>
                <div class="InsectSituation_left_smartIOTData_con">
                    <div class="list_con2" style="height:100%">
                        <div id="scroll_box" style="height: 93%;">
                            <vue-seamless-scroll
                                :data="districtlist"
                                class="seamless-warp"
                                style="width: 100%"
                                :class-option="classOption"
                            >
                                <div id="scroll1">
                                    <div
                                        class="list_item"
                                        v-for="(item, index) in districtlist"
                                        :key="index"
                                        @click="openWindowSuspension(item)"
                                    >
                                        <div class="item" style="width:33%">
                                            {{ item.planDate }}
                                        </div>
                                        <div class="item" style="width:33%">
                                            {{ item.title }}
                                        </div>
                                        <div class="item" style="width:33%">
                                            <span v-if="item.state == 0" style="color: #CCCCCC;">
                                                {{ item.state | productionPlanFormat }}
                                            </span>
                                            <span v-if="item.state == 1" style="color: #FACD1B;">
                                                {{ item.state | productionPlanFormat }}
                                            </span>
                                            <span v-if="item.state == 2" style="color: #00FCFF;">
                                                {{ item.state | productionPlanFormat }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </vue-seamless-scroll>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 智慧植保预防 -->
            <div class="soil_left_equipmentAlarm" style="height:640px">
                <div class="title soil_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧植保预防</span>
                </div>
                <div class="soil_left_equipmentAlarm_con">
                    <div class="prevent">
                        <div class="prevent-table">
                            <div
                                :class="tableClass == 1 ? 'prevent-button' : 'prevent-button-two'"
                                @click="cuttable(1)"
                                class="prevent-radius"
                            >
                                <span>预防病情</span>
                            </div>
                            <div
                                :class="tableClass == 2 ? 'prevent-button' : 'prevent-button-two'"
                                @click="cuttable(2)"
                                class="prevent-radius2"
                            >
                                <span>害虫明确</span>
                            </div>
                        </div>
                        <div class="moreknowledge">
                            <!-- <span>更多知识>></span> -->
                        </div>
                        <div class="content">
                            <div class="content-box" v-for="(item, index) in eppoPreventionlist" :key="index">
                                <el-row>
                                    <el-col :span="11">
                                        <img :src="item.picture" alt="" />
                                    </el-col>
                                    <el-col :span="1">
                                        <div style="height:10px"></div>
                                    </el-col>
                                    <el-col :span="11">
                                        <div class="content-box-text1">{{ item.title }}</div>
                                        <div class="content-box-text2 content-box-margin">
                                            {{ item.description }}
                                        </div>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="4">
                                        <div class="content-box-text2">防治方法：</div>
                                    </el-col>
                                    <el-col :span="20">
                                        <div class="content-box-text2" :class="{ hide_description: item.state }">
                                            {{ item.prevent }}
                                        </div>
                                    </el-col>
                                </el-row>
                                <div class="more" v-if="item.state" @click="item.state = !item.state">展开更多</div>
                                <div class="wire"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="soil_right">
            <div class="title soil_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="soil_right_con">
                <div class="farming">
                    <div class="farming_position">
                        <div class="eppoWisdom">近期植保计划</div>
                        <div class="recentEppo">
                            <div class="recentEppo-box" v-for="(item, index) in plantProtectionPlanlist" :key="index">
                                <div class="recentEppo-time">{{ item.planDate }}</div>
                                <div class="recentEppo-area" @click="openWindowSuspension(item)">
                                    {{ item.planAreaName }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="meteorology_right">
                        <znyAMap :type="7"></znyAMap>
                        <!-- <div class="eppoWisdom_twoDimensionalMap" @click="dialogVisibles = true"></div> -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 悬浮窗 -->

        <div class="mask_layer" v-if="dialogVisible">
            <transition name="el-zoom-in-top">
                <div class="Dialog" v-if="dialogVisible">
                    <img
                        src="../assets/image/eppoWisdom/close.png"
                        alt=""
                        class="clone"
                        @click="dialogVisible = false"
                    />
                    <div class="content">
                        <div class="Dialog-text1">{{ WindowSuspension.title }}</div>
                        <div class="Dialog-text3">
                            <span>植保计划</span>
                            <span>{{ WindowSuspension.startTime }}</span>
                            <span>至</span>
                            <span>{{ WindowSuspension.endTime }}</span>
                        </div>
                        <div class="Dialog-text2">{{ WindowSuspension.content }}</div>
                        <div class="wire"></div>
                        <div class="image">
                            <img :src="item" alt="" v-for="(item, index) in WindowSuspension.picList" :key="index" />
                        </div>
                        <div class="wire"></div>
                        <div class="correlation_area">
                            <div class="correlation_area_left correlation_area_text">相关区域：</div>
                            <div class="correlation_area_right correlation_area_text">
                                <div
                                    class="right_row"
                                    v-for="(item, index) in WindowSuspension.planAreaVoList"
                                    :key="index"
                                >
                                    <div class="right_col1">{{ item.areaName }}</div>
                                    <div class="right_col1">完成时间{{ item.accomplishTime }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>

        <!-- 弹窗 -->
        <div class="meteorologyEMDialog dialogStyle delDialog">
            <el-dialog
                :title="title"
                width="74%"
                :visible.sync="dialogVisibles"
                v-if="dialogVisibles"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="dialogVisibles = false" />
                <div class="header-text">数据采集时间：{{ time }}</div>
                <div class="wire"></div>
                <div class="content">
                    <div class="row1">
                        <div class="topList1">
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/monitoringCenter/icon1.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div>{{ regionalDetailslist.temperature }}℃</div>
                                    <div>温度</div>
                                </div>
                            </div>
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/monitoringCenter/icon2.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div>{{ regionalDetailslist.humidity }}%RH</div>
                                    <div>湿度</div>
                                </div>
                            </div>
                        </div>
                        <div class="topList2">
                            <el-tooltip popper-class="tooltipWidthStylesss" placement="right-start">
                                <div slot="content" class="Dialog_contetrt_hover">
                                    <div
                                        class="hover_row"
                                        v-for="(item, index) in regionalDetailslist.wormCaseMonitor"
                                        :key="index"
                                    >
                                        <div class="hover_row_col1">{{ item.kind | kindFormat }}</div>
                                        <div class="hover_row_col2">{{ item.density }}只/亩，</div>
                                        <div class="hover_row_col3">危害指数{{ item.harm }}</div>
                                    </div>
                                </div>

                                <div>
                                    <div class="topList2_item">
                                        <div class="topList2_item_text1" style="color:#00FFD2">
                                            {{ regionalDetailslist.insectHazard }}
                                        </div>
                                        <div class="topList2_item_text2">虫情危害指数</div>
                                    </div>
                                </div>
                            </el-tooltip>

                            <div class="topList2_item">
                                <div class="topList2_item_text1">{{ regionalDetailslist.insectSpecies }}</div>
                                <div class="topList2_item_text2">今日虫情种类</div>
                            </div>
                            <div class="topList2_item">
                                <div class="topList2_item_text1">{{ regionalDetailslist.totalPlanNumber }}</div>
                                <div class="topList2_item_text2">累计植保次数</div>
                            </div>
                            <div class="topList2_item">
                                <div class="topList2_item_text1">{{ regionalDetailslist.totalPesticides }}</div>
                                <div class="topList2_item_text2">累计施药种类</div>
                            </div>
                        </div>
                    </div>
                    <div class="row2">
                        <div class="tableBox systemTableStyle table">
                            <div class="table_text">历史植保记录</div>
                            <el-table
                                :data="regionalDetailslist.historicalPlantProtectionVoList"
                                height="380"
                                style="width:670px"
                                :cell-style="cellStyle"
                                :header-cell-style="headerCellStyle"
                            >
                                <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                                <el-table-column prop="title" label="关联植保计划" align="center"></el-table-column>
                                <el-table-column
                                    prop="accomplishTime"
                                    label="作业时间"
                                    align="center"
                                ></el-table-column>
                                <el-table-column
                                    prop="pesticides"
                                    label="使用农药种类"
                                    align="center"
                                ></el-table-column>
                            </el-table>
                        </div>

                        <div class="row2_wire"></div>
                        <div class="row2_right">
                            <div class="row2_right_box1">
                                <div class="eppoWisdom">近期植保计划</div>
                                <div class="text" @click="formulate" v-if="areaIsJudge">去制定>></div>
                            </div>
                            <div class="row2_right_box2">
                                <div
                                    v-for="(item, index) in planAreaVoList"
                                    :key="index"
                                    :class="index == planArea ? 'center1' : 'center2'"
                                    @click="planArea = index"
                                >
                                    {{ item }}
                                </div>
                                <!-- <div class="center2">8月28日</div> -->
                            </div>
                            <div
                                class="row2_right_box3"
                                v-if="regionalDetailslist.plantProtectionPlanVoList.length != 0"
                            >
                                <div class="text1">
                                    {{ regionalDetailslist.plantProtectionPlanVoList[planArea].title }}
                                </div>
                                <div class="text2">
                                    {{ regionalDetailslist.plantProtectionPlanVoList[planArea].content }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import znyAMap from '../components/znyAMap.vue'
import PlantProtectionService from '../jaxrs/concrete/com.zny.ia.api.PlantProtectionService'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js';

// import EquipmentAlarm from '../components/equipmentAlarm.vue'
export default {
    components: {
        znyAMap,
    },
    data() {
        return {
            classOption: {
                step: 0.5,
            },
            dialogVisible: false,
            value: 1,
            options: [
                { name: '小麦种植1区', value: 1 },
                { name: '小麦种植2区', value: 2 },
                { name: '小麦种植3区', value: 3 },
            ],
            tableClass: 1,
            dialogVisibles: false,
            areaIsJudge:true,//判断当前用户能否操作该区域
            title: '玉米种植1区虫情监测1',
            time: '2022-06-22 11:15:26',
            tableData: [
                {
                    date: '2016-05-03',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-02',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-04',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-01',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-08',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-06',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
                {
                    date: '2016-05-07',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄',
                },
            ],
            plantProtectionPlanlist: [],
            WindowSuspension: {},
            districtlist: [],
            querlist: {
                num: 1,
                pageSize: 6,
                condition: {
                    type: 0,
                },
            },
            eppoPreventionlist: [],
            text: '',
            regionalDetailslist: {},
            planAreaVoList: [],
            planArea: 0,
        }
    },
    created() {
        this.openAreaDetails()
    },
    mounted() {
        this.addplantProtectionPlan()
        this.eppoPrevention()
    },
    methods: {
        addplantProtectionPlan() {
            PlantProtectionService.recentPlantProtectionProgram().then(res => {
                res.forEach(item => {
                    var month = item.planDate.substr(0, 2)
                    let day = item.planDate.substr(3, 2)

                    item.planDate = `${month}月${day}日`
                })
                this.plantProtectionPlanlist = res
            })
            PlantProtectionService.plantProtectionRecordVoInformation().then(res => {
                this.districtlist = res
            })
        },
        eppoPrevention() {
            PlantProtectionService.plantProtectionPreventionListInformation(this.querlist).then(res => {
                let num = res.list.map(item => {
                    return {
                        description: item.description,
                        picture: item.picture,
                        prevent: item.prevent,
                        title: item.title,
                        state: item.prevent.length > 165 ? true : false,
                    }
                })
                // num.forEach(item => {
                //     if (item.prevent.length > 165) {
                //         item.state = true
                //     }
                // })
                this.eppoPreventionlist = num
            })
        },
        openAreaDetails() {
            this.listen('AreaDetailsDialogOpen', areaId => {
                this.regionalDetails(areaId)
                this.areaJudge(areaId)
                // this.dialogVisibles = true
            })
        },
        // 区域详情
        regionalDetails(areaId) {
            PlantProtectionService.plantProtectionAreaInformation(areaId).then(res => {
                this.regionalDetailslist = res
                let array = []
                res.plantProtectionPlanVoList.forEach(item => {
                    var month = item.planDate.substr(0, 2)
                    let day = item.planDate.substr(3, 2)
                    array.push(`${month}月${day}日`)
                    // item.planDate = `${month}月${day}日`
                })
                this.title = res.areaVo.areaName
                this.time = res.collectDateTime
                this.planAreaVoList = array
                this.dialogVisibles = true
            })
        },
        // 判断当前用户能否操作该区域
        areaJudge(areaId){
            CommonService.areaJudge(areaId)
            .then(res=>{
                // console.log(res);
                this.areaIsJudge=res
            })
        },
        // 点击去制定
        formulate() {
            let routeUrl = this.$router.resolve({
                path: '/productionPlan',
                query: {
                    type: true,
                },
            })
            window.open(routeUrl.href, '_blank')
        },
        // 打开悬浮窗
        openWindowSuspension(value) {
            this.WindowSuspension = value
            this.dialogVisible = true
        },
        cuttable(value) {
            this.tableClass = value
            this.eppoPrevention()
        },
        // 修改 table cell边框的背景色
        cellStyle() {
            return 'background-color: #003D42; color: #DFEEF3; border-color:#FEFFFF'
        },
        // 修改 table header cell的背景色
        headerCellStyle() {
            return 'background: #1a5155;color: #DFEEF3; border:0px'
        },
    },
}
</script>
<style lang="less" scoped>
@import '../assets/css/eppoWisdom.less';
</style>
<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/soil.less';
@import '../assets/css/Dialog/meteorologicalAlarmDialog.less';
// @import '../assets/css/app.less';
@import '../assets/css/InsectSituation.less';
</style>

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppAlarmService = {
    /**
     * 报警记录
     * @param {*} po 报警记录入参
     * @returns Promise 
     */
    'alarmCollect': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '77ac8481eb4bf0a64240a62739246549f5a13f03', po)
            : execute(concreteModuleName, `/App/AlarmService/alarmCollect`, 'json', 'POST', po);
    }, 
    /**
     * 土壤报警详情
     * @param {*} po 记录id
     * @returns Promise 
     */
    'detailSoilAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd8e990467165b07681c226e6e012bc3512b7e39b', po)
            : execute(concreteModuleName, `/App/AlarmService/detailSoilAlarmRecord`, 'json', 'POST', { po });
    }, 
    /**
     * 虫情报警详情
     * @param {*} po 记录id
     * @returns Promise 
     */
    'detailWormAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8716b60b94a5110bc042471725e28bd16a147998', po)
            : execute(concreteModuleName, `/App/AlarmService/detailWormAlarmRecord`, 'json', 'POST', { po });
    }, 
    /**
     * 气象报警详情
     * @param {*} po 记录id
     * @returns Promise 
     */
    'detailWeatherAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2b16ec8a8b0c0be07f8e43ec9a079213ca9d9451', po)
            : execute(concreteModuleName, `/App/AlarmService/detailWeatherAlarmRecord`, 'json', 'POST', { po });
    }
}

export default AppAlarmService

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 控制状态
     * desc: --
     * value: 控制状态
     */
    控制状态: "控制状态",
    /**
     * label: 卷帘
     * desc: --
     * value: 卷帘
     */
    卷帘: "卷帘",
    /**
     * label: 卷膜
     * desc: --
     * value: 卷膜
     */
    卷膜: "卷膜",
    /**
     * label: 通风
     * desc: --
     * value: 通风
     */
    通风: "通风",
    /**
     * label: 上通风
     * desc: --
     * value: 上通风
     */
    上通风: "上通风",
    /**
     * label: 下通风
     * desc: --
     * value: 下通风
     */
    下通风: "下通风",
    /**
     * label: 内遮阳
     * desc: --
     * value: 内遮阳
     */
    内遮阳: "内遮阳",
    /**
     * label: 外遮阳
     * desc: --
     * value: 外遮阳
     */
    外遮阳: "外遮阳",
    /**
     * label: 水帘
     * desc: --
     * value: 水帘
     */
    水帘: "水帘",
    /**
     * label: 电磁阀
     * desc: --
     * value: 电磁阀
     */
    电磁阀: "电磁阀",
    /**
     * label: 补光灯
     * desc: --
     * value: 补光灯
     */
    补光灯: "补光灯",
    /**
     * label: 照明
     * desc: --
     * value: 照明
     */
    照明: "照明",
    /**
     * label: 备用设备
     * desc: --
     * value: 备用设备
     */
    备用设备: "备用设备",
    /**
     * label: 内保温
     * desc: --
     * value: 内保温
     */
    内保温: "内保温",
    /**
     * label: 外保温
     * desc: --
     * value: 外保温
     */
    外保温: "外保温",
    /**
     * label: 拖膜
     * desc: --
     * value: 拖膜
     */
    拖膜: "拖膜",
    /**
     * label: 水泵
     * desc: --
     * value: 水泵
     */
    水泵: "水泵",
    /**
     * label: 搅拌机
     * desc: --
     * value: 搅拌机
     */
    搅拌机: "搅拌机",
    /**
     * label: 顶开窗
     * desc: --
     * value: 顶开窗
     */
    顶开窗: "顶开窗",
    /**
     * label: 侧开窗
     * desc: --
     * value: 侧开窗
     */
    侧开窗: "侧开窗",
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
{"name": "smart_agriculture", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@zny/root-container": "^0.0.4", "@zny/vue-map": "^0.0.59", "autofit.js": "^3.2.8", "axios": "^0.27.2", "core-js": "^3.6.5", "echarts": "^5.3.3", "echarts-gl": "^2.0.9", "element-ui": "2.15.8", "ezuikit-js": "^0.6.3", "file-saver": "^2.0.5", "jquery": "^3.6.1", "protobufjs": "^7.1.0", "qrcodejs2": "^0.0.2", "swiper": "^6.8.4", "video.js": "^7.20.2", "vue": "^2.6.11", "vue-mini-player": "^0.2.1", "vue-qr": "^4.0.9", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.4.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.6", "@vue/cli-plugin-eslint": "~4.4.6", "@vue/cli-plugin-router": "~4.4.6", "@vue/cli-plugin-vuex": "~4.4.6", "@vue/cli-service": "~4.4.6", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "prettier": "^1.19.1", "script-loader": "^0.7.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
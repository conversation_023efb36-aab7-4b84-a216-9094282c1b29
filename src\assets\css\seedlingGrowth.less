.seedlingGrowth{
  width: 1532px;
  height: 922px;
  display: flex;
  .title{
    height: 39px;
    line-height: 39px;
    position: absolute;
    top: 0;
    img{
      vertical-align: middle;
      margin: 0 7px 0 16px;
    }
    span{
      font-size: 16px;
      font-weight: bold;
      color: #DFEEF3;
    }
  }
  .bottom_left{
    position: absolute;
    left: -8px;
    bottom: -11px;
  }
  .bottom_right{
    position: absolute;
    right: -8px;
    bottom: -11px;
  }
  &_left{
    width: 461px;
    height: 922px;
    &_data{
      width: 100%;
      height: 100%;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 894px;
        position: absolute;
        bottom: 0;
        background: url(../image/centralControlPlatform/bg5.png) no-repeat 100% center;
        &_search{
          width: 180px;
          height: 32px;
          margin-top: 30px;
          margin-left: 16px;
        }
        &_image{
          width: 100%;
          height: 262px;
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .image_arrow_left{
            width: 33px;
            height: 47px;
            margin-left: 15px;
          }
          .image_arrow_right{
            width: 33px;
            height: 47px;
            margin-right: 15px;
          }
          .image_con{
            width: 340px;
            height: 262px;
            img{
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
        &_photoTime{
          height: 14px;
          margin: 17px 0 10px 62px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #DFEEF3;
        }
        &_info{
          width: 401px;
          height: 411px;
          overflow-y: scroll;
          padding: 15px 30px 15px 30px;
          .infoList{
            width: 100%;
            display: flex;
            &_item{
              width: 45%;
              display: flex;
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #DFEEF3;
              line-height: 20px;
              &_name{
                width: 50%;
                text-align: right;
              }
              &_value{
                width: 50%;
              }
            }
            &_item:first-child{
              width: 55%;
            }
            &_item2{
              width: 100%!important;
              &_name{
                width: 28%;
                text-align: right;
              }
              &_value{
                width: 72%;
              }
            }
          }
          .info_line{
            width: 100%;
            height: 1px;
            margin: 10px 0;
            background: radial-gradient(circle, #00F4FD, rgba(0,244,253,0));
          }
        }
        &_button{
          .handleBox{
            height: 42px;
            //margin: 24px 0 20px 0;
            display: flex;
            justify-content: center;
            .handleBox_item{
              width: 160px;
              height: 42px;
              border-radius: 2px 4px 4px 4px;
              .el-button{
                padding: 13px 48px;
              }
            }
          }
        }
        &_tip{
          width: 401px;
          padding: 10px 45px 24px 45px;
          display: flex;
          &_image{
            width: 17px;
            height: 17px;
          }
          &_text{
            width: 346px;
            height: 34px;
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FEFFFF;
            line-height: 20px;
            opacity: 0.6;
            margin-left: 9px;
          }
        }
      }
    }
  }
  &_right{
    width: 1050px;
    height: 922px;
    margin-left: 21px;
    position: relative;
    &_title{
      width: 1050px;
      background: url(../image/centralControlPlatform/title.png) no-repeat 100% center;
    }
    &_btn{
      position: absolute;
      top: 85px;
      right: 42px;
    }
    &_con{
      width: 1050px;
      height: 893px;
      position: absolute;
      bottom: 0;
      background: url(../image/centralControlPlatform/bg.png) no-repeat 100% center;
      .seedlingGrowth_twoDimensionalMap{
        width: 950px;
        height: 545px;
        background: url(../image/monitoringCenter/ploat.png) no-repeat;
        background-size: 100%;
        margin: 231px 42px 117px 58px;
      }
    }
  }
}
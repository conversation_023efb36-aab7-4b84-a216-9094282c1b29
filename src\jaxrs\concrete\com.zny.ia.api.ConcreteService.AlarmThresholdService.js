/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AlarmThresholdService = {
    /**
     * 重置阈值
     * @param {*} cropId 作物id
     * @param {*} growStage 作物成长阶段
     * @returns Promise 
     */
    'resetAlarmThreshold': function (cropId, growStage) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '81d10456517507d15929063361fb62554cc99485', {cropId, growStage})
            : execute(concreteModuleName, `/AlarmThresholdService/resetAlarmThreshold`, 'json', 'POST', { cropId, growStage });
    }, 
    /**
     * 设置了阈值的作物列表
     * @returns Promise 
     */
    'listAlarmThresholdCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '637c3d8a3d0a9b205e6ea956ed984987803bbc46')
            : execute(concreteModuleName, `/AlarmThresholdService/listAlarmThresholdCrop`, 'json', 'GET');
    }, 
    /**
     * 根据作物获取对应生长阶段
     * @param {*} cropId 作物id
     * @returns Promise 
     */
    'listGrowStageByCrop': function (cropId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '51edbc98e28f647799969c9727b96f72a8e81ddc', cropId)
            : execute(concreteModuleName, `/AlarmThresholdService/listGrowStageByCrop`, 'json', 'POST', { cropId });
    }, 
    /**
     * 阈值详情
     * @param {*} cropId 作物id
     * @param {*} growStage 作物成长阶段
     * @returns Promise 
     */
    'detailAlarmThresholdInfo': function (cropId, growStage) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'cdeac665faf03877a4466fcef59cd564a3e5f6a1', {cropId, growStage})
            : execute(concreteModuleName, `/AlarmThresholdService/detailAlarmThresholdInfo`, 'json', 'POST', { cropId, growStage });
    }, 
    /**
     * 编辑阈值
     * @param {*} po 
     * @returns Promise 
     */
    'updateAlarmThreshold': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8f7ff7f13631afc4af307e6866d03602a139389a', po)
            : execute(concreteModuleName, `/AlarmThresholdService/alarmThreshold`, 'json', 'PUT', po);
    }
}

export default AlarmThresholdService

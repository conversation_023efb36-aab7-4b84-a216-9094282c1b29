/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const EquipmentCtrlStateService = {
    /**
     * 根据设备ID查看设备控制项状态
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findEquipmentCtrlStateListByEquipmentId': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'eb4b1b89f246aaee528666bb6ac897b1e8ba35df', equipmentId)
            : execute(concreteModuleName, `/EquipmentCtrlStateService/findEquipmentCtrlStateListByEquipmentId`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 根据设备ID和控制项获取一段时间的变化记录
     * @param {*} equipmentId 设备id
     * @param {*} item 控制项
     * @param {*} startTime 开始时间
     * @param {*} endTime 结束时间
     * @returns Promise 
     */
    'findEquipmentCtrlStateDynamicList': function (equipmentId, item, startTime, endTime) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '444043649584a44a521d863e9d60087be198464d', {equipmentId, item, startTime, endTime})
            : execute(concreteModuleName, `/EquipmentCtrlStateService/findEquipmentCtrlStateDynamicList`, 'json', 'POST', { equipmentId, item, startTime, endTime });
    }, 
    /**
     * 根据区域ID获取设备控制项
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findEquipmentCtrlStateListByAreaId': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7f265e12652375484e19a78dad4a836519241534', areaId)
            : execute(concreteModuleName, `/EquipmentCtrlStateService/findEquipmentCtrlStateListByAreaId`, 'json', 'POST', { areaId });
    }
}

export default EquipmentCtrlStateService

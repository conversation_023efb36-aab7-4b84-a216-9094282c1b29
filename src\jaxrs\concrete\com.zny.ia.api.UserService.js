/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const UserService = {
    /**
     * 权限详情
     * @param {*} id 权限id
     * @returns Promise 
     */
    'detailPermission': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f670a75b49075c2835d1c7df7c93f18af3293978', id)
            : execute(concreteModuleName, `/UserService/detailPermission`, 'json', 'POST', { id });
    }, 
    /**
     * 查看用户详情
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'findUserInfo': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '169b698883bb97bd174a02066da343f10dc231dd', userId)
            : execute(concreteModuleName, `/UserService/findUserInfo`, 'json', 'POST', { userId });
    }, 
    /**
     * 删除角色
     * @param {*} roleId 角色Id
     * @returns Promise 
     */
    'removeRole': function (roleId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '38667a0cab0e67284c25393f8561482076dea0a3', roleId)
            : execute(concreteModuleName, `/UserService/removeRole`, 'json', 'POST', { roleId });
    }, 
    /**
     * 删除用户
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'userDelete': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2af4eba664516bdc3f92635ac9c1e81aeb72c484', userId)
            : execute(concreteModuleName, `/UserService/userDelete`, 'json', 'POST', { userId });
    }, 
    /**
     * 编辑用户
     * @param {*} userId 用户id
     * @param {*} po 
     * @returns Promise 
     */
    'userUpdate': function (userId, po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9aa960a1bead929a22beb4cc6cbe926f28d7562f', {userId, po})
            : execute(concreteModuleName, `/UserService/userUpdate`, 'json', 'POST', { userId, po });
    }, 
    /**
     * 删除权限
     * @param {*} id 权限id
     * @returns Promise 
     */
    'deletePermission': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '259cc334c8e78f3dd3bc19852d5f202acc0b7d29', id)
            : execute(concreteModuleName, `/UserService/permission`, 'json', 'DELETE', { id });
    }, 
    /**
     * 添加权限
     * @param {*} po 
     * @returns Promise 
     */
    'addPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'bc663cf9a5a9911a370cf1e877ace16132ff1cfd', po)
            : execute(concreteModuleName, `/UserService/addPermission`, 'json', 'POST', po);
    }, 
    /**
     * 修改角色信息
     * @param {*} updateRoleInfoPo 
     * @returns Promise 
     */
    'changeRoleInfoByRoleId': function (updateRoleInfoPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c571f972f0c3b06f84358eaad4dfedc7d7250c00', updateRoleInfoPo)
            : execute(concreteModuleName, `/UserService/changeRoleInfoByRoleId`, 'json', 'POST', updateRoleInfoPo);
    }, 
    /**
     * 添加用户
     * @param {*} po 
     * @returns Promise 
     */
    'userAdd': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a1bf88fcf04687dd9dc9a8a15c277ce81fbe1147', po)
            : execute(concreteModuleName, `/UserService/userAdd`, 'json', 'POST', po);
    }, 
    /**
     * 查询角色的信息列表
     * @param {*} pageNum 第几页 - 从1开始
     * @param {*} pageSize 每页多少条数据
     * @returns Promise 
     */
    'roleInfoList': function (pageNum, pageSize) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5fd3f9c55c90f1b0f8b4b1444e7207f796d9b012', {pageNum, pageSize})
            : execute(concreteModuleName, `/UserService/roleInfoList`, 'json', 'POST', { pageNum, pageSize });
    }, 
    /**
     * 查看用户列表
     * @param {*} po 
     * @returns Promise 
     */
    'userList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1f44b8f0f4facfebc592525d46f0ab091cd12877', po)
            : execute(concreteModuleName, `/UserService/userList`, 'json', 'POST', po);
    }, 
    /**
     * 重置密码
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'passwordReset': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'eb3b9750f40e6b8a025811b263230c86858b923f', userId)
            : execute(concreteModuleName, `/UserService/passwordReset`, 'json', 'POST', { userId });
    }, 
    /**
     * 权限列表
     * @param {*} po 
     * @returns Promise 
     */
    'listPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9617b84333ef606c625dc11a3a7cb25fe5911ec2', po)
            : execute(concreteModuleName, `/UserService/listPermission`, 'json', 'POST', po);
    }, 
    /**
     * 编辑权限
     * @param {*} po 
     * @returns Promise 
     */
    'updatePermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '36a6b20bc55fec72817a5be4cd9730adb5ace73b', po)
            : execute(concreteModuleName, `/UserService/permission`, 'json', 'PUT', po);
    }, 
    /**
     * 添加角色
     * @param {*} addRoleInfoPo 
     * @returns Promise 
     */
    'addRole': function (addRoleInfoPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9bca90bd14c3873c9ec02c38adc4aa5f18037b83', addRoleInfoPo)
            : execute(concreteModuleName, `/UserService/addRole`, 'json', 'POST', addRoleInfoPo);
    }, 
    /**
     * 根据角色ID查询角色的信息
     * @param {*} roleId 角色Id
     * @returns Promise 
     */
    'roleInfoByRoleId': function (roleId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c201d766779e61589ff72907f175cf5f9598f9df', roleId)
            : execute(concreteModuleName, `/UserService/roleInfoByRoleId`, 'json', 'POST', { roleId });
    }
}

export default UserService

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 待完成
     * desc: --
     * value: 0
     */
    待完成: 0,
    /**
     * label: 未完成
     * desc: --
     * value: 1
     */
    未完成: 1,
    /**
     * label: 已完成
     * desc: --
     * value: 2
     */
    已完成: 2,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
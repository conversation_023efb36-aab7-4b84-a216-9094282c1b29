<template>
    <div class="InsectSituationOfHistoryDialog dialogStyle">
        <el-dialog
            title="虫情历史记录"
            :visible.sync="dialogVisible"
            width="55%"
            center
            :show-close="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @open="dialogOpen"
        >
            <div class="line"></div>
            <div class="close" @click="dialogVisible = false">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="handleBox">
                <div class="handleBox_item formStyle">
                    <el-select v-model="equipmentId" clearable placeholder="请选择设备" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in epData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-select v-model="from" clearable placeholder="请选择数据来源" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in fromData"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-date-picker
                        v-model="time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        popper-class="datePickerStyle"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </div>
                <div class="handleBox_item searchButtonStyle">
                    <el-button @click="search">查询</el-button>
                </div>
            </div>
            <div class="tableBox tableStyle">
                <el-table :data="tableData" border style="width: 100%" height="405px">
                    <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                    <el-table-column align="center" label="时间">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.reportUser == ''">
                                {{ scoped.row.createTime }}
                            </div>
                            <div v-else>
                                <el-tooltip popper-class="tooltipStyle" placement="bottom">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.reportUser }}</div>
                                    </div>
                                    <div style="color:#18BBFF">
                                        {{ scoped.row.createTime }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="种类">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.reportUser == ''">
                                {{ scoped.row.kind }}
                            </div>
                            <div v-else>
                                <el-tooltip popper-class="tooltipStyle" placement="bottom">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.reportUser }}</div>
                                    </div>
                                    <div style="color:#18BBFF">
                                        {{ scoped.row.kind }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="数量">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.reportUser == ''">
                                {{ scoped.row.count }}
                            </div>
                            <div v-else>
                                <el-tooltip popper-class="tooltipStyle" placement="bottom">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.reportUser }}</div>
                                    </div>
                                    <div style="color:#18BBFF">
                                        {{ scoped.row.count }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="图片">
                        <template slot-scope="scoped">
                            <div>
                                <img class="img" :src="scoped.row.picUrl" alt="" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import WormCaseService from '../../jaxrs/concrete/com.zny.ia.api.WormCaseService.js'
import data from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$数据来源.js'
export default {
    data() {
        return {
            dialogVisible: false,
            areaId: '',
            equipmentId: '',
            epData: [{ label: '设备1', value: 1 }],
            from: 0,
            fromData: data._toArray(),
            time: '',
            tableData: [],
        }
    },
    mounted() {
        this.listen('InsectSituationOfHistoryDialogOpen', areaId => {
            this.areaId = areaId
            this.dialogVisible = true
        })
    },
    methods: {
        // 弹窗打开
        dialogOpen() {
            this.getWormCaseHistory()
        },
        // 搜索
        search() {
            if (this.from == '') {
                this.from = null
            }
            this.getWormCaseHistory()
        },
        // 获取虫情历史记录
        getWormCaseHistory() {
            let po = {
                num: 1,
                pageSize: 1000,
                condition: {
                    areaId: this.areaId,
                    equipmentId: this.equipmentId,
                    from: this.from,
                    startTime: this.time.length == 0 ? '' : this.time[0],
                    endTime: this.time.length == 0 ? '' : this.time[1],
                },
            }
            WormCaseService.wormCaseHistory(po).then(res => {
                this.tableData = res.list
            })
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
@import '../../assets/css/Dialog/InsectSituationOfHistoryDialog.less';
</style>

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const BatchCommonService = {
    /**
     * 获取自己管理区域下所有有收获批次的批次信息
     * @returns Promise 
     */
    'haveHarvestBatch': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6a87651372a8b5453e1d4a60615ae3757325e869')
            : execute(concreteModuleName, `/BatchCommonService/haveHarvestBatch`, 'json', 'GET');
    }
}

export default BatchCommonService

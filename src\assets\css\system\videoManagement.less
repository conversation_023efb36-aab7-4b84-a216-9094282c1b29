.videoManagement{
  width: 100%;
  height: 97%;
  &_con{
    width: 96%;
    height: 98%;
    background: #FFFFFF;
    border-radius: 8px;
    margin: 20px 0 0 32px;
    .handleBox,.handleBox1{
      display: flex;
      align-items: center;
      &_item{
        height: 40px;
        margin-top: 24px;
        margin-right: 24px;
      }
      &_item:nth-child(1){
        margin-left: 24px;
      }
      &_item:nth-child(1),
      &_item:nth-child(2){
        width: 270px;
      }
      &_item:nth-child(4){
        width: 80px;
      }
    }
    .handleBox1{
      .handleBox_btn{
        margin-top: 24px;
      }
    }
    .tableBox{
      width: 97%;
      height: 83%;
      margin: auto;
      margin-top: 24px;
      .cell{
        .viewData{
          font-size: 15px;
          font-weight: 400;
          color: #007EFF;
          cursor: pointer;
          margin: 0 8px;
          a{
            color: #007EFF;
            text-decoration: none;
          }
        }
      }
    }
    .pageBox{
      width: 97%;
      height: 5%;
      margin-top: 10px;
      text-align: center;
    }
  }
  .el-dialog{
    height: 700px;
    margin-top: 15vh !important;
    .el-dialog__body{
      height: 645px;
      padding: 0 !important;
      .videoBox{
        width: 100%;
        height: 645px;
        .videoDetails{
          width: 100%;
          height: 100%;
          #myVideo{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
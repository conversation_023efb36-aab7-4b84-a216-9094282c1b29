/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const HomePageService = {
    /**
     * 报警汇总
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'alarmCollect': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '424e61f933c65988b2fea19697a67813521e315c', areaId)
            : execute(concreteModuleName, `/HomePageService/alarmCollect`, 'json', 'POST', { areaId });
    }, 
    /**
     * 区域详情
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'areaDetail': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c8bc439d22556c0de784c84afedb28ea4b4637e0', areaId)
            : execute(concreteModuleName, `/HomePageService/areaDetail`, 'json', 'POST', { areaId });
    }, 
    /**
     * 农作物溯源信息
     * @returns Promise 
     */
    'homeCropSource': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7c0767bf9fd14cb794e1bfcf0ca6ecdeedf83141')
            : execute(concreteModuleName, `/HomePageService/homeCropSource`, 'json', 'GET');
    }, 
    /**
     * 农作物实时价格
     * @returns Promise 
     */
    'homeCropPrice': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c392df62630a4635b85a722d4a77acb717ed8f8e')
            : execute(concreteModuleName, `/HomePageService/homeCropPrice`, 'json', 'GET');
    }, 
    /**
     * 获取区域信息 （作物，边框，中心位置）
     * @returns Promise 
     */
    'findAreaCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5d2f5fce5e5f55ba7288efc9a1f0d8c41ad54d43')
            : execute(concreteModuleName, `/HomePageService/findAreaCrop`, 'json', 'GET');
    }, 
    /**
     * 电商销售占比
     * @returns Promise 
     */
    'homeMarketProportion': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ee33401e355831073dd32cc550207977a600b91a')
            : execute(concreteModuleName, `/HomePageService/homeMarketProportion`, 'json', 'GET');
    }, 
    /**
     * 根据区域ID获取最近30条变化记录
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findEquipmentCtrlStateDynamicTop30List': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '69e4383de0a0e8d0388e3d19f6b5ec8f739af9c4', areaId)
            : execute(concreteModuleName, `/HomePageService/findEquipmentCtrlStateDynamicTop30List`, 'json', 'POST', { areaId });
    }, 
    /**
     * 智能物联数据
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'homeUnionAIData': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2292d3bb93dc52a889db29f8ebd78fa964b04a6b', areaId)
            : execute(concreteModuleName, `/HomePageService/homeUnionAIData`, 'json', 'POST', { areaId });
    }
}

export default HomePageService

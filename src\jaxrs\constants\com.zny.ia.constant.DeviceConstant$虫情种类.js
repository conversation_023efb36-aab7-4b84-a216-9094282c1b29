import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 水虿
     * desc: --
     * value: 0
     */
    水虿: 0,
    /**
     * label: 粉蝶
     * desc: --
     * value: 1
     */
    粉蝶: 1,
    /**
     * label: 蜻蜓
     * desc: --
     * value: 2
     */
    蜻蜓: 2,
    /**
     * label: 稻纵卷
     * desc: --
     * value: 3
     */
    稻纵卷: 3,
    /**
     * label: 稻飞虱
     * desc: --
     * value: 4
     */
    稻飞虱: 4,
    /**
     * label: 木虱
     * desc: --
     * value: 5
     */
    木虱: 5,
    /**
     * label: 蚂蚁
     * desc: --
     * value: 6
     */
    蚂蚁: 6,
    /**
     * label: 北京油葫芦
     * desc: --
     * value: 7
     */
    北京油葫芦: 7,
    /**
     * label: 青尺蛾
     * desc: --
     * value: 8
     */
    青尺蛾: 8,
    /**
     * label: 斑娥
     * desc: --
     * value: 9
     */
    斑娥: 9,
    /**
     * label: 斑衣蜡蝉
     * desc: --
     * value: 10
     */
    斑衣蜡蝉: 10,
    /**
     * label: 螳螂
     * desc: --
     * value: 11
     */
    螳螂: 11,
    /**
     * label: 美国白蛾
     * desc: --
     * value: 12
     */
    美国白蛾: 12,
    /**
     * label: 眉纹天蚕蛾
     * desc: --
     * value: 13
     */
    眉纹天蚕蛾: 13,
    /**
     * label: 草蜻蛉
     * desc: --
     * value: 14
     */
    草蜻蛉: 14,
    /**
     * label: 粘虫
     * desc: --
     * value: 15
     */
    粘虫: 15,
    /**
     * label: 蝶角蛉
     * desc: --
     * value: 16
     */
    蝶角蛉: 16,
    /**
     * label: 菜青虫
     * desc: --
     * value: 17
     */
    菜青虫: 17,
    /**
     * label: 油蝉
     * desc: --
     * value: 18
     */
    油蝉: 18,
    /**
     * label: 羽娥
     * desc: --
     * value: 19
     */
    羽娥: 19,
    /**
     * label: 甜菜夜蛾
     * desc: --
     * value: 20
     */
    甜菜夜蛾: 20,
    /**
     * label: 果蝇
     * desc: --
     * value: 21
     */
    果蝇: 21,
    /**
     * label: 飞蚁
     * desc: --
     * value: 22
     */
    飞蚁: 22,
    /**
     * label: 独角仙
     * desc: --
     * value: 23
     */
    独角仙: 23,
    /**
     * label: 麻步甲
     * desc: --
     * value: 24
     */
    麻步甲: 24,
    /**
     * label: 龙虱
     * desc: --
     * value: 25
     */
    龙虱: 25,
    /**
     * label: 甘薯天蛾
     * desc: --
     * value: 26
     */
    甘薯天蛾: 26,
    /**
     * label: 呼
     * desc: --
     * value: 27
     */
    呼: 27,
    /**
     * label: 烟草天蛾
     * desc: --
     * value: 28
     */
    烟草天蛾: 28,
    /**
     * label: 斑鱼蛉
     * desc: --
     * value: 29
     */
    斑鱼蛉: 29,
    /**
     * label: 石蝇
     * desc: --
     * value: 30
     */
    石蝇: 30,
    /**
     * label: 蒙古寒蝉
     * desc: --
     * value: 31
     */
    蒙古寒蝉: 31,
    /**
     * label: 虻
     * desc: --
     * value: 32
     */
    虻: 32,
    /**
     * label: 犀金龟
     * desc: --
     * value: 33
     */
    犀金龟: 33,
    /**
     * label: 苹果蠹蛾
     * desc: --
     * value: 34
     */
    苹果蠹蛾: 34,
    /**
     * label: 叩头虫
     * desc: --
     * value: 35
     */
    叩头虫: 35,
    /**
     * label: 隐翅虫
     * desc: --
     * value: 36
     */
    隐翅虫: 36,
    /**
     * label: 蓟马
     * desc: --
     * value: 37
     */
    蓟马: 37,
    /**
     * label: 蜂锰
     * desc: --
     * value: 38
     */
    蜂锰: 38,
    /**
     * label: 角蝉
     * desc: --
     * value: 39
     */
    角蝉: 39,
    /**
     * label: 锹形虫
     * desc: --
     * value: 40
     */
    锹形虫: 40,
    /**
     * label: 斗蟋
     * desc: --
     * value: 41
     */
    斗蟋: 41,
    /**
     * label: 卷叶蛾
     * desc: --
     * value: 42
     */
    卷叶蛾: 42,
    /**
     * label: 地老虎
     * desc: --
     * value: 43
     */
    地老虎: 43,
    /**
     * label: 犀牛甲虫
     * desc: --
     * value: 44
     */
    犀牛甲虫: 44,
    /**
     * label: 草铃
     * desc: --
     * value: 45
     */
    草铃: 45,
    /**
     * label: 马蛹
     * desc: --
     * value: 46
     */
    马蛹: 46,
    /**
     * label: 灯蛾
     * desc: --
     * value: 47
     */
    灯蛾: 47,
    /**
     * label: 甘薯蚁象
     * desc: --
     * value: 48
     */
    甘薯蚁象: 48,
    /**
     * label: 射炮步甲
     * desc: --
     * value: 49
     */
    射炮步甲: 49,
    /**
     * label: 舞毒蛾
     * desc: --
     * value: 50
     */
    舞毒蛾: 50,
    /**
     * label: 萝蚊
     * desc: --
     * value: 51
     */
    萝蚊: 51,
    /**
     * label: 黑蚌蝉
     * desc: --
     * value: 52
     */
    黑蚌蝉: 52,
    /**
     * label: 蚕蛾
     * desc: --
     * value: 53
     */
    蚕蛾: 53,
    /**
     * label: 稻三化螟
     * desc: --
     * value: 54
     */
    稻三化螟: 54,
    /**
     * label: 稻二化螟
     * desc: --
     * value: 55
     */
    稻二化螟: 55,
    /**
     * label: 竹节虫
     * desc: --
     * value: 56
     */
    竹节虫: 56,
    /**
     * label: 枯叶蛾
     * desc: --
     * value: 57
     */
    枯叶蛾: 57,
    /**
     * label: 长脚蚊
     * desc: --
     * value: 58
     */
    长脚蚊: 58,
    /**
     * label: 蟪蛄
     * desc: --
     * value: 59
     */
    蟪蛄: 59,
    /**
     * label: 中华婪步甲
     * desc: --
     * value: 60
     */
    中华婪步甲: 60,
    /**
     * label: 蝼蛄
     * desc: --
     * value: 61
     */
    蝼蛄: 61,
    /**
     * label: 田鳖
     * desc: --
     * value: 62
     */
    田鳖: 62,
    /**
     * label: 镬嫂
     * desc: --
     * value: 63
     */
    镬嫂: 63,
    /**
     * label: 蚂蜂
     * desc: --
     * value: 64
     */
    蚂蜂: 64,
    /**
     * label: 蝉虫
     * desc: --
     * value: 65
     */
    蝉虫: 65,
    /**
     * label: 飞虱
     * desc: --
     * value: 66
     */
    飞虱: 66,
    /**
     * label: 磕头虫
     * desc: --
     * value: 67
     */
    磕头虫: 67,
    /**
     * label: 鹿蛾
     * desc: --
     * value: 68
     */
    鹿蛾: 68,
    /**
     * label: 尺娥
     * desc: --
     * value: 69
     */
    尺娥: 69,
    /**
     * label: 蜷
     * desc: --
     * value: 70
     */
    蜷: 70,
    /**
     * label: 夜蛾
     * desc: --
     * value: 71
     */
    夜蛾: 71,
    /**
     * label: 水黾
     * desc: --
     * value: 72
     */
    水黾: 72,
    /**
     * label: 硬壳虫
     * desc: --
     * value: 73
     */
    硬壳虫: 73,
    /**
     * label: 蠹虫
     * desc: --
     * value: 74
     */
    蠹虫: 74,
    /**
     * label: 飞蚂蚁
     * desc: --
     * value: 75
     */
    飞蚂蚁: 75,
    /**
     * label: 衣蛾
     * desc: --
     * value: 76
     */
    衣蛾: 76,
    /**
     * label: 螟蛾
     * desc: --
     * value: 77
     */
    螟蛾: 77,
    /**
     * label: 瓢虫
     * desc: --
     * value: 78
     */
    瓢虫: 78,
    /**
     * label: 草蛉虫
     * desc: --
     * value: 79
     */
    草蛉虫: 79,
    /**
     * label: 金龟子
     * desc: --
     * value: 80
     */
    金龟子: 80,
    /**
     * label: 拟步甲虫
     * desc: --
     * value: 81
     */
    拟步甲虫: 81,
    /**
     * label: 蚤蝇
     * desc: --
     * value: 82
     */
    蚤蝇: 82,
    /**
     * label: 蛾蚋
     * desc: --
     * value: 83
     */
    蛾蚋: 83,
    /**
     * label: 蜜蜂
     * desc: --
     * value: 84
     */
    蜜蜂: 84,
    /**
     * label: 蠓虫
     * desc: --
     * value: 85
     */
    蠓虫: 85,
    /**
     * label: 蚊子
     * desc: --
     * value: 86
     */
    蚊子: 86,
    /**
     * label: 苍蝇
     * desc: --
     * value: 87
     */
    苍蝇: 87,
    /**
     * label: 红蜘蛛
     * desc: --
     * value: 88
     */
    红蜘蛛: 88,
    /**
     * label: 红蚁
     * desc: --
     * value: 89
     */
    红蚁: 89,
    /**
     * label: 白蚁
     * desc: --
     * value: 90
     */
    白蚁: 90,
    /**
     * label: 蝗虫
     * desc: --
     * value: 91
     */
    蝗虫: 91,
    /**
     * label: 白蛾
     * desc: --
     * value: 92
     */
    白蛾: 92,
    /**
     * label: 玉米螟
     * desc: --
     * value: 93
     */
    玉米螟: 93,
    /**
     * label: 棉铃虫
     * desc: --
     * value: 94
     */
    棉铃虫: 94,
    /**
     * label: 嫜螂
     * desc: --
     * value: 95
     */
    嫜螂: 95,
    /**
     * label: 飞蛾
     * desc: --
     * value: 96
     */
    飞蛾: 96,
    /**
     * label: 蚜虫
     * desc: --
     * value: 97
     */
    蚜虫: 97,
    /**
     * label: 介壳虫
     * desc: --
     * value: 98
     */
    介壳虫: 98,
    /**
     * label: 天牛
     * desc: --
     * value: 99
     */
    天牛: 99,
    /**
     * label: 小灰蛾
     * desc: --
     * value: 100
     */
    小灰蛾: 100,
    /**
     * label: 小白蛾
     * desc: --
     * value: 101
     */
    小白蛾: 101,
    /**
     * label: 毛毛虫
     * desc: --
     * value: 102
     */
    毛毛虫: 102,
    /**
     * label: 七星瓢虫
     * desc: --
     * value: 103
     */
    七星瓢虫: 103,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
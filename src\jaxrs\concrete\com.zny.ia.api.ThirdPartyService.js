/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const ThirdPartyService = {
    /**
     * 第三方登录
     * @param {*} keyId 第三方keyId，由我方分配
     * @param {*} timestamp 防重放时间戳 - 自1970-1-1以来的毫秒数
     * @param {*} signature 签名值 - 签名方式，拼接keyId=....&timestamp=....&secretKey=.... 对其md5后转为16进制字符串
     * @returns Promise 
     */
    'sso': function (keyId, timestamp, signature) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '74b828ccade063c1d46fe8d1b425c164dad5ba67', {keyId, timestamp, signature})
            : execute(concreteModuleName, `/ThirdPartService/sso`, 'json', 'POST', { keyId, timestamp, signature });
    }
}

export default ThirdPartyService

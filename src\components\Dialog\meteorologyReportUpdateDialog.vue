<template>
  <div class="meteorologyReportUpdateDialog dialogStyle">
    <el-dialog
      title="气象数据汇报"
      width="40%"
      :visible.sync="dialogVisible"
      center=""
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="closeDialog('form')">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="form_List">
        <el-form ref="form" :model="form" :rules="rules" label-width="130px">
          <div class="formList">
            <el-form-item label="信息项">
              <div>当前监测数值</div>
            </el-form-item>
            <el-form-item label="环境温度：" prop="temperature" class="formStyle">
              <el-input v-model="form.temperature">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  ℃
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="降雨量：" prop="precipitation" class="formStyle">
              <el-input v-model="form.precipitation">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  mm
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="瞬时风向：" class="formStyle">
              <el-select 
              v-model="form.windDirection" 
              clearable 
              placeholder="请选择瞬时风向"
              popper-class="selectStyle_list">
                <el-option
                  v-for="item in windDirection"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="光照度：" prop="illuminance" class="formStyle">
              <el-input v-model="form.illuminance">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  mm
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="日照小时数：" prop="sunshineHour" class="formStyle">
              <el-input v-model="form.sunshineHour">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  h
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="PM2.5：" prop="pm2_5" class="formStyle">
              <el-input v-model="form.pm2_5">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  μg/m³
                </i>
              </el-input>
            </el-form-item>
          </div>
          <div class="formList">
            <el-form-item label="信息项">
              <div>当前监测数值</div>
            </el-form-item>
            <el-form-item label="环境湿度：" prop="humidity" class="formStyle">
              <el-input v-model="form.humidity">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  %
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="风力等级：" class="formStyle">
              <el-select 
              v-model="form.windLevel" 
              clearable 
              placeholder="请选择风力等级"
              popper-class="selectStyle_list">
                <el-option
                  v-for="item in windLevel"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="CO₂浓度：" prop="carbonDioxide" class="formStyle">
              <el-input v-model="form.carbonDioxide">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  ppm
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="太阳辐射强度：" prop="solarRadiation" class="formStyle">
              <el-input v-model="form.solarRadiation">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  W/㎡
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="大气压：" prop="barometricPressure" class="formStyle">
              <el-input v-model="form.barometricPressure">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  pha
                </i>
              </el-input>
            </el-form-item>
            <el-form-item label="汇报时间：" prop="reportTime" class="formStyle">
              <el-date-picker
                v-model="form.reportTime"
                type="datetime"
                placeholder="选择日期时间"
                popper-class='datePickerStyle'
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              <!-- <el-date-picker
                v-model="form.reportTime"
                type="date"
                placeholder="选择日期"
                popper-class='datePickerStyle'
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker> -->
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="searchButtonStyle">
          <el-button @click="cancel('form')">取消</el-button>
        </div>
        <div class="submitButtonStyle">
          <el-button @click="submit('form')">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import WeatherService from '../../jaxrs/concrete/com.zny.ia.api.WeatherService.js';
import windDirectionData from '../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$风向信息.js';
export default {
  data(){
    return{
      dialogVisible:false,
      windDirection:windDirectionData._toArray(),
      windLevel:[
        {label:"1级",value:1},
        {label:"2级",value:2},
        {label:"3级",value:3},
        {label:"4级",value:4},
        {label:"5级",value:5},
        {label:"6级",value:6},
        {label:"7级",value:7},
        {label:"8级",value:8},
        {label:"9级",value:9},
        {label:"10级",value:10},
        {label:"11级",value:11},
        {label:"12级",value:12},
        {label:"13级",value:13},
        {label:"14级",value:14},
        {label:"15级",value:15},
        {label:"16级",value:16},
        {label:"17级",value:17},
      ],
      form: {
        areaId:"",
        temperature: '',
        precipitation: '',
        windDirection: '',
        illuminance: '',
        sunshineHour: '',
        pm2_5: '',
        humidity: '',
        windLevel: '',
        carbonDioxide:'',
        solarRadiation:'',
        barometricPressure:'',
        reportTime:'',
      },
      rules:{
        temperature: [
          // { required: true, message: '请输入环境温度', trigger: 'blur' },
          {
            // pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        precipitation: [
          // { required: true, message: '请输入降雨量', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        windDirection: [
          { required: true, message: '请选择瞬时风向', trigger: 'change' },
        ],
        illuminance: [
          // { required: true, message: '请输入光照度', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)|(^[1-9]([0-9]+)?(\.[0-9]{1,3})?$)/,
            message: '必须为数字值且保留三位小数',
          },
        ],
        sunshineHour: [
          // { required: true, message: '请输入日照小时数', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        pm2_5: [
          // { required: true, message: '请输入PM2.5', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        humidity: [
          // { required: true, message: '请输入环境湿度', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        windLevel: [
          { required: true, message: '请选择风力等级', trigger: 'change' },
        ],
        carbonDioxide: [
          // { required: true, message: '请输入CO₂浓度', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        solarRadiation: [
          // { required: true, message: '请输入太阳辐射强度', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        barometricPressure: [
          // { required: true, message: '请输入大气压', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        reportTime: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
      },
    }
  },
  mounted(){
    this.listen('meteorologyReportUpdateDialogOpen', (areaId) => {
      this.form.areaId=areaId
      this.dialogVisible=true
    })
  },
  methods:{
    // 关闭弹窗
    closeDialog(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
    },
    // 取消
    cancel(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
    },
    // 确定提交
    submit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let po={
            areaId:this.form.areaId,
            temperature:this.form.temperature,
            precipitation:this.form.precipitation,
            windDirection: this.form.windDirection==''?null:this.form.windDirection,
            illuminance: this.form.illuminance,
            sunshineHour: this.form.sunshineHour,
            pm2_5: this.form.pm2_5,
            humidity: this.form.humidity,
            windLevel:this.form.windLevel,
            carbonDioxide:this.form.carbonDioxide,
            solarRadiation:this.form.solarRadiation,
            barometricPressure:this.form.barometricPressure,
            reportTime:this.form.reportTime,
          }
          WeatherService.weatherReport(po)
          .then(res=>{
            // console.log(res);
            that.$refs[formName].resetFields();
            that.dialogVisible=false
            that.$message({
              message: '数据上报成功',
              type: 'success'
            });
            that.zEmit('meteorologyUpdateSuccess')
          }).catch(()=>{
            that.$message.error('数据上报失败,请重试!');
          })
        }
      })
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologyReportUpdateDialog.less';
</style>
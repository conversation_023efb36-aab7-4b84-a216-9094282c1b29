<template>
  <div class="smartIOTDataSwiper">
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
          <div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon6.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.illuminance==null">--</div>
            <div v-else>{{basicData.illuminance}}lux</div>
            <div>光照度</div>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon7.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.carbonDioxide==null">--</div>
            <div v-else>{{basicData.carbonDioxide}}ppm</div>
            <div>CO₂浓度</div>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon8.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.solarRadiation==null">--</div>
            <div v-else>{{basicData.solarRadiation}}W/㎡</div>
            <div>太阳总辐射</div>
          </div>
        </div>
        <div class="swiper-slide"><div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon9.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.sunshineHour==null">--</div>
            <div v-else>{{basicData.sunshineHour}}h</div>
            <div>日照小时数</div>
          </div>
        </div>
        <div class="swiper-slide">
          <div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon10.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.barometricPressure==null">--</div>
            <div v-else>{{basicData.barometricPressure}}hpa</div>
            <div>大气压</div>
          </div>
        </div>
        <div class="swiper-slide"><div class="swiper_item_img">
            <img src="../assets/image/centralControlPlatform/icon11.png" alt="">
          </div>
          <div class="swiper_item_text">
            <div v-if="basicData.pm2_5==null">--</div>
            <div v-else>{{basicData.pm2_5}}μg/m³</div>
            <div>PM2.5</div>
          </div>
        </div>
      </div>
      <!-- Add Pagination -->
      <div class="swiper-pagination"></div>
    </div>
  </div>
</template>
<script>
 import Swiper,{Autoplay} from "swiper";
// import Swiper,{Navigation} from "swiper";
// import "../../node_modules/swiper/swiper-bundle.min.js";
export default {
  props:{
    basicData:{
      type:Object,
      default:new Object
    }
  },
  data(){
    return{

    }
  },
  mounted(){
    this.myswiper()
  },
  methods:{
    myswiper(){
      this.$nextTick(()=>{
        setTimeout(()=>{
          Swiper.use([Autoplay])
          var swiper = new Swiper('.swiper-container', {
            // autoplay:false,
            autoplay: {
              //自动播放,不同版本配置方式不同
              delay: 3000,
              stopOnLastSlide: false,
              disableOnInteraction: false
            },
            direction: 'horizontal',
            slidesPerView: 3,
            loop: true,
          });
        },100)
      })
    },
  },
}
</script>
<style lang="less">
@import '../../node_modules/swiper/swiper.less';
@import '../../node_modules/swiper/swiper-bundle.min.css';
// @import url("../../node_modules/swiper/swiper.less");
@import '../assets/css/smartIOTDataSwiper.less';
</style>
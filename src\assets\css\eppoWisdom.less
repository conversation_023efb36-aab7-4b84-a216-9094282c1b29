.mask_layer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 100;
    background: rgba(208, 243, 255, 0.3);
    // opacity: 0.3;
}
.v-modal {
    background: rgba(208, 243, 255, 0.3);
}
.Dialog_contetrt_hover :nth-child(1) {
    margin-top: 0px;
}
.hover_row {
    display: flex;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #dfeef3;
    // margin-bottom: 5px;
    margin-top: 15px;
    .hover_row_col1 {
        width: 38%;
    }
    // .hover_row_col2 {
    //     // width: 33%;
    // }
    // .hover_row_col3 {
    //     // width: 33%;
    // }
}

.el-table::before {
    height: 0px;
}
// el-table样式
.systemTableStyle {
    ::v-deep .el-table th.el-table__cell > .cell {
        color: #dfeef3 !important;
    }
}
.meteorology_left_smartIOTData_con,
.soil_left_smartIOTData_con,
.InsectSituation_left_smartIOTData_con {
    height: 220px;
    top: 39px;
}
.meteorology_left_smartIOTData,
.soil_left_smartIOTData,
.InsectSituation_left_smartIOTData {
    height: 262px;
}
.meteorology_left_equipmentAlarm_con,
.soil_left_equipmentAlarm_con,
.InsectSituation_left_equipmentAlarm_con {
    width: 461px;
    height: 600px;
    position: absolute;
    bottom: 0;
    background: rgba(1, 13, 23, 0.2);
    // opacity: 0.2;
    /* background: url(img/bg2.f692130e.png) no-repeat 100% center; */
}
.district {
    width: 100%;
    box-sizing: border-box;
    padding-left: 20px;
    padding-top: 20px;
    .district-label {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #dfeef3;
    }
    .district-label-line {
        line-height: 34px;
    }
    .district-text {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #dfeef3;
    }
    .district-row2 {
        margin-top: 26px;
    }
    .district-row3 {
        margin-top: 30px;
    }
    .district-row4 {
        margin-top: 17px;
    }
}
.prevent {
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
    .prevent-table {
        margin-top: 30px;
        display: flex;
        .prevent-button {
            width: 110px;
            height: 36px;
            background: rgba(1, 18, 21, 0);
            border: 1px solid rgba(254, 194, 48, 0.8);
            box-shadow: 0px 0px 9px 1px rgba(255, 165, 45, 0.8) inset;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            line-height: 36px;
            box-sizing: border-box;
            text-align: center;
            cursor: pointer;
        }
        .prevent-radius {
            border-radius: 2px 0px 0px 2px;
        }
        .prevent-radius2 {
            border-radius: 0px 2px 2px 0px;
        }
        .prevent-button-two {
            width: 110px;
            height: 36px;
            background: rgba(1, 18, 21, 0);
            border: 1px solid rgba(0, 244, 253, 0.8);
            box-shadow: 0px 0px 9px 1px rgba(0, 244, 253, 0.8) inset;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            line-height: 36px;
            box-sizing: border-box;
            text-align: center;
            opacity: 0.6;
            cursor: pointer;
        }
    }
    .moreknowledge {
        margin-top: 16px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #007eff;
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
    }
    .content {
        overflow-y: scroll;
        height: 478px;
        .content-box {
            img {
                width: 190px;
                height: 140px;
                margin-bottom: 19px;
                object-fit: cover;
            }
            .content-box-text1 {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #dfeef3;
                // margin-top: 22px;
            }
            .content-box-margin {
                margin-top: 18px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 5;
            }
            .content-box-text2 {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #dfeef3;
            }
            .hide_description {
                display: -webkit-box;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 6;
                word-break: break-all;
                overflow: hidden;
            }
            .wire {
                width: 422px;
                height: 1px;
                margin-bottom: 22px;
                background: radial-gradient(circle, #00dfff, rgba(0, 223, 255, 0));
                margin-top: 20px;
                margin-bottom: 20px;
            }
            .more {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #007eff;
                text-align: center;
                cursor: pointer;
                margin-top: 10px;
                // margin-bottom: 20px;
            }
        }
    }
}
.farming {
    // padding: 0px 29px;
    width: 100%;
    box-sizing: border-box;
    .farming_position {
        width: 100%;
        position: absolute;
        z-index: 2;
        padding: 0px 29px;
        box-sizing: border-box;
    }
    .eppoWisdom {
        width: 230px;
        background: url('../../assets/image/eppoWisdom/eppoWisdom2.png') no-repeat;
        background-size: 100% 100%;
        font-size: 15px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        // color: #00fcff;
        color: #f3fcff;
        margin-top: 30px;
        height: 28px;
        line-height: 28px;
        padding-left: 15px;
        // margin-bottom: 18px;
    }
    .recentEppo {
        display: flex;
        // justify-content: space-between;
        // flex-wrap: wrap;
        :nth-child(n + 2) {
            margin-left: 10px;
        }
        .recentEppo-box {
            width: 240px;
            height: 111px;
            background: url('../../assets/image/eppoWisdom/recentEppo1.png') no-repeat;
            text-align: center;
            margin-top: 18px;
            .recentEppo-time {
                font-size: 30px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #00fcff;
                margin-top: 13px;
            }
            .recentEppo-area {
                width: 135px;
                height: 28px;
                background: linear-gradient(90deg, #006ae1, #16b7ff);
                border-radius: 4px 4px 4px 4px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #ffffff;
                line-height: 28px;
                text-align: center;
                margin: auto;
                margin-top: 18px;
                cursor: pointer;
            }
        }
    }
    .meteorology_right {
        margin: 0;
        .eppoWisdom_twoDimensionalMap {
            width: 950px;
            height: 545px;
            background: url('../../assets/image/monitoringCenter/ploat.png') no-repeat;
            background-size: 100%;
            margin: 62px 0px 0px 19px;
        }
    }
}
.Dialog {
    position: fixed;
    right: 240px;
    top: 242px;
    width: 480px;
    z-index: 100;
    // height: 260px;
    background: rgba(0, 61, 66, 0.96);
    box-shadow: 0px 0px 14px 0px rgba(0, 244, 253, 0.6) inset;
    padding-bottom: 30px;
    .clone {
        float: right;
        margin-top: 10px;
        margin-right: 10px;
    }
    .content {
        margin-top: 26px;
        padding: 0px 21px;
        .correlation_area {
            display: flex;
            margin-top: 6px;
            .correlation_area_text {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #dfeef3;
            }
            // .correlation_area_right .right_row :nth-child(n + 2) {
            //     margin-top: 14px;
            // }
            .correlation_area_left {
                margin-top: 14px;
            }
            .correlation_area_right {
                .right_row {
                    display: flex;
                    margin-top: 14px;
                }
                .right_col1 {
                    margin-left: 69px;
                }
            }
        }
        .Dialog-text1 {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #dfeef3;
            text-align: center;
        }
        .Dialog-text2 {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            margin-top: 18px;
            max-height: 97px;
            overflow: auto;
        }
        .Dialog-text3 {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            margin-top: 18px;
            text-align: center;
            :nth-child(1) {
                margin-right: 10px;
            }
            :nth-child(3) {
                margin-right: 5px;
                margin-left: 5px;
            }
        }
        .wire {
            width: 450px;
            height: 2px;
            background: linear-gradient(90deg, rgba(0, 252, 255, 0.02), rgba(0, 252, 255, 0.96), rgba(0, 252, 255, 0));
            margin-top: 20px;
            // margin-bottom: 20px;
        }
        .image {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            :nth-child(n + 2) {
                margin-left: 10px;
            }
            :nth-child(4) {
                margin-left: 0px;
            }
            img {
                width: 133px;
                height: 84px;
                border: 1px solid #00fff6;
                margin-top: 20px;
            }
        }
    }
}
.meteorologyEMDialog ::v-deep .el-dialog {
    height: 669px;
    margin-top: 30vh !important;
    .el-dialog__body {
        height: 0px;
    }
    .clone {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .header-text {
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #dfeef3;
        position: absolute;
        top: 22px;
        left: 300px;
    }
    .wire {
        height: 2px;
        background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
    }
    .content {
        box-sizing: border-box;
        .row1 {
            display: flex;
            margin-top: 18px;
            .topList1 {
                display: flex;
                margin-top: 16px;
                .topList_item:nth-child(2) {
                    margin-left: 30px;
                    margin-right: 20px;
                }
                .topList_item {
                    display: flex;
                    .item_text {
                        margin-left: 22px;
                        width: 100px;
                        font-weight: 400;
                        text-align: center;
                        :nth-child(1) {
                            font-size: 20px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #70a8ff;
                        }
                        :nth-child(2) {
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #b8d4ff;
                            margin-top: 10px;
                        }
                    }
                }
            }
            .topList2 {
                display: flex;
                .topList2_item {
                    width: 196px;
                    height: 81px;
                    background: url('../../assets/image/eppoWisdom/number.png') no-repeat;
                    background-size: 100%;
                    text-align: center;
                    margin-left: 20px;
                    padding-top: 12px;
                    .topList2_item_text1 {
                        font-size: 20px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #feffff;
                        // margin-top: 12px;
                    }
                    .topList2_item_text2 {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #b8d4ff;
                        margin-top: 8px;
                    }
                }
            }
        }
        .row2 {
            margin-top: 32px;
            display: flex;
            .table {
                .table_text {
                    font-size: 16px;
                    font-family: Microsoft YaHei;
                    font-weight: bold;
                    color: #dfeef3;
                    margin-bottom: 19px;
                }
            }
            .row2_wire {
                width: 1px;
                height: 383px;
                background: radial-gradient(circle, rgba(199, 206, 216, 0.8), rgba(199, 206, 216, 0));
                position: absolute;
                left: 50.6%;
            }
            .row2_right {
                width: 100%;
                margin-left: 40px;
                .row2_right_box1 {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                    .eppoWisdom {
                        width: 200px;
                        background: url('../../assets/image/eppoWisdom/eppoWisdom1.png') no-repeat;
                        font-size: 15px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #00fcff;
                        height: 28px;
                        line-height: 28px;
                        padding-left: 15px;
                    }
                    .text {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #007eff;
                        cursor: pointer;
                    }
                }
                .row2_right_box2 {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;
                    .center1 {
                        width: 297px;
                        height: 94px;
                        background: url('../../assets/image/eppoWisdom/zhibao1.png') no-repeat;
                        background-size: 100%;
                        font-size: 24px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #ffc90c;
                        text-align: center;
                        line-height: 94px;
                        cursor: pointer;
                    }
                    .center2 {
                        width: 297px;
                        height: 94px;
                        background: url('../../assets/image/eppoWisdom/zhibao2.png') no-repeat;
                        background-size: 100%;
                        font-size: 24px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #00fcff;
                        text-align: center;
                        line-height: 94px;
                        cursor: pointer;
                    }
                }
                .row2_right_box3 {
                    width: 100%;
                    margin-top: 25px;
                    background: url('../../assets/image/eppoWisdom/center.png') no-repeat;
                    padding-top: 20px;
                    background-size: 100%;
                    background-size: 100% 100%;

                    width: 650px;
                    height: 196px;
                    .text1 {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #dfeef3;
                        // margin-top: 20px;
                        text-align: center;
                    }
                    .text2 {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #dfeef3;
                        margin-top: 24px;
                        padding: 0px 25px;
                    }
                }
            }
        }
    }
}
// 表头的样式
::v-deep .el-table th {
    // background: rgba($color: #4ec7e1, $alpha: 0.2);
    text-align: center;
    color: #49c3e3;
    font-size: 16px;
    border-bottom: 0px solid #dfe6ec !important;
}
// 行和列的样式
::v-deep .el-table tr,
::v-deep .el-table td {
    background-color: #003d42 !important;
    // border-bottom: 0px solid #dfe6ec !important;
}
// .el-table .warning-row {
//     background: oldlace;
// }

// .el-table .success-row {
//     background: #f0f9eb;
// }

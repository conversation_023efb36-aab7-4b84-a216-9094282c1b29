<template>
  <div class="seedlingGrowthUploadDialog dialogStyle">
    <el-dialog
      :title="title"
      width="607px"
      v-if="dialogVisible"
      center
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="dialogVisible=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="analyse">
        <div class="analyse_list">
          <div class="analyse_list_title analyse_list_title">种植区域：</div>
          <div class="analyse_list_value analyse_list_select formStyle">
            <el-select v-model="ruleForm.areaId" @change="leafDataChange" popper-class="selectStyle_list" placeholder="请选择种植区域">
              <el-option
                      v-for="Item in areaData"
                      :key="Item.areaId"
                      :label="Item.areaName"
                      :value="Item.areaId"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="analyse_list">
          <div class="analyse_list_item">
            <div class="analyse_list_item_title">作物：</div>
            <div class="analyse_list_item_value formStyle">
              <el-select v-model="ruleForm.cropName" @change="cropChange" popper-class="selectStyle_list" placeholder="请选择作物">
                <el-option
                        v-for="item in cropData"
                        :key="item.value"
                        :label="item.key"
                        :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="analyse_list_item">
            <div class="analyse_list_item_title analyse_list_item_title2">作物生长期：</div>
            <div class="analyse_list_item_value formStyle">
              <el-select v-model="ruleForm.growingPeriod" popper-class="selectStyle_list" placeholder="请选择作物生长期">
                <el-option
                        v-for="item in growthData"
                        :key="item.value"
                        :label="item.key"
                        :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="analyse_list">
          <div class="analyse_list_item analyse_list_item2">
            <div class="analyse_list_title">苗株高度：</div>
            <div class="analyse_list_value">
              约
              <div class="value_input formStyle">
                <el-input v-model.number="ruleForm.minHeight" @input="changeMessage()"></el-input>
              </div>
              ~
              <div class="value_input formStyle">
                <el-input v-model.number="ruleForm.maxHeight" @input="changeMessage()"></el-input>
              </div>
              cm
            </div>
          </div>
          <div class="analyse_list_item analyse_list_item2">
            <div class="analyse_list_title" style="margin-right: 0px">杂草覆盖率：</div>
            <div class="analyse_list_value">
              <div class="value_input value_input2 formStyle" style="width: 68px">
                <el-input v-model="ruleForm.weed"></el-input>
              </div>
              %
            </div>
          </div>
        </div>
        <div class="analyse_list">
          <div class="analyse_list_title analyse_list_title2">采样叶色状况：</div>
          <div class="analyse_list_value2">
            <img src="../../assets/image/centralControlPlatform/add-icon2.png" alt=""  class="analyse_list_value2_add" @click="leafAdd()">
<!--            <div class="analyse_list_value2_selected" v-for="(item,index) in 2" :key="index">-->
<!--              叶色白斑-->
<!--              <img src="../../assets/image/centralControlPlatform/reduce-icon2.png" alt=""  class="analyse_list_value2_selected_add">-->
<!--            </div>-->
            <div class="analyse_list_value2_select formStyle" v-for="(item,index) in leafArr" :key="index">
              <el-select v-model="leafArr[index]" @change="leafDataChange" popper-class="selectStyle_list" placeholder="请选择叶色状况">
                <el-option
                        v-for="Item in leafData"
                        :key="Item.value"
                        :label="Item.key"
                        :value="Item.value"
                        :disabled="Item.disabled"
                >
                </el-option>
              </el-select>
              <img src="../../assets/image/centralControlPlatform/reduce-icon2.png" alt="" @click="leafDelete(index)"  class="analyse_list_value2_selected_add">
            </div>
          </div>
        </div>
        <div class="analyse_list">
          <div class="analyse_list_title analyse_list_title2">苗株长相：</div>
          <div class="analyse_list_value2">
            <img src="../../assets/image/centralControlPlatform/add-icon2.png" alt=""  class="analyse_list_value2_add" @click="plantAdd()">
<!--            <div class="analyse_list_value2_selected" v-for="(item,index) in 2" :key="index">-->
<!--              叶色白斑-->
<!--              <img src="../../assets/image/centralControlPlatform/reduce-icon2.png" alt=""  class="analyse_list_value2_selected_add">-->
<!--            </div>-->
            <div class="analyse_list_value2_select formStyle" v-for="(item,index) in plantArr" :key="index">
              <el-select v-model="plantArr[index]" @change="plantDataChange" popper-class="selectStyle_list" placeholder="请选择苗株长相">
                <el-option
                        v-for="Item in plantData"
                        :key="Item.value"
                        :label="Item.key"
                        :value="Item.value"
                        :disabled="Item.disabled"
                >
                </el-option>
              </el-select>
              <img src="../../assets/image/centralControlPlatform/reduce-icon2.png" alt=""  @click="plantDelete(index)" class="analyse_list_value2_selected_add">
            </div>
          </div>
        </div>
        <div class="analyse_list">
          <div class="analyse_list_title analyse_list_title2">说明：</div>
          <div class="analyse_list_value2 analyse_list_inputArea formStyle">
            <el-input type="textarea" v-model="ruleForm.annotation">

            </el-input>
          </div>
        </div>
      </div>
      <div class="btnBox">
        <div class="submit_button searchButtonStyle" @click="dialogVisible=false">
          <el-button>取消</el-button>
        </div>
        <div class="submit_button submitButtonStyle" @click="submit()">
          <el-button>提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import cropData from "../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类";
  import leafData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$叶色状况'
  import plantData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$苗株长相'
  import growthData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期'
  import CropCaseService from "../../jaxrs/concrete/com.zny.ia.api.CropCaseService";
  import CommonService from "../../jaxrs/concrete/com.zny.ia.api.CommonService";

  export default {
  name:'seedlingGrowthUploadDialog',
  data(){
    return{
      title:"变更植株状况",
      dialogVisible:false,
      cropData:cropData._toArray(),
      leafData:leafData._toArray(),
      plantData:plantData._toArray(),
      growthData:[],
      areaId:"",
      areaData:[],
      ruleForm:{
      },
      leafArr:[],
      plantArr:[]
    }
  },
  mounted(){
    this.listen('seedlingGrowthUploadDialogOpen', (data) => {
      this.getAreaData()
      this.dialogVisible=true
      this.ruleForm={...data}
      if(data.cropName!="" && data.cropName!=null) this.cropChange(data.cropName)
      this.leafArr = [...this.ruleForm.leafColor]
      this.plantArr = [...this.ruleForm.cropLooks]
      this.ruleForm.minHeight=this.ruleForm.cropHeight.match(/约(\S*)~/)[1];
      this.ruleForm.maxHeight=this.ruleForm.cropHeight.match(/~(\S*)c/)[1];
      this.disabledLeafArr()
    })
  },
  methods:{
    //获取区域数据
    getAreaData(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        this.areaData=res
      })
    },
    //农作物选择
    cropChange(val){
      //根据作物种类获取对应生长期
      CropCaseService.findCropPeriod(val)
      .then(res=>{
        this.growthData=growthData._toArray(res)
      })
    },
    //数据设置禁用处理
    disabledLeafArr(){
      //重置禁用
      this.leafData=this.leafData.map((item)=>{
        item.disabled=false
        return item
      })
      //设置禁用
      for(var i=0;i<this.leafData.length;i++){
        for(var j=0;j<this.leafArr.length;j++){
          if(this.leafData[i].value==this.leafArr[j]){
            this.leafData[i].disabled=true
            break;
          }
        }
      }
    },
    leafAdd(){
      if(this.leafArr[this.leafArr.length-1]!==''){
        this.leafArr.push('')
      }
    },
    leafDelete(index){
      this.leafArr.splice(index,1)
      this.disabledLeafArr()
    },
    leafDataChange(val){
      this.disabledLeafArr()
    },
    //数据设置禁用处理
    disabledPlantArr(){
      //重置禁用
      this.plantData=this.plantData.map((item)=>{
        item.disabled=false
        return item
      })
      //设置禁用
      for(var i=0;i<this.plantData.length;i++){
        for(var j=0;j<this.plantArr.length;j++){
          if(this.plantData[i].value==this.plantArr[j]){
            this.plantData[i].disabled=true
            break;
          }
        }
      }
    },
    plantAdd(){
      if(this.plantArr[this.plantArr.length-1]!==''){
        this.plantArr.push('')
      }
    },
    plantDelete(index){
      this.plantArr.splice(index,1)
      this.disabledPlantArr()
    },
    plantDataChange(val){
      this.disabledPlantArr()
    },
    //输入强制刷新
    changeMessage(){
      this.$forceUpdate()
    },
    //提交
    submit(){
      if(this.leafArr[this.leafArr.length-1]==''){
        this.leafArr.splice(this.leafArr.length-1,1)
      }
      if(this.plantArr[this.plantArr.length-1]==''){
        this.plantArr.splice(this.plantArr.length-1,1)
      }
      var param={
        areaId:this.ruleForm.areaId,
        annotation:this.ruleForm.annotation,
        cropHeight:'约'+this.ruleForm.minHeight+'~'+this.ruleForm.maxHeight+'cm',
        cropLooks:this.plantArr,
        cropName:this.ruleForm.cropName,
        growingPeriod:this.ruleForm.growingPeriod,
        leafColor:this.leafArr,
        weed:this.ruleForm.weed,
      }
      CropCaseService.saveCropCasePicAnalyse(param)
      .then(()=>{
          this.$message({
            message:'提交成功！',
            type:'success'
          })
          this.dialogVisible=false
      })
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/seedlingGrowthUploadDialog.less';
</style>
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppIrrigationService = {
    /**
     * 首页-获取管理各区域灌溉设备节点详情
     * @returns Promise 
     */
    'areaIrrigationNodeList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '50e487314f097018f4077cdbbd9fddf1285ab917')
            : execute(concreteModuleName, `/App/IrrigationService/areaIrrigationNodeList`, 'json', 'GET');
    }, 
    /**
     * 中控-修改灌溉设备详情
     * @param {*} po 灌溉设备信息
     * @returns Promise 
     */
    'changeIrrigationNodeInformation': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'aace050beda24382d5c382420e1cd77117b4ea1c', po)
            : execute(concreteModuleName, `/App/IrrigationService/changeIrrigationNodeInformation`, 'json', 'POST', po);
    }, 
    /**
     * 首页-区域灌溉历史
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'areaCumulativeIrrigationHistoryList': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a9129a74ab0e5d24266b3dacbed2c67d6d8331d5', areaId)
            : execute(concreteModuleName, `/App/IrrigationService/areaCumulativeIrrigationHistoryList`, 'json', 'POST', { areaId });
    }, 
    /**
     * 中控-管理各区域灌溉设备详情
     * @returns Promise 
     */
    'manageAreaIrrigationNodeList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'acf8b91a647ddcd781f236e9055c9e7dd6598e0f')
            : execute(concreteModuleName, `/App/IrrigationService/manageAreaIrrigationNodeList`, 'json', 'GET');
    }, 
    /**
     * 首页-设置水阀结束时间
     * @param {*} nodeId 节点Id
     * @param {*} dateTime 水阀结束时间 - 秒级时间戳
     * @returns Promise 
     */
    'installTraceSourceWaterEndTime': function (nodeId, dateTime) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0a21476caecbf996c9c6e8abd1bc80b1ec2c5b4d', {nodeId, dateTime})
            : execute(concreteModuleName, `/App/IrrigationService/installTraceSourceWaterEndTime`, 'json', 'POST', { nodeId, dateTime });
    }
}

export default AppIrrigationService

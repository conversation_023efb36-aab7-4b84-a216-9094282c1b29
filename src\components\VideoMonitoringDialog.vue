<template>
    <div class="video-monitoring-dialog">
        <el-dialog
            title="视频监控"
            :visible.sync="dialogVisible"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :modal="false"
            :modal-append-to-body="false"
            width="1256px"
            custom-class="video-monitoring-dialog"
        >
            <!-- 关闭按钮 -->
            <img src="../assets/image/eppoWisdom/close.png" alt="关闭" class="clone" @click="closeDialog" />
            <div class="wire"></div>


            <!-- 弹窗内容容器 -->
            <div class="content-container">
                <!-- 第一行控制区域 -->
                <div class="control-row">
                    <div class="control-left">
                        <button class="back-btn" @click="goBack">返回</button>
                        <el-select
                            v-model="selectedTime"
                            class="time-select formStyle"
                            placeholder="请选择时间"
                            @change="onTimeChange"
                        >
                            <el-option
                                v-for="item in timeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </div>
                    <div class="control-right">
                        <button class="realtime-btn" @click="goToRealtime">实时</button>
                    </div>
                </div>

                <!-- 视频展示区域 -->
                <div class="video-container">
                    <div class="video-border">
                        <div class="video-window">
                            <!-- 视频内容区域 -->
                            <div class="video-content">
                                <img src="../assets/image/monitoringCenter/videoPlay.png" alt="视频播放" class="video-placeholder" />
                                <div class="video-info">{{ selectedTime || '请选择时间段' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'VideoMonitoringDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        videoData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            dialogVisible: false,
            selectedTime: '',
            
            // 时间选项
            timeOptions: [
                { value: '16:04:03 - 17:15:50', label: '16:04:03 - 17:15:50' },
                { value: '14:20:15 - 15:30:45', label: '14:20:15 - 15:30:45' },
                { value: '12:10:30 - 13:25:20', label: '12:10:30 - 13:25:20' },
                { value: '10:05:12 - 11:18:35', label: '10:05:12 - 11:18:35' },
                { value: '08:15:25 - 09:40:10', label: '08:15:25 - 09:40:10' }
            ]
        }
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal
            if (newVal && this.videoData.timeRange) {
                this.selectedTime = this.videoData.timeRange
            }
        },
        dialogVisible(newVal) {
            this.$emit('update:visible', newVal)
        }
    },
    methods: {
        // 关闭弹窗
        closeDialog() {
            this.dialogVisible = false
        },
        
        // 返回
        goBack() {
            this.dialogVisible = false
            this.$emit('go-back')
        },
        
        // 跳转到实时监控
        goToRealtime() {
            this.dialogVisible = false
            this.$emit('go-to-realtime')
        },
        
        // 时间选择改变
        onTimeChange() {
            console.log('时间选择改变:', this.selectedTime)
            // TODO: 实现时间切换逻辑
        }
    }
}
</script>

<style lang="less" scoped>
// 视频监控弹窗样式
.video-monitoring-dialog ::v-deep .el-dialog {
    height: 854px!important;
    margin-top: calc(50vh - 400px) !important;

    .el-dialog__header {
        padding: 23px 30px 23px 30px;
        text-align: left;

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
        }
    }

    .el-dialog__body {
        height: calc(100% - 114px);
        padding: 0 52px 30px 52px!important;
        position: relative;

        .clone {
            position: absolute;
            top: -55px!important;
            right: 24px!important;
            cursor: pointer;
            z-index: 10;
        }
        .wire{
            height: 2px;
            position: relative;
            top: -15px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }
        
        .content-container {
            height: 100%;

            // 控制行样式
            .control-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                height: 32px;

                .control-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .back-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }

                    .time-select {
                        width: 180px;
                        height: 32px;
                    }
                }

                .control-right {
                    .realtime-btn {
                        width: 80px;
                        height: 32px;
                        background: rgba(0,245,255,0);
                        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
                        border-radius: 2px;
                        border: 0;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        cursor: pointer;

                        &:hover {
                            background: rgba(0,245,255,0.1);
                        }
                    }
                }
            }

            // 视频容器样式
            .video-container {
                width: 100%;
                display: flex;
                justify-content: center;

                .video-border {
                    width: 1152px;
                    height: 662px;
                    border: 1px solid rgba(0, 244, 253, 0.5);
                    // border-radius: 4px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.1);

                    .video-window {
                        width: 1120px;
                        height: 630px;
                        background: #000000;
                        border-radius: 2px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .video-content {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            .video-placeholder {
                                width: 64px;
                                height: 64px;
                                opacity: 0.5;
                                margin-bottom: 20px;
                            }

                            .video-info {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                color: #DFEEF3;
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 自定义下拉选择框文字颜色
.video-monitoring-dialog ::v-deep .formStyle {
    .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
    }

    .el-select .el-input .el-select__caret {
        color: rgba(254, 255, 255, 0.6) !important;
    }
}
</style>

<template>
  <div class="irrigationDrainageDialog dialogStyle">
    <el-dialog
      :title="title"
      width="795px"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :show-close="false"
      @open="dialogOpen()"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="dialogVisible=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="time">
        数据采集时间：{{areaData1.createDate}}
      </div>
      <div class="searchBox formStyle">
        <el-select v-model="node" @change="nodeChange" popper-class="selectStyle_list" placeholder="请选择灌溉节点">
          <el-option
                  v-for="item in nodeData"
                  :key="item.nodeId"
                  :label="item.nodeName"
                  :value="item.nodeId"
          >
          </el-option>
        </el-select>
      </div>
      <div class="container">
        <div class="container_left">
          <div class="container_left_data">
            <div class="container_left_data_num">
              <div class="list">
                <div class="list_item">
                  <div class="item_img">
                    <img src="../../assets/image/monitoringCenter/icon3.png" alt="">
                  </div>
                  <div class="item_text">
                    <div>{{areaData1.soilTemperature}}℃</div>
                    <div>土温</div>
                  </div>
                </div>
                <div class="list_item">
                <div class="item_img">
                  <img src="../../assets/image/monitoringCenter/icon4.png" alt="">
                </div>
                <div class="item_text">
                  <div>{{areaData1.soilHumidity}}%RH</div>
                  <div>墒情</div>
                </div>
              </div>
              </div>
              <div class="list3">
                <img src="../../assets/image/centralControlPlatform/liusu.png" alt="" class="list3_image">
                <div class="list3_numItem list3_numItem1">
                  <div class="list3_numItem_text1">{{areaData2.flowVelocity}}m/s</div>
                  <div class="list3_numItem_text2">瞬时流速</div>
                </div>
                <div class="list3_numItem list3_numItem2">
                  <div class="list3_numItem_text1">{{areaData2.todayIrrigation}}m³</div>
                  <div class="list3_numItem_text2">今日灌溉</div>
                </div>
              </div>
            </div>
            <div class="container_left_data_con">
              <div class="list_button">
                <div class="list_button_item">
                  <div class="item_text">水阀开关：</div>
                  <div class="item_button">
                    <el-switch
                      @change="waterValueChange"
                      v-model="areaData2.waterValve"
                      active-color="#0BBA8B"
                      inactive-color="#F13737"
                      :disabled="waterValveDisabled">
                    </el-switch>
                  </div>
                </div>
                <div class="list_button_item">
                  <div class="item_text">施肥开关：</div>
                  <div class="item_button">
                    <el-switch
                      @change="applyFertilizerChange"
                      v-model="areaData2.applyFertilizer"
                      active-color="#0BBA8B"
                      inactive-color="#F13737"
                      :disabled="applyFertilizerDisabled">
                    </el-switch>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="container_Info">
          <div class="item_header">基础信息</div>
          <div class="itemContainer">
            <div class="itemContainer_left">
              <div class="item_list">
                <div class="item_list_title">测站名称：</div>
                <div class="item_list_value">{{areaData1.checkoutName}}</div>
              </div>
              <div class="item_list">
                <div class="item_list_title">通讯代码：</div>
                <div class="item_list_value">{{areaData1.communicationId}}</div>
              </div>
              <div class="item_list">
                <div class="item_list_title">测站类别：</div>
                <div class="item_list_value">{{areaData1.checkoutType}}</div>
              </div>
              <div class="item_list">
                <div class="item_list_title">安装位置：</div>
                <div class="item_list_value">{{areaData1.location}}</div>
              </div>
              <div class="item_list">
                <div class="item_list_title">经纬度：</div>
                <div class="item_list_value">{{areaData1.longitude}},{{areaData1.latitude}}</div>
              </div>
              <div class="item_list">
                <div class="item_list_title">设站年月：</div>
                <div class="item_list_value">{{areaData1.createDate}}</div>
              </div>
            </div>
            <div class="itemContainer_right">
              <div class="item_list">
                <div class="item_list_title">测站图片：</div>
                <div class="item_list_image">
                  <img :src=areaData1.picture alt="">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import IrrigationService from '../../jaxrs/concrete/com.zny.ia.api.IrrigationService'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js';

export default {
  name:'irrigationDrainageDialog',
  data(){
    return{
      title:"",
      dialogVisible:false,
      node:'',
      nodeData:[],
      waterValue:true,
      fertilizerValue:true,
      areaData1:[],//基本信息
      areaData2:[],//流速信息
      waterValveDisabled:false,
      applyFertilizerDisabled:false,
      areaId:'',

    }
  },
  mounted(){
    this.listen('irrigationDrainageDialogOpen', (areaId) => {
      this.areaId=areaId
      this.dialogVisible=true
    })
  },
  methods:{
    // 判断当前用户能否操作该区域
    areaJudge(){
      CommonService.areaJudge(this.areaId)
      .then(res=>{
        // console.log(res);
        this.waterValveDisabled=!res
        this.applyFertilizerDisabled=!res
      })
    },
    dialogOpen(){
      this.getIrrigationNode()
      this.getAreaData1()
      this.getAreaData2()
      this.areaJudge()
    },
    //获取所有灌溉节点
    getIrrigationNode(){
      IrrigationService.irrigationNodeListInformation()
      .then(res=>{
        if(res.length==0){
          this.waterValveDisabled=true
          this.applyFertilizerDisabled=true
        }else{
          this.waterValveDisabled=false
          this.applyFertilizerDisabled=false
        }
        this.nodeData=res
      })
    },
    //灌溉节点改变
    nodeChange(val){
      this.node=val
      this.getAreaData1()
      this.getAreaData2()
    },
    //获取区域基本信息
    getAreaData1(){
      IrrigationService.irrigationAreaInformation(this.areaId)
      .then(res=>{
        this.areaData1=res
        this.title=res.checkoutName
      })
    },
    //获取灌溉基本信息
    getAreaData2(){
      IrrigationService.instantaneousInformation(this.node)
        .then(res=>{
          this.areaData2=res
        })
    },
    //施肥开关控制
    applyFertilizerChange(val){
      var control= val?0:1
      IrrigationService.controlTraceSourceFertilization(this.node,control)
      .then(()=>{
        this.getAreaData2()
      })
      .catch(()=>{
        this.getAreaData2()
      })
    },
    //水阀开关控制
    waterValueChange(val) {
      var control = val ? 0 : 1
      IrrigationService.controlTraceSourceWater(this.node, control)
      .then(() => {
        this.getAreaData2()
      })
      .catch(() => {
        this.getAreaData2()
      })
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/irrigationDrainageDialog.less';
</style>
.centralControlPlatform{
  width: 1920px;
  height: 971px;
  display: flex;
  &_menuList{
    width: 292px;
    height: 922px;
    margin-left: 38px;
    background: url(../image/centralControlPlatform/bg1.png) no-repeat 100% center;
    margin-top: 9px;
    position: relative;
    &_container{
      position: absolute;
      z-index: 100;
      .menuList_item{
        &_con{
          width: 292px;
          height: 82px;
          line-height: 82px;
          font-size: 20px;
          font-weight: 400;
          color: #DFEEF3;
          display: flex;
          cursor: pointer;
        }
        img{
          vertical-align: middle;
          margin: 0 27px 0 45px;
        }
      }
      .itemActive{
        background: url(../image/centralControlPlatform/activeBorder.png) no-repeat 100% center;
      }
      .active{
        color: #FBCD1B;
      }
    }
    .menuLine{
      width: 1.1px;
      height: 721.8px;
      position: absolute;
      top: 52px;
      left: 54px;
      z-index: 1;
    }
  }
  &_con{
    width: 1532px;
    height: 922px;
    margin: 9px 0 0 20px;
  }
}
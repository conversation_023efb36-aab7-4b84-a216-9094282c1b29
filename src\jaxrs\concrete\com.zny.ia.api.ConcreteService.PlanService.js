/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const PlanService = {
    /**
     * 新增生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'addPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8ef00b5f4033fdc3be63acc7f970c0637f7afb8f', po)
            : execute(concreteModuleName, `/PlanService/addPlan`, 'json', 'POST', po);
    }, 
    /**
     * 生产计划详情-带完成区域
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'singlePlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2fd40e883a0f0e12525e0686e261409b7133b49d', planId)
            : execute(concreteModuleName, `/PlanService/singlePlan`, 'json', 'POST', { planId });
    }, 
    /**
     * 生产计划详情
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'detailPlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '492b0bc2c5a558e697a95b0b5de1f8e7fc31ed98', planId)
            : execute(concreteModuleName, `/PlanService/detailPlan`, 'json', 'POST', { planId });
    }, 
    /**
     * 生产计划列表
     * @param {*} po 
     * @returns Promise 
     */
    'listPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b1cfa135aea2194f1e93ddac9abfe59ea21f340b', po)
            : execute(concreteModuleName, `/PlanService/listPlan`, 'json', 'POST', po);
    }, 
    /**
     * 下载报表
     * @param {*} po 
     * @returns Promise 
     */
    'downloadPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '39ce27e04c5c9d457e1d13abeec1138eb841ba1d', po)
            : execute(concreteModuleName, `/PlanService/downloadPlan`, 'json', 'POST', po);
    }, 
    /**
     * 编辑生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'updatePlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4fb794d6f3460927e7b3011429ccb67160f6fdb1', po)
            : execute(concreteModuleName, `/PlanService/plan`, 'json', 'PUT', po);
    }, 
    /**
     * 本周相关计划
     * @param {*} po 
     * @returns Promise 
     */
    'correlationPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd5968cb1736ecce150ed78fc20b5b7ab5de47fc0', po)
            : execute(concreteModuleName, `/PlanService/correlationPlan`, 'json', 'POST', po);
    }, 
    /**
     * 完成生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'finishPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a24e6779a7cbe5baa3cd5588369f42661f2c9073', po)
            : execute(concreteModuleName, `/PlanService/finishPlan`, 'json', 'POST', po);
    }, 
    /**
     * 删除生产计划 pc app 共用
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'deletePlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2f7573d7f91ccf0ec9556ab546175ec8d8445d5d', planId)
            : execute(concreteModuleName, `/PlanService/plan`, 'json', 'DELETE', { planId });
    }
}

export default PlanService

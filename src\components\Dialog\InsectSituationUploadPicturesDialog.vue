<template>
  <div class="InsectSituationUploadPicturesDialog dialogStyle">
    <el-dialog
      title="提示"
      width="32%"
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      center
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="closeDialog()">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="addFrom">
        <el-form ref="ruleForm" :model="ruleForm"  :rules="rules" label-width="125px" label-position="left">
          <el-form-item prop="photoList" label="图片信息：" class="formStyle" :class="{hide:hideUpload}">
            <el-upload
              class="uploadCard128"
              action=""
              list-type="picture-card"
              :file-list="fileList"
              :limit="1"
              :on-exceed="handleExceed"
              :on-change="handleChange"
              :auto-upload="false"
              :on-remove="handleRemove">
              <img src="../../assets/image/centralControlPlatform/upload-add.png" alt="">
            </el-upload>
          </el-form-item>
          <el-form-item prop="areaId" label="归属区域：" class="formStyle">
            <el-select
            v-model="ruleForm.areaId" 
            clearable 
            placeholder="请选择" 
            popper-class="selectStyle_list">
              <el-option
                v-for="item in areaData"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaId"
                >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="wormCaseKinds" label="虫情检测状况：" class="formStyle">
            <div class="tipsImg" @click="numIncrease">
              <img src="../../assets/image/centralControlPlatform/add-icon1.png" alt="">
            </div>
            <div class="formList">
              <div class="formList_item" v-for="(item,index) in num" :key="index">
                <div class="handleBox_item handleBox_item_select formStyle">
                  <el-select
                  v-model="ruleForm.wormCaseKinds[index].kind" 
                  clearable 
                  placeholder="请选择" 
                  popper-class="selectStyle_list">
                    <el-option
                      v-for="item in kindData"
                      :key="item.value"
                      :label="item.key"
                      :value="item.value"
                      >
                    </el-option>
                  </el-select>
                </div>
                <div class="handleBox_item handleBox_item_input formStyle">
                  <el-input v-model.number="ruleForm.wormCaseKinds[index].count" ></el-input>
                </div>
                <div class="handleBox_item handleBox_item_text">
                  只
                </div>
                <div class="handleBox_item handleBox_item_img" @click="numReduce(index)">
                  <img src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="searchButtonStyle">
          <el-button @click="cancel">取消</el-button>
        </div>
        <div class="submitButtonStyle">
          <el-button @click="submit('ruleForm')">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getBase64} from '../../js/base64.js';
import data from '../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$虫情种类.js';
import WormCaseService from '../../jaxrs/concrete/com.zny.ia.api.WormCaseService.js';
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js';

export default {
  data(){
    return{
      dialogVisible:false,
      num:1,//虫情监测状况循环数
      areaData:[],
      kindData:data._toArray(),//虫情种类
      ruleForm:{
        areaId:'',
        imagesPath:[],
        litPicUrl:"",
        photoList:[],
        wormCaseKinds:[{kind:"",count:""}]
      },
      fileList:[],
      hideUpload:false,
      rules:{
        photoList:[
          { required: true, message: '请上传图片', trigger: 'blur' },
        ],
        areaId:[
          { required: true, message: '请选择归属区域', trigger: 'change' },
        ],
        wormCaseKinds:[
          { required: true, message: '请输入虫情监测状况', trigger: 'blur' },
        ],
      },
    }
  },
  mounted(){
    this.listen('InsectSituationUploadPicturesDialogOpen', () => {
      this.dialogVisible=true
      this.getAreaData()
    })
  },
  methods:{
    //获取区域数据
    getAreaData(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        this.areaData=res
      })
    },
    //处理上传的文件
    updatePicProperties(fileList) {
      this.ruleForm.imagesPath = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content}
      });
      this.ruleForm.litPicUrl = fileList.filter(e => !e._type).map(e => e.url).join(',')
    },
    // 移除文件
    handleRemove(files,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 1
      this.updatePicProperties(fileList)
    },
    // 上传文件个数限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传 1 张图片`);
    },
    handleChange(file,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 1
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicProperties(fileList)
      })
    },
    // 点击数量增加
    numIncrease(){
      this.num+=1
      this.ruleForm.wormCaseKinds.push({kind:"",count:""})
    },
    // 点击删除某一项
    numReduce(index){
      this.ruleForm.wormCaseKinds.splice(index,1)
      this.num-=1
    },
    // 关闭弹窗
    closeDialog(){
      this.dialogVisible=false
      Object.assign(this.$data,this.$options.data())
    },
    // 取消
    cancel(){
      this.dialogVisible=false
      Object.assign(this.$data,this.$options.data())
    },
    // 确定提交
    submit(formName){
      const that=this
      // console.log(this.ruleForm)
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let po={
            areaId:this.ruleForm.areaId,
            pictures:this.ruleForm.imagesPath[0],
            wormCaseKinds:this.ruleForm.wormCaseKinds
          }
          WormCaseService.wormCasePicImport(po)
          .then(()=>{
            that.dialogVisible=false
            that.ruleForm={
              areaId:'',
              imagesPath:[],
              litPicUrl:"",
              photoList:[],
              wormCaseKinds:[{kind:"",count:""}]
            }
            that.$message({
              message: '数据上报成功',
              type: 'success'
            });
            that.zEmit('insectSituationUpdateSuccess')
          }).catch(()=>{
            that.$message.error('数据上报失败,请重试!');
          })
        }
      })
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/InsectSituationUploadPicturesDialog.less';
</style>
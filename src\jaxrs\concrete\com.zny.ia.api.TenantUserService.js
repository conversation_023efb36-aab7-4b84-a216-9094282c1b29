/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const TenantUserService = {
    /**
     * 删除租户功能集
     * @param {*} tenantId 租户id
     * @returns Promise 
     */
    'deleteAllTenantPermission': function (tenantId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3923d9053225ec6e6568ff968aa293275424f4cf', tenantId)
            : execute(concreteModuleName, `/TenantUserService/allTenantPermission`, 'json', 'DELETE', { tenantId });
    }, 
    /**
     * 全量更新租户功能集
     * @param {*} po 
     * @returns Promise 
     */
    'updateTenantPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '067777c07d9f046239dc51d579f5edd170646088', po)
            : execute(concreteModuleName, `/TenantUserService/tenantPermission`, 'json', 'PUT', po);
    }
}

export default TenantUserService

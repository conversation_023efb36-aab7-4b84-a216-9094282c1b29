/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppEquipmentService = {
    /**
     * 获取设备列表
     * @param {*} equipmentType 设备类型
     * @returns Promise 
     */
    'listEquipment': function (equipmentType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0942a7bb1af12b3e2e27c979f5dac442c433c682', equipmentType)
            : execute(concreteModuleName, `/App/EquipmentService/listEquipment`, 'json', 'POST', { equipmentType });
    }, 
    /**
     * 编辑设备
     * @param {*} po 
     * @returns Promise 
     */
    'updateEquipment': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c1637de43d9b438ae2429897e5819576fd8eb2d3', po)
            : execute(concreteModuleName, `/App/EquipmentService/equipment`, 'json', 'PUT', po);
    }, 
    /**
     * 设备详情
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'detailEquipment': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '02e82fce02162330f8a459d297c3d7979cbf73a3', equipmentId)
            : execute(concreteModuleName, `/App/EquipmentService/detailEquipment`, 'json', 'POST', { equipmentId });
    }
}

export default AppEquipmentService

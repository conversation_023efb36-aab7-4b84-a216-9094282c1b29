<template>
  <div class="InsectSituationAlarmDialog dialogStyle">
    <el-dialog
      title="虫情报警记录"
      width="68%"
      :visible.sync="dialogVisible"
      center
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="dialogVisible=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item formStyle">
          <el-select
          v-model="value" 
          clearable 
          placeholder="请选择" 
          popper-class="selectStyle_list">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item formStyle">
          <el-select 
          v-model="value" 
          clearable 
          placeholder="请选择"
          popper-class="selectStyle_list">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item formStyle">
          <el-date-picker
            v-model="value1"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'>
          </el-date-picker>
        </div>
        <div class="handleBox_item searchButtonStyle">
          <el-button>查询</el-button>
        </div>
      </div>
      <div class="tableBox tableStyle">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          height="404px">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="50">
          </el-table-column>
          <el-table-column
            prop="date"
            label="报警类型">
          </el-table-column>
          <el-table-column
            prop="name"
            label="监测数值">
          </el-table-column>
          <el-table-column
            prop="address"
            label="种类">
          </el-table-column>
          <el-table-column
            prop="address"
            label="数量">
          </el-table-column>
          <el-table-column
            prop="address"
            label="报警状态">
          </el-table-column>
          <el-table-column
            prop="address"
            label="报警时间">
          </el-table-column>
          <el-table-column
            prop="address"
            label="报警结束时间">
          </el-table-column>
          <el-table-column
            prop="address"
            label="图片">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data(){
    return{
      dialogVisible:false,
    }
  },
  mounted(){
    this.listen('InsectSituationAlarmDialogOpen', () => {
      this.dialogVisible=true
    })
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
@import '../../assets/css/Dialog/InsectSituationAlarmDialog.less';
</style>
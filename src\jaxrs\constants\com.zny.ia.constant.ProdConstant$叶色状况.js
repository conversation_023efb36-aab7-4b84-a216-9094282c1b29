import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 黄褐
     * desc: --
     * value: 0
     */
    黄褐: 0,
    /**
     * label: 白色
     * desc: --
     * value: 1
     */
    白色: 1,
    /**
     * label: 暗红
     * desc: --
     * value: 2
     */
    暗红: 2,
    /**
     * label: 暗绿
     * desc: --
     * value: 3
     */
    暗绿: 3,
    /**
     * label: 黄白色
     * desc: --
     * value: 4
     */
    黄白色: 4,
    /**
     * label: 白化
     * desc: --
     * value: 5
     */
    白化: 5,
    /**
     * label: 红化
     * desc: --
     * value: 6
     */
    红化: 6,
    /**
     * label: 边缘黄叶
     * desc: --
     * value: 7
     */
    边缘黄叶: 7,
    /**
     * label: 叶片发白
     * desc: --
     * value: 8
     */
    叶片发白: 8,
    /**
     * label: 变紫
     * desc: --
     * value: 9
     */
    变紫: 9,
    /**
     * label: 浅绿色
     * desc: --
     * value: 10
     */
    浅绿色: 10,
    /**
     * label: 暗绿色
     * desc: --
     * value: 11
     */
    暗绿色: 11,
    /**
     * label: 发黄
     * desc: --
     * value: 12
     */
    发黄: 12,
    /**
     * label: 叶脉发白
     * desc: --
     * value: 13
     */
    叶脉发白: 13,
    /**
     * label: 青绿色
     * desc: --
     * value: 14
     */
    青绿色: 14,
    /**
     * label: 黑绿色
     * desc: --
     * value: 15
     */
    黑绿色: 15,
    /**
     * label: 黄绿斑点
     * desc: --
     * value: 16
     */
    黄绿斑点: 16,
    /**
     * label: 黄叶
     * desc: --
     * value: 17
     */
    黄叶: 17,
    /**
     * label: 正常
     * desc: --
     * value: 18
     */
    正常: 18,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
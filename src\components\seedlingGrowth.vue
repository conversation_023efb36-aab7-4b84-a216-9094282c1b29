<template>
    <div class="seedlingGrowth">
        <div class="seedlingGrowth_left">
            <div class="seedlingGrowth_left_data">
                <div class="title seedlingGrowth_left_data_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>植株状况</span>
                </div>
                <div class="seedlingGrowth_left_data_con">
                    <div class="seedlingGrowth_left_data_con_search formStyle">
                        <el-select
                            v-model="area"
                            @change="areaChange"
                            popper-class="selectStyle_list"
                            placeholder="请选择区域"
                        >
                            <el-option
                                v-for="item in areaData"
                                :key="item.areaId"
                                :label="item.areaName"
                                :value="item.areaId"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="seedlingGrowth_left_data_con_image">
                        <img
                            src="../assets/image/centralControlPlatform/left-arrow.png"
                            alt=""
                            class="image_arrow_left"
                            @click="leftChange()"
                        />
                        <div class="image_con">
                            <img :src="imageData[imageIndex].pictureUrl" alt="" />
                        </div>
                        <img
                            src="../assets/image/centralControlPlatform/right-arrow.png"
                            alt=""
                            class="image_arrow_right"
                            @click="rightChange()"
                        />
                    </div>
                    <div class="seedlingGrowth_left_data_con_photoTime">
                        拍摄时间：{{ imageData[imageIndex].shootTime }}
                    </div>
                    <div class="seedlingGrowth_left_data_con_info">
                        <div class="infoList">
                            <div class="infoList_item">
                                <div class="infoList_item_name">作物：</div>
                                <div class="infoList_item_value">{{ cropName | cropFormat }}</div>
                            </div>
                            <div class="infoList_item">
                                <div class="infoList_item_name">作物生长期：</div>
                                <div class="infoList_item_value">{{ growingPeriod | growthFormat }}</div>
                            </div>
                        </div>
                        <div class="info_line"></div>
                        <div class="infoList">
                            <div class="infoList_item">
                                <div class="infoList_item_name">苗株高度：</div>
                                <div class="infoList_item_value">{{ detailData.cropHeight }}</div>
                            </div>
                            <div class="infoList_item">
                                <div class="infoList_item_name">杂草覆盖率：</div>
                                <div class="infoList_item_value">{{ detailData.weed }}%</div>
                            </div>
                        </div>
                        <div class="info_line"></div>
                        <div class="infoList">
                            <div class="infoList_item infoList_item2">
                                <div class="infoList_item2_name">采样叶色状况：</div>
                                <div class="infoList_item2_value">
                                    <div v-for="(item, index) in detailData.leafColor" :key="index">
                                        {{ item | leafColorFormat }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="info_line"></div>
                        <div class="infoList">
                            <div class="infoList_item infoList_item2">
                                <div class="infoList_item2_name">苗株长相：</div>
                                <div class="infoList_item2_value">
                                    <div v-for="(item, index) in detailData.cropLooks" :key="index">
                                        {{ item | plantLookFormat }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="info_line"></div>
                        <div class="infoList">
                            <div class="infoList_item infoList_item2">
                                <div class="infoList_item2_name">说明：</div>
                                <div class="infoList_item2_value">{{ detailData.annotation }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="seedlingGrowth_left_data_con_button" v-if="areaIsJudge">
                        <div class="handleBox">
                            <div class="handleBox_item searchButtonStyle" @click="seedlingGrowthUploadDialogOpen">
                                <el-button>变更</el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="seedlingGrowth_right">
            <!-- <div class="seedlingGrowth_right_con" @click="seedlingGrowthMonitorDialogOpen()"> -->
            <div class="seedlingGrowth_right_con">
                <znyAMap :type="6"></znyAMap>
                <!--        <div class="seedlingGrowth_twoDimensionalMap" ></div>-->
            </div>
            <div class="title seedlingGrowth_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="seedlingGrowth_right_btn" @click="floatingWindow">
                <EquipmentButton />
            </div>
        </div>
    </div>
</template>
<script>
import EquipmentButton from '../components/equipmentButton.vue'
import znyAMap from '../components/znyAMap.vue'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
import CropCaseService from '../jaxrs/concrete/com.zny.ia.api.CropCaseService'
export default {
    name: 'seedlingGrowth',
    components: {
        EquipmentButton,
        znyAMap,
    },
    data() {
        return {
            area: '',
            areaIsJudge:true,//判断当前用户能否操作该区域
            areaData: [],
            imageIndex: 0,
            imageData: [{ pictureUrl: '', shootTime: '' }],
            detailData: [],
            imageLoadIndex: 0,
            cropName: '',
            growingPeriod: '',
        }
    },
    mounted() {
        this.getAreaData()
        this.listen('takePhotoIsSuccess', () => {
            this.getAreaData()
        })
    },
    methods: {
        floatingWindow() {
            this.$router.push('/floatingWindow')
        },
        //获取区域信息
        getAreaData() {
            CommonService.allAreaListInformation().then(res => {
                this.area = res[0].areaId
                this.areaData = res
                this.getPictureData()
                this.areaJudge()
            })
        },
        // // 实时监控弹窗
        // seedlingGrowthMonitorDialogOpen(){
        //   this.zEmit('seedlingGrowthMonitorDialogOpen',this.area);
        // },
        //区域改变
        areaChange(val) {
            this.area = val
            this.getPictureData()
            this.areaJudge()
        },
        // 判断当前用户能否操作该区域
        areaJudge(){
            CommonService.areaJudge(this.area)
            .then(res=>{
                // console.log(res);
                this.areaIsJudge=res
            })
        },
        //获取图片信息
        getPictureData() {
            let ref = ++this.imageLoadIndex
            this.imageData = [{ pictureUrl: '', shootTime: '' }]
            CropCaseService.findCropCasePic(this.area).then(res => {
                if (ref !== this.imageLoadIndex) return
                this.imageData = res
                if (res.length == 0) {
                    this.imageData = [{ pictureUrl: '', shootTime: '' }]
                }
                this.getDetail(this.imageData[0].shootTime)
                this.imageIndex = 0
            })
        },
        //获取苗情信息
        getDetail(time) {
            CropCaseService.findCropCasePicAnalyse(this.area, time).then(res => {
                this.detailData = res
                this.cropName = res.cropName
                this.growingPeriod = res.growingPeriod
            })
        },
        //图片左切换
        leftChange() {
            if (this.imageIndex <= 0) {
                return
            } else {
                this.imageIndex--
                this.getDetail(this.imageData[this.imageIndex].shootTime)
            }
        },
        //图片右切换
        rightChange() {
            if (this.imageIndex >= this.imageData.length) {
                return
            } else {
                this.imageIndex++
                this.getDetail(this.imageData[this.imageIndex].shootTime)
            }
        },
        // 变更弹窗
        seedlingGrowthUploadDialogOpen() {
            this.zEmit('seedlingGrowthUploadDialogOpen', this.detailData)
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../assets/css/seedlingGrowth.less';
</style>

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 预防病情
     * desc: --
     * value: 0
     */
    预防病情: 0,
    /**
     * label: 虫病明确
     * desc: --
     * value: 1
     */
    虫病明确: 1,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
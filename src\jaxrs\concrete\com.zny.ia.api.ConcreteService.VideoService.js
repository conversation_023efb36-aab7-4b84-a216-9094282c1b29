/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const VideoService = {
    /**
     * 历史视频 - 返回视频url
     * @param {*} videoId 视频id
     * @returns Promise 
     */
    'detailVideo': function (videoId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7995c3da8e482f199587f2c09af9971fe7c6c0d2', videoId)
            : execute(concreteModuleName, `/VideoService/detailVideo`, 'text', 'POST', { videoId });
    }, 
    /**
     * 历史视频列表
     * @param {*} po 
     * @returns Promise 
     */
    'listVideo': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c00feb80226b83afd1ae6d1cdf37b8bc25d1c0cd', po)
            : execute(concreteModuleName, `/VideoService/listVideo`, 'json', 'POST', po);
    }
}

export default VideoService

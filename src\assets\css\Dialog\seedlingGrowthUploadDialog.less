.seedlingGrowthUploadDialog{
  .el-dialog{
    height: 760px;
    margin-top: 160px !important;
    .el-dialog__body{
      height: 477px;
      .line{
        width: 580px;
        margin: auto;
      }
      .analyse{
        width: 548px;
        height: 586px;
        overflow-y: scroll;
        margin-top: 20px;
        .el-select{
          width: 100%;
          height: 100%;
        }
        .el-input{
          width: 100%;
          height: 100%;
          .el-select__caret{
            line-height: 100%;
          }
          .el-input__inner{
            height:  100%;;
            height: 100%;
          }
        }
        .analyse_list{
          width: 100%;
          display: flex;
          align-items: center;
          &_item{
            width: 50%;
            display: flex;
            align-items: center;
            &_title{
              width: 98px;
              height: 15px;
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #DFEEF3;
              line-height: 20px;
              text-align: right;
              margin-right: 20px;
            }
            &_title2{
              width: 84px;
            }
            &_value{
              width: 126px;
              height: 32px;
            }
          }
          &_item2{
            width: 37%;
          }
          &_item2:first-child{
            width: 63%;
          }
        }
        .analyse_list{
          width: 100%;
          margin-top: 16px;
          display: flex;
          align-items: center;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #DFEEF3;
          line-height: 20px;
          &_title{
            width: 98px;
            text-align: right;
            margin-right: 20px;
          }
          &_title2{
            align-self: flex-start;
          }
          &_value{
            .value_input{
              width: 50px;
              height: 32px;
              margin: 0 13px;
              display: inline-block;
            }
            .value_input2{
              margin: 0 13px 0 0;
            }
          }
          &_value2{
            &_add{
              width: 21px;
              height: 21px;
              display: inline-block;
              cursor: pointer;
            }
            &_selected{
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              &_add{
                width: 21px;
                height: 21px;
                margin-left: 16px;
                display: inline-block;
                cursor: pointer;
              }
            }
            &_select{
              width: 240px;
              height: 32px;
              display: flex;
              align-items: center;
              margin-top: 10px;
              &_add{
                width: 21px;
                height: 21px;
                margin-left: 16px;
                display: inline-block;
                cursor: pointer;
              }
            }
          }
          &_inputArea{
            width: 403px;
            height: 120px;
          }
          &_select{
            width: 385px;
            height: 32px;;
          }
        }
        .handleBox{
          height: 42px;
          margin: 24px 0 20px 0;
          display: flex;
          justify-content: center;
          .handleBox_item{
            width: 160px;
            height: 42px;
            border-radius: 2px 4px 4px 4px;
            .el-button{
              padding: 13px 48px;
            }
          }
        }
        .analyse_opinion{
          width: 100%;
          height: auto;
          border: 1px solid rgba(0,225,255,.4);
          &_header{
            //width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 20px 15px 17px;
            &_text{
              width: 72px;
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #DFEEF3;
              line-height: 20px;
            }
            &_added{
              width: 80px;
              height: 26px;
              .handleBox_item{
                width: 100%;
                height: 100%;
                .el-button{
                  padding: 7px 28px;
                }
              }
            }
          }
          &_con{
            margin: 0 18px 18px 18px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #DFEEF3;
            line-height: 22px;
          }
        }
      }
      .btnBox{
        width: 567px;
        height: 84px;
        display: flex;
        justify-content: center;
        align-items: center;
        .submit_button{
          width: 120px;
          .el-button{
            width: 120px;
            height: 42px;
          }
        }
        .submit_button:nth-child(1){
          margin-right: 60px;
        }
      }
    }
  }
}
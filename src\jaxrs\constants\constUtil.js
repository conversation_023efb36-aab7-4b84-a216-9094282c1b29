const allValues = c => {
    var a = []
    for (var k in c) {
        if (typeof c[k] === 'function') continue
        a.push(c[k])
    }
    return a
}

export function valueOf(c, value) {
    for (var k in c) {
        if (typeof c[k] === 'function') continue
        if (c[k] === value) {
            return {
                key: k,
                value: c[k],
            }
        }
    }
    return null
}

export function toArray(c, values) {
    var result = []
    if (!values) values = allValues(c)
    for (var i = 0, l = values.length; i < l; i++) {
        var v = valueOf(c, values[i])
        if (v) result.push(v)
    }
    return result
}
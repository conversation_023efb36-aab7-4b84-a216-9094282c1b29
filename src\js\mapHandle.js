import img0 from '../assets/image/monitoringCenter/img0.png';
import img1 from '../assets/image/monitoringCenter/img1.png';
import img2 from '../assets/image/monitoringCenter/img2.png';
import img3 from '../assets/image/monitoringCenter/img3.png';
let mapHandle={

  // 地图放marker
  addMarker(map, block, opts) {
    let { bgImg, zIndex, selectGroup, showImg, onClick, position } = Object.assign(
      {
        // position: [block.longitude, block.latitude]
      },
      opts || {}
    )
    block.numSelectGroup = selectGroup
    let markerContent
    if(block.cropId==undefined){
      showImg=false
    }
    if (showImg) {
      let img;
      switch (block.cropId) {
        case 0:
          img=img0
          break;
        case 1:
          img=img1
          break;
        case 2:
          img=img2
          break;
        case 3:
          img=img3
          break;
      }
      markerContent =
        '' +
        '<div class="custom-content-marker" style="position: relative;">' +
          '<img src="' +
            bgImg +
          '">' +
          '<div class="close-btn" style="word-break:normal;position:absolute;top:13px;left:15px;color:#fff;">' +
            '<img src="'+img+'">'+
          '</div>' +
        '</div>'
    } else {
      markerContent =
        '' +
        '<div class="custom-content-marker" style="position: relative;">' +
        '   <img src="' +
        bgImg +
        '">' +
        '</div>'
    }

    let marker = new map.Marker({
      selectGroup,
      content: markerContent,
      position: position,
      zIndex: zIndex,
      extData: block
    })
    onClick &&
      marker.bind('click', e => {
        onClick(e)
      })
    map.addMarker(marker)
    return marker
  },
  // 显示区域边框
  addPlatPolygon(map, block, opts){
    let polygonPath=block.polygonPath
    let {
      color,
      opacity,
      selectColor,
      selectOpacity,
      zIndex,
      selectzIndex,
      selectGroup,
      onClick,
      onDblClick,
      // onSelected,
      // onUnselected,
      // keyOfGeo,
      // noCurve,
      borderStyle
    } = Object.assign(
      {
        color: '#F7B618',
        selectColor: '#fafa04',
        selectable: false,
        keyOfGeo: 'll',
        noCurve: false
      },
      opts || {}
    )

    // let polygonPath = noCurve ? block[keyOfGeo] : createCurve(block[keyOfGeo])
    // block.polSelectGroup = selectGroup
    let polygon = new map.Polygon({
      selectGroup, // 用整数
      selected: {
        border: {
          weight: 2,
          color: selectColor || '#FFFF00',
          opacity: 1
        },
        fill: {
          color: selectColor || '#FFFF00',
          opacity: selectOpacity || 0.5
        },
        zIndex: selectzIndex
      },
      zIndex: zIndex,
      border: {
        weight: 2,
        color: color || '#FFDE36',
        opacity: 1,
        style: borderStyle || 'solid'
      },
      fill: {
        color: color || '#FFDE36',
        opacity: opacity || 0.5
      },
      path: polygonPath,
      extData: block
    })
    if (onClick) polygon.bind('click', onClick)
    if (onDblClick) polygon.bind('dblclick', onDblClick)
    // if (onSelected) polygon.bind('selected', onSelected)
    // if (onUnselected) polygon.bind('unselect', onUnselected)
    
    map.addPolygon(polygon)
    
    return polygon
  },
}
export{mapHandle}
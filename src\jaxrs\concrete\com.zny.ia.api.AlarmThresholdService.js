/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AlarmThresholdService = {
    /**
     * 重置阈值
     * @param {*} cropId 作物id
     * @param {*} growStage 作物成长阶段
     * @returns Promise 
     */
    'resetAlarmThreshold': function (cropId, growStage) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '13fb744458ad195eb6d0d6891cd3907eb203f327', {cropId, growStage})
            : execute(concreteModuleName, `/AlarmThresholdService/resetAlarmThreshold`, 'json', 'POST', { cropId, growStage });
    }, 
    /**
     * 设置了阈值的作物列表
     * @returns Promise 
     */
    'listAlarmThresholdCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd51f93b01ae7813deeab91204188674d168e9ce7')
            : execute(concreteModuleName, `/AlarmThresholdService/listAlarmThresholdCrop`, 'json', 'GET');
    }, 
    /**
     * 根据作物获取对应生长阶段
     * @param {*} cropId 作物id
     * @returns Promise 
     */
    'listGrowStageByCrop': function (cropId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '78ef9be689418f5690649313033e1d9cb8e86ed1', cropId)
            : execute(concreteModuleName, `/AlarmThresholdService/listGrowStageByCrop`, 'json', 'POST', { cropId });
    }, 
    /**
     * 阈值详情
     * @param {*} cropId 作物id
     * @param {*} growStage 作物成长阶段
     * @returns Promise 
     */
    'detailAlarmThresholdInfo': function (cropId, growStage) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4e41201f38c7704eebcc7e2df1c74f5a8cfe8c9d', {cropId, growStage})
            : execute(concreteModuleName, `/AlarmThresholdService/detailAlarmThresholdInfo`, 'json', 'POST', { cropId, growStage });
    }, 
    /**
     * 编辑阈值
     * @param {*} po 
     * @returns Promise 
     */
    'updateAlarmThreshold': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd3dd69e674a5617d3c845b97adcaebe2b912915f', po)
            : execute(concreteModuleName, `/AlarmThresholdService/alarmThreshold`, 'json', 'PUT', po);
    }
}

export default AlarmThresholdService

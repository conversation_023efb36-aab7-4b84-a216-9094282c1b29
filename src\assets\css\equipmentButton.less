.equipmentButton{
  width: auto;
  height: auto;
  display: flex;
  .button{
    width: 190px;
    height: 78px;
    display: flex;
    align-items: center;
    justify-content: center;
    &_img{
      img{
        width: 50px;
        height: 56px;
        margin: 0 18px 0 33px;
      }
    }
    &_text{
      width: 100px;
      font-weight: 400;
      div:first-child {
        font-size: 21px;
        font-weight: bold;
      }
      div:last-child {
        margin-top: 10px;
        font-size: 14px;
      }
    }
  }
  .button:nth-child(1){
    background: url(../image/centralControlPlatform/border1.png) no-repeat 100% center;
    margin-right: 60px;
    color: #EE0101;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.2);
      box-shadow: 0 0 15px rgba(238, 1, 1, 0.3);
    }

    &:active {
      transform: scale(0.98);
    }
  }
  .button:nth-child(2){
    background: url(../image/centralControlPlatform/border2.png) no-repeat 100% center;
    margin-right: 60px;
    color: #FFA72A;
  }
  .button:nth-child(3){
    background: url(../image/centralControlPlatform/border3.png) no-repeat 100% center;
    color: #00FFDD;
  }
}
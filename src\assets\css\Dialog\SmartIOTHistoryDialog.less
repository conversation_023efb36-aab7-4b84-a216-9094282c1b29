.smartIOTHistoryDialog{
  .el-dialog{
    height: 1045px;
    // padding: 30px;
    margin-top: 2vh !important;
    .el-dialog__header{
      padding:24px 30px 12px 30px;
    }
    .el-dialog__body{
      height: 950px;
      .line{
        margin: auto;
      }
      .time{
        position: absolute;
        top: 24px;
        left: 165px;
        font-size: 18px;
        font-weight: 400;
        color: #DFEEF3;
      }
      .handleBox{
        display: flex;
        // margin-top: 30px;
        position: relative;
        &_item{
          position: absolute;
          top: -55px;
          right: 70px;
          .el-button{
            border-radius: 4px !important;
          }
        }
      }
      .dataList{
        margin-top: 30px;
        .list1{
          margin: auto;
          width: 95%;
          .list_item{
            display: inline-block;
            margin-right: 65px;
            // vertical-align: top;
            &:last-child {
              margin-right: 0;
            }
            .item_img{
              display: inline-block;
              // vertical-align: top;
              margin-right: 16px;
              img{
                width: 42px;
                height: 55px;
              }
            }
            .item_text{
              display: inline-block;
              // vertical-align: top;
              font-weight: 400;
              text-align: center;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
      }
      .chartList{
        height: 675px;
        margin-top: 47px;
        display: flex;
        &_left,&_right{
          width: 680px;
          height: 675px;
          .chartList_item{
            background: rgba(255,255,255,0);
            border: 1px solid #00F4FD;
            box-shadow:inset 0px 0px 28px 0px rgba(0,244,253,0.3);
          }
        }
        &_left{
          .chartList_item{
            width: 680px;
            height: 260px;
            position: relative;
            text-align: center;
            margin-bottom: 10px;
            &_title{
              margin-top: 10px;
              font-size: 16px;
              font-weight: 400;
              position: absolute;
              left: 14px;
              color: #DFEEF3;
            }
            &_checkBtn{
              position: absolute;
              top: 10px;
              right: 10px;
              display: flex;
              .checkBtn1{
                width: 42px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di1.png) no-repeat 100% center;
              }
              .checkBtn2{
                width: 63px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di3.png) no-repeat 100% center;
              }
              .checkBtn{
                margin-left: 21px;
                font-size: 14px;
                font-weight: 400;
                color: #DFEEF3;
                cursor: pointer;
                line-height: 20px;
                text-align: center;
              }
              .active1{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di2.png) no-repeat 100% center;
              }
              .active2{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di4.png) no-repeat 100% center;
              }
            }
            &_chart{
              width: 680px;
              height: 160px;
              margin-top: 20px;
            }
          }
        }
        &_right{
          margin-left: 13px;
          .chartList_item{
            width: 680px;
            height: 397px;
            position: relative;
            text-align: center;
            margin-bottom: 10px;
            &_title{
              margin-top: 10px;
              font-size: 16px;
              font-weight: 400;
              position: absolute;
              left: 14px;
              color: #DFEEF3;
            }
            &_checkBtn{
              position: absolute;
              top: 10px;
              right: 10px;
              display: flex;
              .checkBtn1{
                width: 42px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di1.png) no-repeat 100% center;
              }
              .checkBtn2{
                width: 63px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di3.png) no-repeat 100% center;
              }
              .checkBtn{
                margin-left: 21px;
                font-size: 14px;
                font-weight: 400;
                color: #DFEEF3;
                cursor: pointer;
                line-height: 20px;
                text-align: center;
              }
              .active1{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di2.png) no-repeat 100% center;
              }
              .active2{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di4.png) no-repeat 100% center;
              }
            }
            &_chart{
              width: 680px;
              height: 319px;
              margin-top: 20px;
            }
          }
        }
      }
    }
  }
}

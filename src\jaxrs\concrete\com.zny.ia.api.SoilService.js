/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const SoilService = {
    /**
     * 土壤历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'listHistoryRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3c3b55fc14b1aa2e8cc996c2a7e4dd957350c2a8', po)
            : execute(concreteModuleName, `/SoilService/listHistoryRecord`, 'json', 'POST', po);
    }, 
    /**
     * 土壤报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'listAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ad8b0e0de1ce92054a5810bc070c6c689a66081c', po)
            : execute(concreteModuleName, `/SoilService/listAlarmRecord`, 'json', 'POST', po);
    }, 
    /**
     * 灌溉主页面-最近七天累计土壤墒情统计
     * @param {*} areaId 区域id null-全部
     * @returns Promise 
     */
    'cumulativeSoilListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '90404affced32347d9d0c468c8e726733d3bf3d6', areaId)
            : execute(concreteModuleName, `/SoilService/cumulativeSoilListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 土壤数据汇报
     * @param {*} po 
     * @returns Promise 
     */
    'addData': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '16708950ba87631f07d1b5ca15e9ad40cfed8533', po)
            : execute(concreteModuleName, `/SoilService/addData`, 'json', 'POST', po);
    }, 
    /**
     * 土壤聚合图表
     * @param {*} po 
     * @returns Promise 
     */
    'listChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3fda5569df260fcfb3dcc5124b317f1329ab5646', po)
            : execute(concreteModuleName, `/SoilService/listChart`, 'json', 'POST', po);
    }, 
    /**
     * 土壤报警
     * @returns Promise 
     */
    'listAlarm': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ef2cbb0217843b7ae992484ea92d3dc77ca37f64')
            : execute(concreteModuleName, `/SoilService/listAlarm`, 'json', 'GET');
    }, 
    /**
     * 获取实时土壤数据
     * @param {*} layer 土壤层
     * @param {*} areaId 区域id null-所有
     * @returns Promise 
     */
    'findNewInfo': function (layer, areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '046e534e57b0d1ec1094654d59bd63d4aa210704', {layer, areaId})
            : execute(concreteModuleName, `/SoilService/findNewInfo`, 'json', 'POST', { layer, areaId });
    }
}

export default SoilService

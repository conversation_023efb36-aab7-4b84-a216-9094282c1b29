.waternumber_button {
    display: flex;
    margin-top: 20px;
    justify-content: center;
    .cancel_button {
        width: 120px;
        height: 42px;
        background: rgba(1, 18, 21, 0);
        box-shadow: 0px 0px 10px 0px rgba(0, 244, 253, 0.5) inset;
        border-radius: 2px 4px 4px 4px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        line-height: 42px;
        text-align: center;
        cursor: pointer;
    }
    .confirm_button {
        margin-left: 60px;
        width: 120px;
        height: 42px;
        background: rgba(1, 18, 21, 0);
        box-shadow: 0px 0px 10px 0px rgba(243, 161, 52, 0.8) inset;
        border-radius: 2px 4px 4px 4px;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        line-height: 42px;
        text-align: center;
        cursor: pointer;
    }
}
.checkDialog ::v-deep .el-dialog {
    .clone {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .wire {
        height: 2px;
        background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
    }
    .content {
        padding-top: 29px;
        .content_imgbox {
            display: flex;
            .content_img {
                width: 780px;
                height: 648px;
                object-fit: cover;
                margin: auto;
            }
        }
        .content_row1_col1 {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #dfeef3;
        }
        .content_row2_col1 {
            margin-top: 17px;
        }
        .content_row2_col2 {
            margin-top: 11px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }
        .content_row1_col2 {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
        }
        .content_row1 {
            display: flex;
            margin-top: 19px;
        }
    }
}
.analyseDialog ::v-deep .el-dialog {
    height: 405px;
    margin-top: 214px !important;
    .el-dialog__body {
        height: 307px;
    }
    .clone {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .wire {
        height: 2px;
        background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
    }
    .content {
        margin-top: 20px;
    }
}
.waterMonitorDialog ::v-deep .el-dialog {
    height: 640px;
    margin-top: 214px !important;
    .el-dialog__body {
        height: 580px;
    }
    .clone {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .wire {
        height: 2px;
        background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
    }
    .content {
        width: 100%;
        padding-top: 29px;
        .content_overflow {
            height: 446px;
            overflow: auto;

            .content_flex {
                display: flex;
                justify-content: end;
                margin-bottom: 18px;
                .text {
                    line-height: 40px;
                    width: 10%;
                    font-size: 16px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #dfeef3;
                }
            }
        }
        .el-form-item__label {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
        }
        // .el-input__inner {
        //     height: 40px !important;
        // }
        .el-checkbox__inner {
            background: #04b0ba !important;
            border: 0px;
        }
        .el-checkbox {
            color: #dfeef3;
            .el-checkbox__label {
                color: #dfeef3;
            }
        }
    }
}
.remoteSens_content {
    height: 100%;
    overflow-y: scroll;
    padding: 0px 10px;
    padding-top: 20px;
    box-sizing: border-box;
    .remoteSens_box {
        width: 440px;
        height: 140px;
        background: url('../../assets/image/eppoWisdom/remoteSens.png');
        background-size: 100% 100%;
        margin-bottom: 20px;
        display: flex;
        .box_left {
            width: 160px;
            height: 106px;
            object-fit: cover;
            margin-top: auto;
            margin-bottom: auto;
            margin-left: 10px;
        }
        .box_right {
            margin-left: 15px;
            .box_right_text {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #dfeef3;
            }
            .box_right_row1 {
                margin-top: 22px;
            }
            .box_right_row2 {
                margin-top: 28px;
            }
            .box_right_row3 {
                margin-top: 9px;
                width: 211px;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; //溢出不换行
            }
        }
    }
}
.farming {
    padding-left: 25px;
    padding-top: 25px;
    .farming_row1 {
        display: flex;
        .farming_row1_left {
            width: 425px;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            overflow: hidden; //超出的文本隐藏
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
        }
        .farming_row1_right {
            width: 110px;
            height: 32px;
            background: rgba(1, 18, 21, 0);
            box-shadow: 0px 0px 10px 0px rgba(0, 244, 253, 0.5) inset;
            border-radius: 2px;
            text-align: center;
            line-height: 32px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            margin-left: 308px;
            cursor: pointer;
        }
    }
    .farming_row2 {
        margin-top: 20px;
        display: flex;
        position: relative;
        .farming_row2_img {
            width: 843px;
            height: 648px;
            object-fit: cover;
        }
        .farming_row2_box {
            position: absolute;
            bottom: 24px;
            left: 864px;
            .box_title {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #dfeef3;
                margin-bottom: 20px;
            }
            .box_row1 {
                display: flex;
                .box_row1_color {
                    width: 80px;
                    height: 26px;
                    background: #ea7544;
                }
                .box_row2_color {
                    width: 80px;
                    height: 26px;
                    background: #ffa004;
                }
                .box_row3_color {
                    width: 80px;
                    height: 26px;
                    background: #b8f61b;
                }
                .box_row4_color {
                    width: 80px;
                    height: 26px;
                    background: #59ff35;
                }
                .box_row5_color {
                    width: 80px;
                    height: 26px;
                    background: #13ca1c;
                }
                .box_row6_color {
                    width: 80px;
                    height: 26px;
                    background: #17800e;
                }
                .box_row7_color {
                    width: 80px;
                    height: 26px;
                    background: #09450b;
                }
                .box_row1_text {
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #dfeef3;
                    margin-left: 14px;
                }
            }
        }
    }
    .farming_row3 {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #dfeef3;
        margin-top: 16px;
    }
    .farming_row4 {
        display: flex;
        margin-top: 16px;
        .farming_row4_col1 {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #dfeef3;
        }
        .farming_row4_col2 {
            width: 110px;
            height: 32px;
            background: rgba(1, 18, 21, 0);
            box-shadow: 0px 0px 10px 0px rgba(0, 244, 253, 0.5) inset;
            border-radius: 2px;
            margin-left: 29px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            text-align: center;
            line-height: 32px;
            cursor: pointer;
        }
    }
    .farming_row5 {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #dfeef3;
        margin-top: 15px;
    }
}
.zuobian {
    width: 33px;
    height: 47px;
    margin: auto;
    margin-right: 30px;
    cursor: pointer;
}
.youbian {
    width: 33px;
    height: 47px;
    margin: auto;
    margin-left: 30px;
    cursor: pointer;
}

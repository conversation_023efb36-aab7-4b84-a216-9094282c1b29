/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppWormCaseService = {
    /**
     * 首页虫情数据
     * @param {*} po 
     * @returns Promise 
     */
    'wormCaseData': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a779d34978ec851946d003ce632b05fa643f8706', po)
            : execute(concreteModuleName, `/App/WormCaseService/wormCaseData`, 'json', 'POST', po);
    }, 
    /**
     * 虫情实时拍摄
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'realTimeShoot': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '816ee445e788e35637b8ebfb9259bc2e87347092', equipmentId)
            : execute(concreteModuleName, `/App/WormCaseService/realTimeShoot`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 查询往期图片
     * @param {*} po 
     * @returns Promise 
     */
    'findPictureBackList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e69d588d626bc3d98e823c6bc6227be6eecca36b', po)
            : execute(concreteModuleName, `/App/WormCaseService/findPictureBackList`, 'json', 'POST', po);
    }, 
    /**
     * 设备状态页面接口
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findWormCaseEqStatus': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f0a710b4c4bb1fc71d6071252ac3e813c1ccfd68', equipmentId)
            : execute(concreteModuleName, `/App/WormCaseService/findWormCaseEqStatus`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 报警信息查看
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'selectAlarmInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4c86cca6c9463e080d8ace3d822d29c93acfe167', areaId)
            : execute(concreteModuleName, `/App/WormCaseService/selectAlarmInfo`, 'json', 'POST', { areaId });
    }
}

export default AppWormCaseService

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const RemoteSensingService = {
    /**
     * 作物长势分析
     * @returns Promise 
     */
    'findRemoteCropHeight': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2a1a6829186f9da8bcf94e87a6756f62414156f5')
            : execute(concreteModuleName, `/RemoteSensingService/findRemoteCropHeight`, 'json', 'GET');
    }, 
    /**
     * 上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'addRemote': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8451155f0d018540628d9d77ccdcaa7b0d24ef5c', po)
            : execute(concreteModuleName, `/RemoteSensingService/addRemote`, 'json', 'POST', po);
    }, 
    /**
     * 遥感相关历史
     * @param {*} page 第几页
     * @param {*} num 页大小
     * @returns Promise 
     */
    'listRemoteHistory': function (page, num) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd2b49aa0bc0de5210f35e608bd0adc4fe1f379b2', {page, num})
            : execute(concreteModuleName, `/RemoteSensingService/listRemoteHistory`, 'json', 'POST', { page, num });
    }, 
    /**
     * 遥感详情
     * @param {*} id 遥感记录id
     * @returns Promise 
     */
    'detailRemote': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6af2f8059e25e8b71ffafe4ef6705ba7caf2dbc3', id)
            : execute(concreteModuleName, `/RemoteSensingService/detailRemote`, 'json', 'POST', { id });
    }, 
    /**
     * 补充遥感分析结果
     * @param {*} po 
     * @returns Promise 
     */
    'addRemoteDescription': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2b75ae3aac0ea717cc2362bd0c5088ee1d622d69', po)
            : execute(concreteModuleName, `/RemoteSensingService/addRemoteDescription`, 'json', 'POST', po);
    }, 
    /**
     * 根据时间和区域获取作物种类
     * @param {*} dateTime 日期时间 yyyy-MM-dd HH:mm:ss
     * @param {*} areaIdList 区域id列表
     * @returns Promise 
     */
    'listCropType': function (dateTime, areaIdList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e62ef6441d86f3896936559a46c31bdb9fc7c2dd', {dateTime, areaIdList})
            : execute(concreteModuleName, `/RemoteSensingService/listCropType`, 'json', 'POST', { dateTime, areaIdList });
    }, 
    /**
     * 遥感作物占比
     * @returns Promise 
     */
    'findRemoteCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '45777f5ec480644fd034c7a0ae9224932123d2cc')
            : execute(concreteModuleName, `/RemoteSensingService/findRemoteCrop`, 'json', 'GET');
    }
}

export default RemoteSensingService

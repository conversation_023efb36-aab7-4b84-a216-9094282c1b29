<template>
    <div class="soil">
        <div class="soil_left">
            <!-- 智慧物联 -->
            <div class="soil_left_smartIOTData">
                <div class="title soil_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧物联数据</span>
                </div>
                <div class="soil_left_smartIOTData_con">
                    <div class="handleBox">
                        <div class="handleBox_item formStyle">
                            <el-select
                                v-model="layer"
                                placeholder="请选择土壤层"
                                popper-class="selectStyle_list"
                                @change="layerChange"
                            >
                                <el-option
                                    v-for="item in soilLayerData"
                                    :key="item.value"
                                    :label="item.key"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div style="margin-top: -40px;">
                        <div class="soil_left_smartIOTData_con_topList">
                            <div class="topList1">
                                <div class="topList_item">
                                    <div class="item_img">
                                        <img src="../assets/image/monitoringCenter/icon1.png" alt="" />
                                    </div>
                                    <div class="item_text">
                                        <div v-if="newInfo.soilTemperature==null">--</div>
                                        <div v-else>{{ newInfo.soilTemperature }}℃</div>
                                        <div>温度</div>
                                    </div>
                                </div>
                                <div class="topList_item">
                                    <div class="item_img">
                                        <img src="../assets/image/monitoringCenter/icon4.png" alt="" />
                                    </div>
                                    <div class="item_text">
                                        <div v-if="newInfo.soilHumidity==null">--</div>
                                        <div v-else>{{ newInfo.soilHumidity }}%</div>
                                        <div>墒情</div>
                                    </div>
                                </div>
                            </div>
                            <div class="topList2">
                                <div class="topList_item">
                                    <div class="item_img">
                                        <img src="../assets/image/monitoringCenter/icon5.png" alt="" />
                                    </div>
                                    <div class="item_text">
                                        <div v-if="newInfo.conductivity==null">--</div>
                                        <div v-else>{{ newInfo.conductivity }}μS/cm</div>
                                        <div>电导率</div>
                                    </div>
                                </div>
                                <div class="topList_item">
                                    <div class="item_img">
                                        <img src="../assets/image/monitoringCenter/icon6.png" alt="" />
                                    </div>
                                    <div class="item_text">
                                        <div v-if="newInfo.ph==null">--</div>
                                        <div v-else>{{ newInfo.ph }}</div>
                                        <div>pH值</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="soil_left_smartIOTData_con_bottomList">
                            <div class="bottomList_item">
                                <div class="item_img">
                                    <img src="../assets/image/centralControlPlatform/icon12.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="newInfo.nitrogen==null">--</div>
                                    <div v-else>{{ newInfo.nitrogen }}mg/kg</div>
                                    <div>氮浓度</div>
                                </div>
                            </div>
                            <div class="bottomList_item">
                                <div class="item_img">
                                    <img src="../assets/image/centralControlPlatform/icon14.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="newInfo.phosphorus==null">--</div>
                                    <div v-else>{{ newInfo.phosphorus }}mg/kg</div>
                                    <div>磷浓度</div>
                                </div>
                            </div>
                            <div class="bottomList_item">
                                <div class="item_img">
                                    <img src="../assets/image/centralControlPlatform/icon13.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="newInfo.potassium==null">--</div>
                                    <div v-else>{{ newInfo.potassium }}mg/kg</div>
                                    <div>钾浓度</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 设备报警 -->
            <div class="soil_left_equipmentAlarm">
                <div class="title soil_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>土壤报警</span>
                </div>
                <div class="soil_left_equipmentAlarm_con">
                    <EquipmentAlarm :type="2" :alarmCollect="alarmCollect" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="soil_right">
            <div class="title soil_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="soil_right_btn" @click="floatingWindow">
                <EquipmentButton />
            </div>
            <!-- <div class="soil_right_con" @click="soilEMDialogOpen"> -->
            <div class="soil_right_con">
                <znyAMap :type="2"></znyAMap>
                <!-- <div class="twoDimensionalMap" @click="soilEMDialogOpen"></div> -->
            </div>
        </div>
    </div>
</template>
<script>
import znyAMap from '../components/znyAMap.vue'
import EquipmentAlarm from '../components/equipmentAlarm.vue'
import EquipmentButton from '../components/equipmentButton.vue'
import SoilService from '../jaxrs/concrete/com.zny.ia.api.SoilService.js'
import data from '../jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js'
export default {
    components: {
        EquipmentAlarm,
        EquipmentButton,
        znyAMap,
    },
    data() {
        return {
            layer: 0,
            soilLayerData: data._toArray(), //土壤层数据
            newInfo: {}, //智能物联数据
            alarmCollect: {}, //报警信息
        }
    },
    mounted() {
        this.findNewInfo()
        this.getListAlarm()
    },
    methods: {
        // 智慧物联数据选择土壤层 切换数据
        layerChange() {
            this.findNewInfo()
        },
        // 获取实时土壤数据
        findNewInfo() {
            SoilService.findNewInfo(this.layer, null).then(res => {
                this.newInfo = res
            })
        },
        // 获取土壤报警
        getListAlarm() {
            SoilService.listAlarm().then(res => {
                this.alarmCollect = res
            })
        },
        floatingWindow() {
            this.$router.push('/floatingWindow')
        },
        // 土壤环境监测弹窗
        // soilEMDialogOpen(){
        //   this.zEmit('soilEMDialogOpen');
        // },
    },
}
</script>
<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/soil.less';
</style>

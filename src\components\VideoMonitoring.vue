<template>
    <div class="video-monitoring">
        <!-- 有视频数据时显示视频 -->
        <div class="video-container" v-if="hasVideoData">
            <video
                ref="mainVideoElement"
                class="video-player"
                :src="videoSrc"
                preload="metadata"
                @error="handleVideoError"
                @play="onMainVideoPlay"
                @pause="onMainVideoPause"
                @ended="onMainVideoEnded"
            >
                您的浏览器不支持视频播放
            </video>
            <!-- 视频信息显示 -->
            <div class="video-info">
                <span class="video-time">{{ currentTime }}</span>
            </div>
            <!-- 主视频控制按钮 -->
            <div class="main-video-controls">
                <div class="control-left">
                    <button class="main-control-btn play-btn" @click.stop="toggleMainVideoPlay">
                        <img :src="isMainVideoPlaying ? videostopIcon : videoplayIcon" alt="播放/暂停" />
                    </button>
                </div>
                <div class="control-right">
                    <button class="main-control-btn dialog-btn" @click.stop="openVideoDialog">
                        <img :src="allScreenIcon" alt="打开弹窗" />
                    </button>
                </div>
            </div>
        </div>

        <!-- 无视频数据时显示无数据样式 -->
        <div class="no-data-container" v-else @click="openVideoDialog">
            <div class="no-data-content">
                <img src="../assets/image/monitoringCenter/noData.png" alt="无数据" />
                <div class="no-data-text">暂无视频数据</div>
            </div>
        </div>

        <!-- 视频监控弹窗 -->
        <div class="meteorologyEMDialog dialogStyle videoMonitorDialog">
            <el-dialog
                title="视频监控"
                width="1256px"
                :visible.sync="dialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="closeVideoDialog" />

                <div class="wire"></div>
                <div class="content">
                    <!-- 控制区域 -->
                    <div class="control-area">
                        <div class="control-item formStyle">
                            <el-select
                                v-model="selectedArea"
                                placeholder="请选择种植区域"
                                popper-class="selectStyle_list"
                                @change="onAreaChange"
                            >
                                <el-option
                                    v-for="area in areaOptions"
                                    :key="area.value"
                                    :label="area.label"
                                    :value="area.value"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="control-item formStyle">
                            <el-select
                                v-model="selectedCamera"
                                placeholder="请选择摄像头"
                                popper-class="selectStyle_list"
                                @change="onCameraChange"
                            >
                                <el-option
                                    v-for="camera in cameraOptions"
                                    :key="camera.value"
                                    :label="camera.label"
                                    :value="camera.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>

                    <!-- 视频播放区域 -->
                    <div class="video-dialog-container">
                        <div class="video-border">
                            <div class="video-window">
                                <!-- 视频内容区域 -->
                                <div class="video-content" v-if="dialogVideoSrc">
                                    <video
                                        ref="videoElement"
                                        class="video-element"
                                        :src="dialogVideoSrc"
                                        preload="metadata"
                                        @error="handleDialogVideoError"
                                        @timeupdate="updateProgress"
                                        @loadedmetadata="onVideoLoaded"
                                        @ended="onVideoEnded"
                                        @play="onVideoPlay"
                                        @pause="onVideoPause"
                                    >
                                        您的浏览器不支持视频播放
                                    </video>
                                    <!-- 视频内部信息显示 -->
                                    <div class="video-info-overlay">
                                        <div class="device-datetime-info">
                                            <div class="device-info">{{ currentDeviceId }}</div>
                                            <div class="datetime-info">{{ currentDateTime }} {{ currentWeekday }}</div>
                                        </div>
                                    </div>
                                    <!-- 视频进度条 -->
                                    <div class="video-progress-container">
                                        <div class="video-progress-bar" @click="seekVideo">
                                            <div class="progress-track">
                                                <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 视频控制按钮 -->
                                    <div class="video-controls">
                                        <div class="control-left">
                                            <button class="control-btn play-pause-btn" @click="togglePlayPause">
                                                <img :src="isPlaying ? videostopIcon : videoplayIcon" alt="" />
                                            </button>
                                        </div>
                                        <div class="control-right">
                                            <button class="control-btn fullscreen-btn" @click="captureVideo">
                                                <img :src="camera" alt="" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="video-placeholder" v-else>
                                    <img src="../assets/image/monitoringCenter/videoPlay.png" alt="视频播放" class="video-placeholder-icon" />
                                    <div class="video-info">{{ getVideoDisplayText() }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
export default {
    name: 'VideoMonitoring',
    data() {
        return {
            hasVideoData: false, // 控制是否有视频数据，暂时设为false
            videoSrc: '', // 视频源地址，待接口接入时使用
            currentTime: '2022-05-26 13:26:31', // 当前时间，参考图片中的时间格式

            // 弹窗相关数据
            dialogVisible: false, // 弹窗显示状态
            selectedArea: '', // 选中的种植区域
            selectedCamera: '', // 选中的摄像头
            dialogVideoSrc: '', // 弹窗中的视频源

            // 视频播放控制
            isPlaying: false,
            isMainVideoPlaying: false, // 主视频播放状态
            progressPercent: 0, // 视频播放进度百分比
            videoDuration: 0, // 视频总时长
            progressUpdatePending: false, // 进度更新标志
            currentDeviceId: '',
            currentDateTime: '',
            currentWeekday: '',

            // 图标
            videoplayIcon: require('../assets/image/monitoringCenter/videoPlay.png'),
            videostopIcon: require('../assets/image/monitoringCenter/videoStop.png'),
            allScreenIcon: require('../assets/image/monitoringCenter/allScreen.png'),
            camera: require('../assets/image/centralControlPlatform/camera.png'),

            // 种植区域选项
            areaOptions: [
                { value: 'area1', label: '玉米种植一区' },
                { value: 'area2', label: '玉米种植二区' },
                { value: 'area3', label: '小麦种植一区' },
                { value: 'area4', label: '小麦种植二区' },
                { value: 'area5', label: '蔬菜种植区' }
            ],

            // 摄像头选项
            cameraOptions: [
                { value: 'front', label: '前摄像头' },
                { value: 'back', label: '后摄像头' }
            ]
        }
    },
    mounted() {
        // 初始化视频数据
        this.loadVideoData()

        // 初始化时间显示
        this.updateCurrentTime();
        // 定时更新时间（如果需要实时时间）
        this.timeInterval = setInterval(() => {
            this.updateCurrentTime();
        }, 1000);

        // TODO: 这里预留接口调用位置
        // this.getVideoData();
    },
    beforeDestroy() {
        // 清除定时器
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
        }
    },
    methods: {
        // 加载视频数据
        loadVideoData() {
            // TODO: 调用视频接口
            // 暂时模拟有视频数据用于测试按钮效果
            this.hasVideoData = true
            this.videoSrc = 'https://www.w3schools.com/html/mov_bbb.mp4' // 测试视频
            this.currentTime = this.getCurrentTimeString()
        },

        // 更新当前时间
        updateCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        // 获取当前时间字符串
        getCurrentTimeString() {
            const now = new Date()
            return now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0') + ' ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0')
        },

        // 更新时间显示
        updateDateTime() {
            const now = new Date()
            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

            this.currentDateTime = now.getFullYear() + '-' +
                                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                                 String(now.getDate()).padStart(2, '0') + ' ' +
                                 String(now.getHours()).padStart(2, '0') + ':' +
                                 String(now.getMinutes()).padStart(2, '0') + ':' +
                                 String(now.getSeconds()).padStart(2, '0')

            this.currentWeekday = weekdays[now.getDay()]
            this.currentDeviceId = this.selectedCamera ? this.selectedCamera.toUpperCase() : 'CAMERA1'
        },
        
        // 处理视频加载错误
        handleVideoError() {
            console.error('视频加载失败');
            this.hasVideoData = false;
        },

        // 主视频播放事件
        onMainVideoPlay() {
            this.isMainVideoPlaying = true
        },

        // 主视频暂停事件
        onMainVideoPause() {
            this.isMainVideoPlaying = false
        },

        // 主视频播放完成事件
        onMainVideoEnded() {
            this.isMainVideoPlaying = false
        },

        // 切换主视频播放/暂停
        toggleMainVideoPlay() {
            const videoElement = this.$refs.mainVideoElement
            if (videoElement) {
                if (this.isMainVideoPlaying) {
                    videoElement.pause()
                } else {
                    videoElement.play()
                }
            }
        },
        
        // 预留的获取视频数据方法
        getVideoData() {
            // TODO: 调用视频接口
            // VideoService.getVideoData()
            // .then(res => {
            //     if (res && res.videoUrl) {
            //         this.videoSrc = res.videoUrl;
            //         this.hasVideoData = true;
            //     } else {
            //         this.hasVideoData = false;
            //     }
            // })
            // .catch(err => {
            //     console.error('获取视频数据失败:', err);
            //     this.hasVideoData = false;
            // });
        },

        // 打开视频弹窗
        openVideoDialog() {
            this.dialogVisible = true
            // 设置默认选项
            if (this.areaOptions.length > 0) {
                this.selectedArea = this.areaOptions[0].value
            }
            if (this.cameraOptions.length > 0) {
                this.selectedCamera = this.cameraOptions[0].value
            }
            this.loadDialogVideo()
            this.updateDateTime()
        },

        // 关闭视频弹窗
        closeVideoDialog() {
            this.dialogVisible = false
            this.dialogVideoSrc = ''
        },

        // 视频加载完成
        onVideoLoaded() {
            if (this.$refs.videoElement) {
                this.videoDuration = this.$refs.videoElement.duration
            }
        },

        // 更新播放进度
        updateProgress() {
            if (this.$refs.videoElement && this.videoDuration > 0) {
                const currentTime = this.$refs.videoElement.currentTime
                const newProgressPercent = Math.min(100, (currentTime / this.videoDuration) * 100)

                // 使用 requestAnimationFrame 来优化更新频率
                if (!this.progressUpdatePending) {
                    this.progressUpdatePending = true
                    requestAnimationFrame(() => {
                        this.progressPercent = newProgressPercent
                        this.progressUpdatePending = false
                    })
                }
            }
        },

        // 视频播放事件
        onVideoPlay() {
            this.isPlaying = true
        },

        // 视频暂停事件
        onVideoPause() {
            this.isPlaying = false
        },

        // 视频播放完成事件
        onVideoEnded() {
            this.isPlaying = false
            this.progressPercent = 100
        },

        // 播放/暂停切换
        togglePlayPause() {
            if (this.$refs.videoElement) {
                if (this.isPlaying) {
                    this.$refs.videoElement.pause()
                } else {
                    this.$refs.videoElement.play()
                }
            }
        },

        // 点击进度条跳转
        seekVideo(event) {
            if (this.$refs.videoElement && this.videoDuration > 0) {
                const progressBar = event.currentTarget
                const rect = progressBar.getBoundingClientRect()
                const clickX = event.clientX - rect.left
                const progressWidth = rect.width
                const clickPercent = Math.max(0, Math.min(1, clickX / progressWidth))
                const seekTime = clickPercent * this.videoDuration

                this.$refs.videoElement.currentTime = seekTime

                // 立即更新进度条显示
                this.progressPercent = clickPercent * 100

                // 如果视频已经结束，跳转后重置播放状态
                if (this.$refs.videoElement.ended) {
                    this.isPlaying = false
                }
            }
        },

        // 截图功能
        captureVideo() {
            console.log('视频截图')
            // TODO: 实现截图功能
            // 可以使用 canvas 来截取视频当前帧
        },

        // 获取视频显示文本
        getVideoDisplayText() {
            if (this.selectedArea && this.selectedCamera) {
                return `${this.selectedArea} - ${this.selectedCamera}`
            }
            return '请选择查询条件'
        },

        // 区域选择改变
        onAreaChange() {
            this.loadDialogVideo()
        },

        // 摄像头选择改变
        onCameraChange() {
            this.loadDialogVideo()
        },

        // 加载弹窗视频
        loadDialogVideo() {
            // 这里根据选中的区域和摄像头加载对应的视频
            // 暂时模拟有视频数据用于测试
            this.dialogVideoSrc = 'https://www.w3schools.com/html/mov_bbb.mp4'
            this.updateDateTime()
        },

        // 处理弹窗视频加载错误
        handleDialogVideoError() {
            console.error('弹窗视频加载失败')
            this.dialogVideoSrc = ''
        }
    }
}
</script>

<style lang="less" scoped>
.video-monitoring {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .video-container {
        width: 386px;
        height: 217px;
        position: relative;
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .video-player {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #000;
        }

        .video-info {
            position: absolute;
            top: 8px;
            left: 8px;
            // background: rgba(0, 0, 0, 0.6);
            padding: 4px 8px;
            border-radius: 2px;

            .video-time {
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                font-weight: normal;
            }
        }

        // 主视频控制按钮
        .main-video-controls {
            position: absolute;
            bottom: 8px;
            right: 8px;
            display: flex;
            align-items: center;
            gap: 135px;

            .control-left,
            .control-right {
                display: flex;
                align-items: center;
            }

            .main-control-btn {
                width: 32px;
                height: 32px;
                background: transparent;
                border: none;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.05);
                }

                img {
                    width: 20px;
                    height: 20px;
                    filter: brightness(0) invert(1); // 将图标变为白色
                }
            }

            .play-btn {
                img {
                    width: 16px;
                    height: 16px;
                }
            }

            .dialog-btn {
                img {
                    width: 18px;
                    height: 18px;
                }
            }
        }
    }
    
    .no-data-container {
        width: 386px;
        height: 217px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.2);
        }

        .no-data-content {
            text-align: center;

            img {
                width: 60px;
                height: 60px;
                margin-bottom: 12px;
                opacity: 0.6;
            }

            .no-data-text {
                color: rgba(255, 255, 255, 0.6);
                font-size: 16px;
                font-weight: 400;
            }
        }
    }
}

// 弹窗样式
.videoMonitorDialog ::v-deep .el-dialog {
    height: 854px;
    margin-top: 113px !important; // 调整弹窗垂直位置，可根据需要修改
    // margin-left: auto !important; // 水平居中，如需偏移可设置具体数值

    .el-dialog__header {
        padding: 20px 20px 0 20px;
        text-align: left; // title居中显示

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
            // left: 0; // 如需微调title位置，可修改此值
        }
    }

    .el-dialog__body {
        height: calc(100% - 74px); // 调整body高度以适应header
        padding: 13px 30px 30px 30px;

        .clone {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            z-index: 10;
        }

        .wire {
            height: 2px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content {
            height: calc(100% - 22px);

            .control-area {
                display: flex;
                width: 1120px;
                gap: 30px;
                margin-bottom: 20px;
                margin-left: 20px;

                .control-item {
                    display: flex;
                    align-items: center;
                    height: 32px;
                    color:#FEFFFF;

                    .control-label {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #DFEEF3;
                        margin-right: 10px;
                        white-space: nowrap;
                    }

                    .el-select {
                        width: 200px;
                    }
                }
            }

            .video-dialog-container {
                width: 100%;
                display: flex;
                justify-content: center;

                .video-border {
                    width: 1152px;
                    height: 662px;
                    border: 1px solid rgba(0, 244, 253, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.1);

                    .video-window {
                        width: 1120px;
                        height: 630px;
                        background: #000000;
                        border-radius: 2px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .video-content {
                            width: 100%;
                            height: 100%;
                            position: relative;

                            .video-element {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }

                            .video-info-overlay {
                                position: absolute;
                                top: 10px;
                                left: 10px;
                                color: #DFEEF3;
                                font-size: 14px;
                                font-family: Microsoft YaHei;
                                z-index: 2;

                                .device-datetime-info {
                                    .device-info {
                                        margin-bottom: 5px;
                                    }
                                    .datetime-info {
                                        opacity: 0.8;
                                    }
                                }
                            }

                            // 视频进度条样式
                            .video-progress-container {
                                position: absolute;
                                bottom: 50px;
                                left: 10px;
                                right: 10px;
                                z-index: 3;

                                .video-progress-bar {
                                    width: 100%;
                                    height: 6px;
                                    cursor: pointer;

                                    .progress-track {
                                        width: 100%;
                                        height: 100%;
                                        background: rgba(255, 255, 255, 0.3);
                                        border-radius: 3px;
                                        overflow: hidden;

                                        .progress-fill {
                                            height: 100%;
                                            background: #00E4FF;
                                            border-radius: 3px;
                                            transition: width 0.3s linear;
                                            will-change: width;
                                        }
                                    }

                                    &:hover .progress-track {
                                        background: rgba(255, 255, 255, 0.4);
                                    }
                                }
                            }

                            .video-controls {
                                position: absolute;
                                bottom: 10px;
                                left: 519px;
                                right: 10px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                z-index: 4;

                                .control-btn {
                                    width: 32px;
                                    height: 32px;
                                    background: transparent;
                                    border: none;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;

                                    img {
                                        width: 24px;
                                        height: 24px;
                                    }
                                }
                            }
                        }

                        .video-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;

                            .video-placeholder-icon {
                                width: 64px;
                                height: 64px;
                                opacity: 0.5;
                                margin-bottom: 20px;
                            }

                            .video-info {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                color: #DFEEF3;
                                opacity: 0.7;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/waterMonitor.less';
</style>

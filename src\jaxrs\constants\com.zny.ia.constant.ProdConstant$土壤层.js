import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 第一层
     * desc: --
     * value: 0
     */
    第一层: 0,
    /**
     * label: 第二层
     * desc: --
     * value: 1
     */
    第二层: 1,
    /**
     * label: 第三层
     * desc: --
     * value: 2
     */
    第三层: 2,
    /**
     * label: 第四层
     * desc: --
     * value: 3
     */
    第四层: 3,
    /**
     * label: 第五层
     * desc: --
     * value: 4
     */
    第五层: 4,
    /**
     * label: 第六层
     * desc: --
     * value: 5
     */
    第六层: 5,
    /**
     * label: 第七层
     * desc: --
     * value: 6
     */
    第七层: 6,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const NetMonitorService = {
    /**
     * 悬浮窗设备列表
     * @param {*} areaId 区域id ,为空表示所有
     * @returns Promise 
     */
    'netMonitorList': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'fbe3f123538e092da57cc0bce2091ba2235cfae3', areaId)
            : execute(concreteModuleName, `/NetMonitorService/netMonitorList`, 'json', 'POST', { areaId });
    }, 
    /**
     * 悬浮窗设备详情
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'netMonitorEqDetail': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c3263e6017c413e4785f828a02792bcf0ff74f05', equipmentId)
            : execute(concreteModuleName, `/NetMonitorService/netMonitorEqDetail`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取地图上设备点信息
     * @param {*} areaId 区域id ,为空表示所有
     * @returns Promise 
     */
    'findEqInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8be78365d813dee5495ef7e601b356f0bba5fa03', areaId)
            : execute(concreteModuleName, `/NetMonitorService/findEqInfo`, 'json', 'POST', { areaId });
    }
}

export default NetMonitorService

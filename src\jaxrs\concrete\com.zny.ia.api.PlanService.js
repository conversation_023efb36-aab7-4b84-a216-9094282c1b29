/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const PlanService = {
    /**
     * 新增生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'addPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9622795feb2a44e45a8e4d99324c2fc683e1db77', po)
            : execute(concreteModuleName, `/PlanService/addPlan`, 'json', 'POST', po);
    }, 
    /**
     * 生产计划详情-带完成区域
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'singlePlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '11038180eb60771258d0de45317c0e7df43f1476', planId)
            : execute(concreteModuleName, `/PlanService/singlePlan`, 'json', 'POST', { planId });
    }, 
    /**
     * 生产计划详情
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'detailPlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e8d4d13a8c4b641e56818677f5c2fe8c4377b0d6', planId)
            : execute(concreteModuleName, `/PlanService/detailPlan`, 'json', 'POST', { planId });
    }, 
    /**
     * 生产计划列表
     * @param {*} po 
     * @returns Promise 
     */
    'listPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '04568d96f157dc93125460b64d9f22c635b682c0', po)
            : execute(concreteModuleName, `/PlanService/listPlan`, 'json', 'POST', po);
    }, 
    /**
     * 下载报表
     * @param {*} po 
     * @returns Promise 
     */
    'downloadPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4cf95dc5ffa993be5df7b5d6edf9170726df2915', po)
            : execute(concreteModuleName, `/PlanService/downloadPlan`, 'json', 'POST', po);
    }, 
    /**
     * 编辑生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'updatePlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '504377ff5b2276218dda2cadc23952c558a9ef4a', po)
            : execute(concreteModuleName, `/PlanService/plan`, 'json', 'PUT', po);
    }, 
    /**
     * 本周相关计划
     * @param {*} po 
     * @returns Promise 
     */
    'correlationPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5d1b8085b9168d01cfde200250c119cca2e82c46', po)
            : execute(concreteModuleName, `/PlanService/correlationPlan`, 'json', 'POST', po);
    }, 
    /**
     * 完成生产计划 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'finishPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'eb3879f72fa8597b6cd18e4c7670391d383c119e', po)
            : execute(concreteModuleName, `/PlanService/finishPlan`, 'json', 'POST', po);
    }, 
    /**
     * 删除生产计划 pc app 共用
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'deletePlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b9ee444d272cbf60c734d75768f79fe95350441f', planId)
            : execute(concreteModuleName, `/PlanService/plan`, 'json', 'DELETE', { planId });
    }
}

export default PlanService

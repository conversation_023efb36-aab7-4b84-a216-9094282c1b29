<template>
  <!-- 新增区域弹窗 -->
  <el-dialog
    class="addAreaDialog"
    :visible.sync="dialogVisible"
    width="1512px"
    center
    :modal-append-to-body="false"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div slot="title"></div>
    <div class="addAreaDialog_content">
      <!-- 左侧地图区域 -->
      <div class="addAreaDialog_map">
        <div class="map_controls">
          <div class="map_selectButton" @click="toggleAreaSelection">
            {{ isSelectingArea ? '停止选择' : '选择区域' }}
          </div>
          <div class="map_clearButton" @click="clearPlot" v-if="currentPolygon">
            清除地块
          </div>
        </div>
        <zny-map
          ref="addAreaMap"
          style="width: 100%; height: 100%"
          :map-options="addMapOptions"
          :max-zoom="20"
          :roadnet="true"
          @click="handleMapClick">
        </zny-map>
      </div>
      <!-- 右侧表单区域 -->
      <div class="addAreaDialog_form">
        <div class="form_header">
          <div class="form_title">添加</div>
          <div class="close" @click="closeDialog('addRuleForm')">
            <img src="../../../../assets/image/managementSystem/close.png" alt="">
          </div>
        </div>
        <el-form :model="addRuleForm" :rules="addRules" ref="addRuleForm" label-width="100px" label-position="top">
          <el-form-item label="区域名称" prop="areaName">
            <el-input v-model="addRuleForm.areaName" clearable class="systemFormStyle" placeholder="请输入区域名称"></el-input>
          </el-form-item>
          <!-- <el-form-item label="当前作物" prop="crop">
            <el-input v-model="addRuleForm.crop" clearable class="systemFormStyle" placeholder="请输入作物种类"></el-input>
          </el-form-item> -->
          <el-form-item label="区域面积">
            <el-input v-model="addRuleForm.areaSize" class="systemFormStyle" disabled>
              <span slot="suffix">亩</span>
            </el-input>
          </el-form-item>
          <el-form-item>
            <div class="equipment_label_container">
              <span class="equipment_label">包含设备</span>
              <div class="equipment_addButton" @click="showEquipmentSelector" v-if="!showSelector">
                <img src="../../../../assets/image/managementSystem/add-icon.png" alt="">
              </div>
            </div>
            <!-- 设备选择器 - 独立于滚动容器 -->
            <div class="equipment_selector" v-if="showSelector">
              <el-select
                v-model="addRuleForm.equipment"
                placeholder="请选择设备"
                class="systemFormStyle"
                @change="handleEquipmentSelect">
                <el-option
                  v-for="item in availableEquipmentList"
                  :key="item.equipmentId"
                  :label="item.equipmentName"
                  :value="item.equipmentId">
                </el-option>
              </el-select>
              <!-- 减号按钮 - 用于取消选择 -->
              <img @click="cancelEquipmentSelector" src="../../../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="equipment_cancelBtn">
            </div>
            <!-- 已选设备列表 - 带滚动条 -->
            <div class="equipment_list" v-if="addRuleForm.equipmentId.length > 0">
              <div class="equipment_item" v-for="(item,index) in addRuleForm.equipmentId" :key="index">
                <el-input v-model="addRuleForm.equipmentId[index].equipmentName" class="systemFormStyle" disabled></el-input>
                <img @click="removeEquipment(index)" src="../../../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="equipment_removeBtn">
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="btnBox">
          <div class="btnItem systemResetButtonStyle2">
            <el-button @click="cancelDialog('addRuleForm')">取消</el-button>
          </div>
          <div class="btnItem systemSearchButtonStyle2">
            <el-button @click="submitDialog('addRuleForm')">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import BaseManagementService from '../../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.BaseManagementService.js';
import CommonService from '../../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';

export default {
  name: 'AddAreaDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addRuleForm: {
        areaName: "",
        // crop: "", 
        areaSize: "0.00",
        equipmentId: [],
        equipment: "",
        equipmentList: [],
      },
      addMapOptions: {
        zoom: 15,
        center: [117.12665, 36.6584],
        amapTileUrl: localStorage.getItem('amapTileUrl'),
      },
      isSelectingArea: false,
      showSelector: false,
      currentPolygon: null,
      // 存储多边形的地理信息
      polygonCenter: null, // 中心点坐标 [经度, 纬度]
      polygonBorder: '', // 边界字符串
      addRules: {
        areaName: [
          { required: true, message: '请输入区域名称', trigger: 'blur' },
        ],
        // crop: [
        //   { required: true, message: '请选择作物种类', trigger: 'change' },
        // ],
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    availableEquipmentList() {
      if (!this.addRuleForm.equipmentList) return [];
      const selectedIds = this.addRuleForm.equipmentId.map(item => item.equipmentId);
      return this.addRuleForm.equipmentList.filter(item => !selectedIds.includes(item.equipmentId));
    }
  },
  methods: {
    // 打开新增区域弹窗
    openDialog() {
      this.dialogVisible = true;
      this.resetForm();
      this.getAddListEquipment();
    },
    // 重置表单
    resetForm() {
      this.addRuleForm.areaName = "";
      // this.addRuleForm.crop = "";
      this.addRuleForm.areaSize = "0.00";
      this.addRuleForm.equipmentId = [];
      this.addRuleForm.equipment = "";
      this.isSelectingArea = false;
      this.showSelector = false;
      this.currentPolygon = null;
      // 重置地理信息
      this.polygonCenter = null;
      this.polygonBorder = '';
    },
    // 获取新增区域所需的设备列表
    getAddListEquipment() {
      CommonService.listEquipment()
        .then(res => {
          this.addRuleForm.equipmentList = res;
        })
        .catch(error => {
          console.error('获取设备列表失败:', error);
          this.$message({
            message: '获取设备列表失败，请重试',
            type: 'error'
          });
        });
    },
    // 显示设备选择器
    showEquipmentSelector() {
      this.showSelector = true;
    },
    // 取消设备选择器
    cancelEquipmentSelector() {
      this.showSelector = false;
      this.addRuleForm.equipment = ''; // 清空选择值
    },
    // 处理设备选择
    handleEquipmentSelect() {
      if (this.addRuleForm.equipment) {
        this.addNumIncrease();
        // 选择完设备后隐藏选择器，显示加号
        this.showSelector = false;
      }
    },
    // 新增区域设备添加
    addNumIncrease() {
      if (this.addRuleForm.equipment == "") {
        this.$message('请选择设备');
      } else {
        this.addRuleForm.equipmentList.forEach(e => {
          if (this.addRuleForm.equipment == e.equipmentId) {
            this.addRuleForm.equipmentId.push(e);
            this.addRuleForm.equipment = ''; // 清空选择值
          }
        })
      }
    },
    // 新增区域删除已选设备
    removeEquipment(index) {
      this.addRuleForm.equipmentId.splice(index, 1);
      // 删除设备后不自动显示选择器，需要点击加号才显示
    },
    // 切换区域选择状态
    toggleAreaSelection() {
      if (this.isSelectingArea) {
        this.stopAreaSelection();
      } else {
        this.startAreaSelection();
      }
    },
    // 开始区域选择
    startAreaSelection() {
      if (this.currentPolygon) {
        this.clearPlot();
      }
      this.isSelectingArea = true;
      this.$message({
        message: '请在地图上绘制多边形选择区域',
        type: 'info'
      });
      this.drawPolygon();
    },
    // 停止区域选择
    stopAreaSelection() {
      this.isSelectingArea = false;
    },
    // 清除地块
    clearPlot() {
      if (this.currentPolygon) {
        this.currentPolygon.hideAreaLabel();
        this.$refs.addAreaMap.removePolygon(this.currentPolygon);
        this.currentPolygon = null;
        this.addRuleForm.areaSize = "0.00";
        // 清除地理信息
        this.polygonCenter = null;
        this.polygonBorder = '';
        this.$message({
          message: '已清除地块',
          type: 'info'
        });
      }
    },
    // 清除所有地图数据
    clearAllMapData() {
      if (this.currentPolygon) {
        this.currentPolygon.hideAreaLabel();
        this.$refs.addAreaMap.removePolygon(this.currentPolygon);
        this.currentPolygon = null;
      }
      this.addRuleForm.areaSize = "0.00";
      this.isSelectingArea = false;
      // 清除地理信息
      this.polygonCenter = null;
      this.polygonBorder = '';
    },
    // 绘制多边形
    drawPolygon() {
      if (!this.$refs.addAreaMap) return;

      this.$refs.addAreaMap
        .drawPolygon({
          border: {
            color: "#0093BC",
            weight: 2,
            opacity: 1
          },
          fill: {
            color: "#0093BC",
            opacity: 0.2
          },
          zIndex: 101,
        })
        .then((polygon) => {
          this.currentPolygon = polygon;

          polygon.showAreaLabel({
            formatter: (area) => {
              return ((area / 2000) * 3).toFixed(1) + " 亩";
            }
          });

          const area = ((polygon.getArea() / 2000) * 3).toFixed(2);
          this.addRuleForm.areaSize = area;

          // 获取多边形的路径坐标
          let polygonPath = null;
          let centerPoint = null;
          let borderString = '';

          if (polygon.getPath) {
            polygonPath = polygon.getPath();
            console.log('获取到的路径数据:', polygonPath);

            // 从路径数据中提取边界坐标
            if (polygonPath && polygonPath.border && polygonPath.border.length > 0) {
              const coordinates = polygonPath.border;

              // 计算中心点
              centerPoint = this.calculatePolygonCenter(coordinates);

              // 转换为边界字符串格式 (纬度,经度;纬度,经度...)
              borderString = this.coordinatesToBorderString(coordinates);

              // 存储到 data 中
              this.polygonCenter = centerPoint;
              this.polygonBorder = borderString;

              console.log('中心点坐标:', centerPoint);
              console.log('边界字符串:', borderString);
            }
          }

          this.isSelectingArea = false;
          this.$message({
            message: `区域选择完成，面积：${area}亩`,
            type: 'success'
          });
        })
        .catch(() => {
          this.isSelectingArea = false;
        });
    },
    // 处理地图点击事件
    handleMapClick(e) {
      return;
    },
    // 计算多边形中心点
    calculatePolygonCenter(points) {
      if (!points || points.length === 0) return [117.12665, 36.6584];

      //取多边形所有点的平均值作为中心点
      let sumX = 0, sumY = 0;
      points.forEach(point => {
        sumX += point[0];
        sumY += point[1];
      });

      return [sumX / points.length, sumY / points.length];
    },
    // 将坐标数组转换为边界字符串格式
    coordinatesToBorderString(coordinates) {
      if (!coordinates || coordinates.length === 0) return '';

      return coordinates.map(coord => `${coord[1]},${coord[0]}`).join(';');
    },
    // 新增区域弹窗关闭
    closeDialog(formName) {
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.clearAllMapData();
      this.dialogVisible = false;
    },
    // 新增区域弹窗取消
    cancelDialog(formName) {
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.clearAllMapData();
      this.dialogVisible = false;
    },
    // 新增区域弹窗确定
    submitDialog(formName) {
      const that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          //验证

          // 验证区域面积
          const areaSize = parseFloat(that.addRuleForm.areaSize);
          if (!areaSize || areaSize <= 0) {
            that.$message({
              message: '请先在地图上绘制区域',
              type: 'warning'
            });
            return;
          }

          //验证设备列表
          if (!that.addRuleForm.equipmentId || that.addRuleForm.equipmentId.length === 0) {
            that.$message({
              message: '请至少选择一个设备',
              type: 'warning'
            });
            return;
          }

          //验证区域边界
          if (!that.polygonBorder || !that.polygonCenter) {
            that.$message({
              message: '请先在地图上绘制区域',
              type: 'warning'
            });
            return;
          }

          let equipmentIds = [];
          that.addRuleForm.equipmentId.forEach(e => {
            equipmentIds.push(e.equipmentId);
          });

          let po = {
            areaName: that.addRuleForm.areaName,
            area: areaSize,
            equipmentIds: equipmentIds,
            longitude: that.polygonCenter[0].toString(), // 经度
            latitude: that.polygonCenter[1].toString(),  // 纬度
            border: that.polygonBorder // 边界字符串
          };

          console.log('提交的完整参数:', po);

          BaseManagementService.saveAreaEquipment(po)
            .then(() => {
              that.$message({
                message: '新增区域成功',
                type: 'success'
              });
              that.$emit('add-success');
              that.$refs[formName].resetFields();
              that.clearAllMapData();
              that.dialogVisible = false;
            })
            .catch((error) => {
              console.error('新增区域失败:', error);
              that.$message({
                message: '新增区域失败，请重试',
                type: 'error'
              });
            });
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .el-input__prefix, ::v-deep .el-input__suffix {
  color: #000;
  font-size: 16px;
}
</style>

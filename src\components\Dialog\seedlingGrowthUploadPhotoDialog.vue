<template>
  <div class="seedlingGrowthUploadPhotoDialog dialogStyle">
    <el-dialog
      :title="title"
      width="627px"
      center
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="cancel()">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="addFrom">
        <el-form ref="form" :rules="rules" :model="ruleForm" label-width="120px">
          <el-form-item prop="area" label="请选择种植区域：" class="form_select formStyle">
            <el-select v-model="ruleForm.area" popper-class="selectStyle_list" placeholder="请选择区域">
              <el-option
                      v-for="item in areaData"
                      :key="item.areaId"
                      :label="item.areaName"
                      :value="item.areaId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="photoList" label="图片信息：" class="formStyle">
            <el-upload
                    :class="{hide:hideUpload}"
                    class="uploadCard128"
                    action=""
                    list-type="picture-card"
                    :file-list="fileList"
                    :limit="3"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <img src="../../assets/image/centralControlPlatform/upload-add.png" alt="">
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="submit_button searchButtonStyle" @click="cancel()">
          <el-button>取消</el-button>
        </div>
        <div class="submit_button submitButtonStyle" @click="submit('form')">
          <el-button>提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import CropCaseService from "../../jaxrs/concrete/com.zny.ia.api.CropCaseService";
  import CommonService from "../../jaxrs/concrete/com.zny.ia.api.CommonService";
export default {
  name:'seedlingGrowthUploadPhotoDialog',
  data(){
    return{
      title:"上传图片",
      dialogVisible:false,
      areaData:[],
      ruleForm:{
        area:'',
        imagesPath:[],
        litPicUrl:"",
        photoList:[],
      },
      fileList:[],
      hideUpload:false,
      rules:{
        area:[
          { required: true, message: '请选择区域', trigger: 'change' },
        ],
        photoList:[
          { required: true, message: '请上传图片', trigger: 'blur' },
        ],
      },
    }
  },
  mounted(){
    this.listen('seedlingGrowthUploadPhotoDialogOpen', () => {
      this.dialogVisible=true
      this.getAreaData()
    })
  },
  methods:{
    //获取区域信息
    getAreaData(){
      CommonService.allAreaListInformation()
              .then(res=>{
                this.areaData=res
              })
    },
    //处理上传的文件
    updatePicProperties(fileList) {
      this.ruleForm.imagesPath = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content}
      });
      this.ruleForm.litPicUrl = fileList.filter(e => !e._type).map(e => e.url).join(',')
    },
    // 移除文件
    handleRemove(files,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 3
      this.updatePicProperties(fileList)
    },
    // 上传文件个数限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传 3 张图片`);
    },
    handleChange(file,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 3
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicProperties(fileList)
      })
    },
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        if (file) {
          reader.readAsDataURL(file);
        } else {

        }
        reader.onload = function() {
          imgResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(imgResult);
        };
      });
    },
    //提交
    submit(formName){
      var that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          var params={
            areaId:this.ruleForm.area,
            pictures:that.ruleForm.imagesPath,
          }
          CropCaseService.cropCasePicImport(params)
                  .then(()=>{
                    // console.log(res)
                    that.$message({
                      message:'提交成功！',
                      type:'success'
                    })
                    //上传成功
                    this.zEmit('seedlingGrowthUploadSuccess')
                    that.cancel()
                  })
        }
      })
    },
    //取消
    cancel(){
      var that=this
      that.dialogVisible=false
      Object.assign(that.$data,that.$options.data())
    }
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/seedlingGrowthUploadPhotoDialog.less';
</style>
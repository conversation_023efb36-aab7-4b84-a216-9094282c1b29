<template>
  <div class="demonstrationMapAssembly">
    <!-- <div class="toggleButton">
      <img v-if="map=='twoDimensionalMap'" @click="map='satelliteMap'" src="../assets/image/monitoringCenter/btn.png" alt="">
      <img v-if="map=='satelliteMap'" @click="map='twoDimensionalMap'" src="../assets/image/monitoringCenter/btn1.png" alt="">
    </div> -->
    <!-- 二维地图 -->
    <!-- <div class="twoDimensionalMap" v-if="map=='twoDimensionalMap'">
      
    </div> -->
    <!-- 卫星地图 -->
    <div class="satelliteMap" v-if="map=='satelliteMap'">
      <zny-map
        ref="map"
        style="width: 100%; height: 100%"
        :map-options="amapOptions"
        :max-zoom="20"
        :roadnet="true"
      ></zny-map>
    </div>
  </div>
</template>
<script>
import {mapHandle} from '../js/mapHandle.js';
import HomePageService from '../jaxrs/concrete/com.zny.ia.api.HomePageService.js';
import { getAmapTileUrl } from '../utils/mapTileUtils.js';
export default {
  data(){
    return{
      map:"satelliteMap",
      areaCropList:[],
      amapOptions: {
        zoom:15,
        center:[117.12665,36.6584],
        amapTileUrl: localStorage.getItem('amapTileUrl'),
      },
    }
  },
  mounted(){
    this.findAreaCrop()
  },
  methods:{
    findAreaCrop(){
      HomePageService.findAreaCrop()
      .then(res=>{
        this.areaCropList=res
        let map = this.$refs["map"];
        this.areaCropList.forEach(block=>{
          // mapHandle.addMarker(map,block,{
          //   bgImg: require("@/assets/image/monitoringCenter/bg.png"),
          //   showText: false,
          //   onClick: (e) => {
          //     console.log(e);
          //   },
          // })
          mapHandle.addPlatPolygon(map,block,{
            keyOfGeo: "geometry",
            color: "#FF3D16",
            opacity: 0.5,
            selectColor: "#FF3D16",
            selectOpacity: 1,
            onSelected: (e) => {
              console.log(e);
            },
            onUnselected:(e)=>{
              console.log(e);
            }
          })
        })
        map.setFitView()
      })
    },
  },
}
</script>
<style lang="less">
@import '../assets/css/demonstrationMap.less';
</style>
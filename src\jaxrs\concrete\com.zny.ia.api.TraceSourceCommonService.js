/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const TraceSourceCommonService = {
    /**
     * 溯源建档
     * @param {*} traceSourceInformationPo 溯源建档信息
     * @returns Promise 
     */
    'traceSourceBuild': function (traceSourceInformationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '47c14fc739e9b3f080a26956919e471a72a02e58', traceSourceInformationPo)
            : execute(concreteModuleName, `/TraceSourceCommonService/traceSourceBuild`, 'json', 'POST', traceSourceInformationPo);
    }, 
    /**
     * 根据溯源Id删除溯源档案
     * @param {*} id 溯源Id
     * @returns Promise 
     */
    'removeTraceSourceById': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8d77dcdc0d731fcc976efac8832fc4f64df7e650', id)
            : execute(concreteModuleName, `/TraceSourceCommonService/removeTraceSourceById`, 'json', 'POST', { id });
    }, 
    /**
     * 溯源档案馆列表
     * @param {*} traceSourceRecordPo 查询条件
     * @returns Promise 
     */
    'listTraceSourceRecord': function (traceSourceRecordPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '84c4fb00c831465617589e2c2059aa5f8f9c6ad5', traceSourceRecordPo)
            : execute(concreteModuleName, `/TraceSourceCommonService/listTraceSourceRecord`, 'json', 'POST', traceSourceRecordPo);
    }, 
    /**
     * 溯源详情
     * @param {*} id 溯源码
     * @returns Promise 
     */
    'traceSourceDetail': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7975ecd3542535b14ff7ed2c2c19b77f87049002', id)
            : execute(concreteModuleName, `/TraceSourceCommonService/${id}/traceSourceDetail`, 'json', 'GET');
    }, 
    /**
     * 修改溯源
     * @param {*} changeTraceSourceInformationPo 修改溯源信息
     * @returns Promise 
     */
    'changeTraceSourceInformation': function (changeTraceSourceInformationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7bfdd5bcd5e59c68c063c881867b4bac8eafa99a', changeTraceSourceInformationPo)
            : execute(concreteModuleName, `/TraceSourceCommonService/changeTraceSourceInformation`, 'json', 'POST', changeTraceSourceInformationPo);
    }
}

export default TraceSourceCommonService

<template>
    <!-- 卫星地图 -->
    <div class="AMapContainer">
        <zny-map
            ref="aMap"
            style="width: 100%; height: 100%"
            :map-options="amapOptions"
            :max-zoom="20"
            :roadnet="true"
        ></zny-map>
        <!-- 区域详情弹窗 -->
        <!-- <div style="display: none">
            <div class="areaDetail_infoWindow infoWindow" ref="areaDetailIW" v-if="areaDetail">
                <div class="iw_detailsList">
                    <div class="iw_detailsItem">区域：{{ areaDetail.areaName }}</div>
                    <div class="iw_detailsItem">作物：{{ areaDetail.cropId | cropFormat }}</div>
                    <div class="iw_detailsItem">种植批次：{{ areaDetail.batch }}</div>
                    <div class="iw_detailsItem">区域面积：{{ areaDetail.acreage }}亩</div>
                    <div class="iw_detailsItem">设备：{{ areaDetail.equipmentInfo }}</div>
                    <div class="iw_detailsItem">
                        生产计划：
                        <div class="planHomeVoList">
                            <div v-for="(item, index) in areaDetail.planHomeVoList" :key="index" class="planHomeItem">
                                <div>
                                    {{ item.planTypeName | productionPlanTypeFormat }}
                                </div>
                                <div>{{ item.startTime }} - {{ item.endTime }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>{{areaDetail}}</div>
                <div class="top_left">
                    <img src="../assets/image/monitoringCenter/top_left.png" alt="" />
                </div>
                <div class="top_right">
                    <img src="../assets/image/monitoringCenter/top_right.png" alt="" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div> -->
        <!-- 气象设备弹窗 -->
        <!-- <div style="display: none">
            <div class="equipment_infoWindow infoWindow" ref="equipmentIW" v-if="equipmentDetail">
                <div class="iw_title">
                    {{ equipmentDetail.equipmentName }}
                </div>
                <div class="iw_con">
                    <div class="iw_detailsItem">设备ID：{{ equipmentDetail.equipmentId }}</div>
                    <div class="iw_detailsItem">
                        状态：
                        <span v-if="equipmentDetail.onlineType == 1" style="color:#00FFAE">在线</span>
                        <span v-if="equipmentDetail.onlineType == 2" style="color:#999999">离线</span>
                        <span v-if="equipmentDetail.onlineType == 3" style="color:#EE0101">报警</span>
                    </div>
                    <div class="iw_detailsItem">
                    维护：{{equipmentDetail.telephone}}
                </div>
                <div class="iw_detailsItem">
                    厂商：{{equipmentDetail.manufacturer}}
                </div>
                    <div class="iw_detailsItem">位置：{{ equipmentDetail.position }}</div>
                    <div class="iw_detailsItem">说明：{{ equipmentDetail.explain }}</div>
                    <div class="iw_detailsItem">({{ equipmentDetail.longitude }},{{ equipmentDetail.latitude }})</div>
                </div>
                <div class="top_left">
                    <img src="../assets/image/monitoringCenter/top_left.png" alt="" />
                </div>
                <div class="top_right">
                    <img src="../assets/image/monitoringCenter/top_right.png" alt="" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div> -->
    </div>
</template>

<script>
import { mapHandle } from '../js/mapHandle.js'
import HomePageService from '../jaxrs/concrete/com.zny.ia.api.HomePageService.js'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService.js'
export default {
    props: {
        // 设备在线:1:在线,2:离线,3：报警
        type: {
            //0->监控中心,1->气象,2->土壤,3->灌排,4->虫情,5->水质,6->苗情,7->植保
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            amapOptions: {
                zoom: 15,
                center: [117.12665, 36.6584],
                amapTileUrl: 'https://tiles.zhongnongyun.cn/gcj02&x=[x]&y=[y]&z=[z]',
            },
            aMap: null,
            areaDetail: {}, //区域详情(弹窗)
            equipmentList: [], //设备列表(气象,土壤,虫情,苗情,灌排,水质)
            equipmentDetail: {}, //设备详情(弹窗)
        }
    },
    mounted() {
        this.aMap = this.$refs['aMap']
        let scaleValue = localStorage.getItem('scaleValue')
        if (scaleValue) this.aMap.descale(scaleValue)
        this.listen('scaleChangeWatch', val => {
            this.aMap.descale(val.scale)
        })
        this.findAreaCrop()
        if (this.type != 0 && this.type != 7) {
            this.getListEquipment()
        }
    },
    methods: {
        findAreaCrop() {
            HomePageService.findAreaCrop().then(res => {
                this.areaCropList = res
                let map = this.$refs['aMap']
                this.areaCropList.forEach(block => {
                    let arr = block.border.split(';')
                    let ll = []
                    arr.forEach(e => {
                        let lat, lng
                        lat = parseFloat(e.split(',')[0])
                        lng = parseFloat(e.split(',')[1])
                        ll.push([lng, lat])
                    })
                    let polygonPath = [{ border: ll }]
                    block.polygonPath = polygonPath
                    // 显示区域边框
                    mapHandle.addPlatPolygon(map, block, {
                        keyOfGeo: 'geometry',
                        opacity: 0.5,
                        selectOpacity: 1,
                        onClick: e => {
                            switch (this.type) {
                                case 0:
                                    break
                                case 1:
                                    // 气象环境检测系统弹窗
                                    this.zEmit('meteorologyEMDialogOpen', block)
                                    break
                                case 2:
                                    // 土壤检测系统弹窗
                                    this.zEmit('soilEMDialogOpen',block)
                                    break
                                case 3:
                                    // 灌排系统弹窗
                                    this.zEmit('irrigationDrainageDialogOpen', block.areaId)
                                    break
                                case 4:
                                    // 虫情系统弹窗
                                    this.zEmit('InsectSituationEMDialogOpen', block)
                                    break
                                case 5:
                                    // 水质系统弹窗
                                    this.zEmit('waterMonitorEMDialogOpen', block.areaId)
                                    break
                                case 6:
                                    // 苗情系统弹窗
                                    this.zEmit('seedlingGrowthMonitorDialogOpen', block.areaId)
                                    break
                                case 7:
                                    // 植保系统弹窗
                                    this.zEmit('AreaDetailsDialogOpen', block.areaId)
                                    break
                            }
                        },
                    })
                    // 监控中心显示区域Marker
                    // if (this.type == 0 || this.type == 7) {
                    //     this.showMarker(block)
                    // }
                })
                map.center(this.areaCropList[0].polygonPath[0].border[0])
                map.zoom(18)
                // map.setFitView()
            })
        },
        getListEquipment() {
            // 获取设备列表
            CommonService.listEquipment(this.type).then(res => {
                this.equipmentList = res
                this.equipmentList.forEach(e => {
                    this.showMarker(e)
                })
            })
        },
        // 在地图放marker
        showMarker(block) {
            let showImg, position, bgImg
            switch (this.type) {
                case 0:
                case 7:
                    position = block.polygonPath[0].border[0]
                    showImg = true
                    bgImg = require('@/assets/image/monitoringCenter/bg.png')
                    break
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    position = [block.longitude, block.latitude]
                    showImg = false
                    var onlineType = 0
                    if (block.online) {
                        if (block.alarmStatus) {
                            onlineType = 3
                        } else {
                            onlineType = 1
                        }
                    } else {
                        onlineType = 2
                    }
                    block.onlineType = onlineType
                    bgImg = require('@/assets/image/icon/' + this.type + '_' + onlineType + '.png')
                    break
            }
            let map = this.$refs['aMap']
            mapHandle.addMarker(map, block, {
                bgImg: bgImg,
                showImg: showImg,
                position: position,
                onClick: e => {
                    console.log(e)
                    switch (this.type) {
                        case 0:
                        case 7:
                            this.getAreaDetail(e.marker.extData.areaId).then(() => {
                                let iw1 = e.marker.popup({
                                    content: this.$refs['areaDetailIW'],
                                    offset: [0, -120],
                                    autoClose: false,
                                })
                                this.$refs['areaDetailIW'].onclick = () => {
                                    iw1.close()
                                }
                                this.$refs['areaDetailIW'].onmousewheel = e => {
                                    e.stopPropagation()
                                }
                            })
                            break
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                            this.equipmentDetail = e.marker.extData
                            var iw2 = e.marker.popup({
                                content: this.$refs['equipmentIW'],
                                offset: [0, -70],
                                autoClose: false,
                            })
                            this.$refs['equipmentIW'].onclick = () => {
                                iw2.close()
                            }
                            this.$refs['equipmentIW'].onmousewheel = e => {
                                e.stopPropagation()
                            }
                            break
                    }
                },
            })
        },
        // 获取区域详情
        getAreaDetail(areaId) {
            return HomePageService.areaDetail(areaId).then(res => {
                this.areaDetail = res
                return
            })
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../assets/css/znyAMap.less';
</style>

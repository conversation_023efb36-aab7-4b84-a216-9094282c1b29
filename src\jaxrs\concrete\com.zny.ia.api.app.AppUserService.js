/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppUserService = {
    /**
     * 修改用户头像
     * @param {*} avatar 用户头像
     * @returns Promise 
     */
    'userAvatarUpdate': function (avatar) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c601e3163172594cf13d2d0c39db5aada9d9f473', avatar)
            : execute(concreteModuleName, `/App/UserService/userAvatarUpdate`, 'json', 'POST', avatar);
    }, 
    /**
     * 修改用户昵称
     * @param {*} nickName 用户昵称
     * @returns Promise 
     */
    'userNickNameUpdate': function (nickName) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2c5587a604944907f43bd9a5c5b411c580a56dba', nickName)
            : execute(concreteModuleName, `/App/UserService/userNickNameUpdate`, 'json', 'POST', { nickName });
    }, 
    /**
     * 查看管理区域
     * @returns Promise 
     */
    'userAreaSelect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ec5b9c5b4f7c206a48fb2ea6c1c9c234c64a22b7')
            : execute(concreteModuleName, `/App/UserService/userAreaSelect`, 'json', 'GET');
    }, 
    /**
     * 修改手机号
     * @param {*} telephone 手机号
     * @returns Promise 
     */
    'userTelephoneUpdate': function (telephone) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '64468fdb1a2a98855fbba8d18b0a290f03adb662', telephone)
            : execute(concreteModuleName, `/App/UserService/userTelephoneUpdate`, 'json', 'POST', { telephone });
    }, 
    /**
     * 查看用户信息
     * @returns Promise 
     */
    'userDataSelect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'bae228816b8849553b27758fa06754742f3b07d5')
            : execute(concreteModuleName, `/App/UserService/userDataSelect`, 'json', 'GET');
    }
}

export default AppUserService

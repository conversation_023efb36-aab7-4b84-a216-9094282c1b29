import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 大豆
     * desc: --
     * value: 0
     */
    大豆: 0,
    /**
     * label: 小麦
     * desc: --
     * value: 1
     */
    小麦: 1,
    /**
     * label: 玉米
     * desc: --
     * value: 2
     */
    玉米: 2,
    /**
     * label: 水稻
     * desc: --
     * value: 3
     */
    水稻: 3,
    /**
     * label: 樱桃番茄
     * desc: --
     * value: 4
     */
    樱桃番茄: 4,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
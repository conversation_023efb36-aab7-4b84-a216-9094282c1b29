/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const TraceSourceService = {
    /**
     * 优质溯源-灌溉数据
     * @returns Promise 
     */
    'traceSourceHighQualityIrrigationPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c704745ec2902afde108796390283451afff97ee')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityIrrigationPicList`, 'json', 'GET');
    }, 
    /**
     * 补充灌溉数据
     * @param {*} id 溯源Id
     * @param {*} irrigationList 灌溉数据
     * @returns Promise 
     */
    'supplementIrrigation': function (id, irrigationList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'bee41b607f27c36407e937d3da7ec3939e845b56', {id, irrigationList})
            : execute(concreteModuleName, `/TraceSourceService/supplementIrrigation`, 'json', 'POST', { id, irrigationList });
    }, 
    /**
     * 优质溯源-生长环境图片
     * @returns Promise 
     */
    'traceSourceHighQualityGrowingEnvironmentPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'fa2d7750e675bd808c67dc170e09dc2cbc12018e')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityGrowingEnvironmentPicList`, 'json', 'GET');
    }, 
    /**
     * 补充溯源档案生长照片
     * @param {*} id 溯源Id
     * @param {*} picUrl 生长环境照片信息
     * @returns Promise 
     */
    'supplementTraceSourceGrowthImages': function (id, picUrl) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '070885ac66f4913185593daa1760160a36ce714d', {id, picUrl})
            : execute(concreteModuleName, `/TraceSourceService/supplementTraceSourceGrowthImages`, 'json', 'POST', { id, picUrl });
    }, 
    /**
     * 优质溯源-宣传视频
     * @returns Promise 
     */
    'traceSourceHighQualityVideoPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4bc3be73d72fa39cb01789fef1b50e1de53605db')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityVideoPicList`, 'json', 'GET');
    }, 
    /**
     * 优质溯源-认证文件
     * @returns Promise 
     */
    'traceSourceHighQualityCertificationPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3810bc4088f0e6f283f91b270a9edd97bf3569b7')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityCertificationPicList`, 'json', 'GET');
    }, 
    /**
     * 补充视频
     * @param {*} id 溯源Id
     * @param {*} video 视频
     * @param {*} videoCover 视频封面
     * @returns Promise 
     */
    'supplementVideo': function (id, video, videoCover) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '17cd85ccb3f078c9d0d568f3043c9c44f848ba4e', {id, video, videoCover})
            : execute(concreteModuleName, `/TraceSourceService/supplementVideo`, 'json', 'POST', { id, video, videoCover });
    }, 
    /**
     * 补充溯源档案认证文件
     * @param {*} id 溯源Id
     * @param {*} traceSourceCertificationPo 认证文件
     * @returns Promise 
     */
    'supplementTraceSourceCertification': function (id, traceSourceCertificationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a32abb912453d90eac742ece75e09daff7b5e25c', {id, traceSourceCertificationPo})
            : execute(concreteModuleName, `/TraceSourceService/supplementTraceSourceCertification`, 'json', 'POST', { id, traceSourceCertificationPo });
    }, 
    /**
     * 补充水质报告
     * @param {*} id 溯源Id
     * @param {*} waterList 水质报告
     * @returns Promise 
     */
    'supplementWaterQuality': function (id, waterList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ac5576a2820652fc85135516344816647e8e7002', {id, waterList})
            : execute(concreteModuleName, `/TraceSourceService/supplementWaterQuality`, 'json', 'POST', { id, waterList });
    }, 
    /**
     * 优质溯源-水质环境
     * @returns Promise 
     */
    'traceSourceHighQualityWaterPicList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ff7274e5a1902e92e86cc86c4b2265aab4e5d241')
            : execute(concreteModuleName, `/TraceSourceService/traceSourceHighQualityWaterPicList`, 'json', 'GET');
    }
}

export default TraceSourceService

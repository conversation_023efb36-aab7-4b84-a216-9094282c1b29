.baseManagement{
  width: 100%;
  height: 96%;

  &_tabList{
    margin: 22px 32px;
    display: flex;
    align-items: center;
    .tabList_item{
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-right: 40px;
      cursor: pointer;
    }
    .active{
      font-size: 22px;
      font-weight: bold;
      color: #0093BC;
    }
  }
  &_dataList{
    margin: 32px 0 20px 32px;
    display: flex;
    align-items: center;
    .dataList_item{
      width: 210px;
      height: 92px;
      // line-height: 92px;
      background: #FFFFFF;
      box-shadow: 6px 6px 54px rgba(0,0,0,0.05);
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;
      &_text{
        margin-left: 16px;
        div:nth-child(1){
          font-size: 16px;
          font-weight: 400;
          color: #333333;
        }
        div:nth-child(2){
          font-size: 28px;
          font-weight: bold;
          color: #333333;
        }
      }
      &_img{
        vertical-align: middle;
        margin-right: 16px;
      }
    }
    .report_item{
      width: 210px;
      height: 92px;
      background: linear-gradient(133deg, #0DC4BD 0%, #0093BC 100%);
      box-shadow: 6px 6px 54px rgba(0,0,0,0.05);
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #FFFFFF;
      margin-right: 24px;
      cursor: pointer;
      div:nth-child(1){
        margin-left: 21px;
        img{
          vertical-align: middle;
        }
      }
      div:nth-child(2){
        margin-right: 36px;
      }
    }
  }
  &_con{
    width: 96%;
    height: 78%;
    background: #FFFFFF;
    border-radius: 8px;
    margin: 0 0 0 32px;
    border: 1px solid transparent;
    .handleBox,.handleBox1{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24px;

      // 左侧区域（下拉框和查询按钮）
      &_leftSection{
        display: flex;
        align-items: center;
        margin-left: 24px;
        .handleBox_item{
          height: 40px;
          margin-right: 16px;
          &:nth-child(1){
            width: 270px;
          }
          &:nth-child(2){
            width: 235px;
          }
          &:nth-child(3){
            width: 80px;
          }
        }
      }

      // 右侧按钮组（添加和下载）
      &_rightButtons{
        display: flex;
        align-items: center;
        margin-right: 24px;
        .handleBox_item{
          width: 80px;
          height: 40px;
          margin-right: 16px;
          &:last-child{
            margin-right: 0;
          }
        }
      }

      &_item{
        height: 40px;
      }
      &_text{
        margin: 18px 20px 0 23px;
      }
      &_btn{
        width: 80px;
        height: 40px;
      }
    }
    .handleBox1{
      margin-top: 0;
      .handleBox_btn{
        width: 80px;
        height: 40px;
      }
      .handleBox_leftSection .handleBox_item,
      .handleBox_rightButtons .handleBox_item{
        margin-top: 0;
      }
    }
    .tableBox1{
      height: 79%;
    }
    .tableBox2{
      height: 88%;
    }
    .tableBox{
      width: 97%;
      margin: auto;
      margin-top: 24px;
      .cell{
        .viewData{
          font-size: 15px;
          font-weight: 400;
          color: #007EFF;
          cursor: pointer;
          margin: 0 8px;
        }
      }
    }
    .pageBox{
      width: 97%;
      height: 5%;
      margin-top: 10px;
      text-align: center;
    }
  }
  .el-dialog{
    margin-top: 30vh !important;
    .el-dialog__body{
      .systemFormStyle{
        width: 400px;
        height: 48px;
      }
      .btnBox{
        display: flex;
        justify-content: center;
        .btnItem{
          width: 160px;
          height: 40px;
          margin: 0 15px;
        }
      }
    }
  }
  .tipsDialog{
    .el-dialog{
      margin-top: 35vh !important;
      .el-dialog__body{
        .tipsText{
          margin: 0 30px 40px 30px;
        }
      }
    }
  }
  .reportHistoryDialog{
    .el-dialog{
      height: 858px;
      margin-top: 5vh !important;
      .el-dialog__body{
        height: 763px;
        .tableBox{
          height: 100%;
        }
      }
    }
  }
  .editDialog{
    .el-dialog{
      height: 484px;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 389px;
        .formList{
          height: 340px;
          .el-form{
            height:340px;
            .formItem{
              display: flex;
              justify-content: space-between;
              .el-form-item:nth-child(2){
                margin-right: 38px;
              }
            }
            .formItem1{
              height: 245px;
              overflow: scroll;
              padding-top: 5px;
              
              .el-form-item{
                .el-form-item__content{
                  display: flex;
                  flex-wrap: wrap;
                  justify-content: flex-start;
                  position: relative;
                  .tips{
                    position: absolute;
                    top: -34px;
                    left: 61px;
                  }
                  .systemFormStyle{
                    width: 280px;
                    height: 48px;                    
                  }
                  .formItem1_con2{
                    position: relative;
                    img{
                      position: absolute;
                      top: 14px;
                      right: -31px;
                    }
                  }
                }
              }
              &_con{
                img{
                  margin:0 0 0 12px;
                  vertical-align: middle;
                }
              }
              .formItem1_con:nth-child(2n) {
                margin-left: 32px;
              }
              .formItem1_con:not(:first-child){
                margin-bottom: 16px;
              }
            }
            .el-input,.el-select{
              width: 280px;
              height: 48px;
            }
          }
        }
        .btnBox{
          margin-top: 15px;
        }
      }
    }
  }
  .reportDialog{
    .el-dialog{
      height: 450px !important;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 355px;
        .formList{
          height: 300px;
        }
      }
    }
  }

  // 新增区域弹窗样式
  .addAreaDialog{
    .el-dialog{
      width: 1512px !important;
      height: 872px !important;
      margin-top: 3vh !important;
      .el-dialog__header{
        display: none; // 隐藏默认标题栏
      }
      .el-dialog__body{
        height: 823px;
        padding: 24px 32px 25px 24px !important;
        .addAreaDialog_content{
          display: flex;
          width: 100%;
          height: 100%;

          // 左侧地图区域
          .addAreaDialog_map{
            width: 892px;
            height: 823px;
            position: relative;
            margin-right: 32px;

            .map_controls{
              position: absolute;
              top: 16px;
              left: 16px;
              z-index: 1000;
              display: flex;
              gap: 12px;
            }

            .map_selectButton, .map_clearButton{
              width: 120px;
              height: 32px;
              color: #FFFFFF;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              font-size: 14px;
              transition: all 0.3s ease;
            }

            .map_selectButton{
              background: rgba(0, 147, 188, 0.8);

              &:hover{
                background: rgba(0, 147, 188, 1);
              }

              // 选择状态下的样式
              &.selecting{
                background: rgba(255, 61, 22, 0.8);

                &:hover{
                  background: rgba(255, 61, 22, 1);
                }
              }
            }

            .map_clearButton{
              background: rgba(204, 204, 204, 0.8);

              &:hover{
                background: rgba(204, 204, 204, 1);
              }
            }
          }

          // 右侧表单区域
          .addAreaDialog_form{
            width: 532px;
            height: 100%;
            position: relative;

            .form_header{
              display: flex;
              justify-content: center;
              align-items: center;
              // margin-bottom: 24px;
              padding:25px 0 24px 0;

              .form_title{
                font-size: 18px;
                font-weight: bold;
                color: #333333;
              }

              .close{
                cursor: pointer;
                img{
                  width: 16px;
                  height: 16px;
                }
              }
            }

            .el-form{
              height: calc(100% - 120px);
              overflow-y: auto;
              .el-form-item{
                margin-bottom: 24px;

                .el-form-item__label{
                  font-size: 16px;
                  color: #333333;
                  line-height: 22px;
                  margin-bottom: 8px;
                }

                .systemFormStyle{
                  width: 100%;
                  height: 48px;
                }

                // 设备选择区域
                .equipment_label_container{
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;

                  .equipment_label{
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    margin-right: 12px;
                  }

                  .equipment_addButton{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    cursor: pointer;

                    img{
                      width: 16px;
                      height: 16px;
                    }
                  }
                }

                // 设备选择
                .equipment_selector{
                  display: flex;
                  align-items: center;
                  margin-bottom: 16px;

                  .systemFormStyle{
                    flex: 1;
                    margin-right: 12px;
                  }

                  .equipment_removeBtn{
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                  }
                }

                .equipment_list{
                  max-height: 300px;
                  overflow-y: auto;
                  padding-right: 8px;

                  
                  &::-webkit-scrollbar {
                    width: 6px;
                  }

                  &::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                  }

                  &::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;

                    &:hover {
                      background: #a8a8a8;
                    }
                  }

                  .equipment_item{
                    display: flex;
                    align-items: center;
                    margin-bottom: 16px;

                    .systemFormStyle{
                      flex: 1;
                      margin-right: 12px;
                    }

                    .equipment_removeBtn{
                      width: 16px;
                      height: 16px;
                      cursor: pointer;
                    }
                  }
                }
              }
            }

            .btnBox{
              position: absolute;
              bottom: 0px;
              left: 0;
              right: 0;
              display: flex;
              justify-content: center;

              .btnItem{
                width: 160px;
                height: 40px;
                margin: 0 15px;
              }
            }
          }
        }
      }
    }
  }
}
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WaterService = {
    /**
     * 汇报水质检测报告 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'addWaterReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9ec403d7302bc0b76f6c777fbdcc554f6b14bb40', po)
            : execute(concreteModuleName, `/WaterService/addWaterReport`, 'json', 'POST', po);
    }, 
    /**
     * 近期检测水质 pc app 共用
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findWaterInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b1a9fd9b9ae8ca6bb008d817c10afcdf04f54b81', areaId)
            : execute(concreteModuleName, `/WaterService/findWaterInfo`, 'json', 'POST', { areaId });
    }, 
    /**
     * 人工汇报文件（图片）查看
     * @param {*} reportId 水质报告id
     * @returns Promise 
     */
    'detailWaterPic': function (reportId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '653b1752a59fbd1a039cbf3ffec9c04baefe24fc', reportId)
            : execute(concreteModuleName, `/WaterService/detailWaterPic`, 'json', 'POST', { reportId });
    }, 
    /**
     * 近期水质检测时间计划(已检测的)
     * @returns Promise 
     */
    'listWaterSchedule': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b21d578a9d2af3aafc9dc2722d18af57148a19df')
            : execute(concreteModuleName, `/WaterService/listWaterSchedule`, 'json', 'GET');
    }, 
    /**
     * 报告详情 pc app 共用
     * @param {*} reportId 水质报告id
     * @returns Promise 
     */
    'findWaterDetailInfo': function (reportId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3f74c27b183758ec14d2aaeea5e8ae6ca99cb703', reportId)
            : execute(concreteModuleName, `/WaterService/findWaterDetailInfo`, 'json', 'POST', { reportId });
    }, 
    /**
     * 水质检测报告列表
     * @param {*} po 
     * @returns Promise 
     */
    'listWaterReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4dc6d56d9ea8fc5f2497a12d9d1728cbf917bdfe', po)
            : execute(concreteModuleName, `/WaterService/listWaterReport`, 'json', 'POST', po);
    }
}

export default WaterService

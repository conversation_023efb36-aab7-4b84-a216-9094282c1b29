import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 水田作物
     * desc: --
     * value: 0
     */
    水田作物: 0,
    /**
     * label: 旱地作物
     * desc: --
     * value: 1
     */
    旱地作物: 1,
    /**
     * label: 加工烹调及去皮蔬菜
     * desc: --
     * value: 2
     */
    加工烹调及去皮蔬菜: 2,
    /**
     * label: 生食类蔬菜和瓜类和草本水果
     * desc: --
     * value: 3
     */
    生食类蔬菜和瓜类和草本水果: 3,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
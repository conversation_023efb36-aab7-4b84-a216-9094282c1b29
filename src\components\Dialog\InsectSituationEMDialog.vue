<template>
    <div class="InsectSituationEMDialog dialogStyle">
        <el-dialog
            :title="title"
            width="74.6%"
            :visible.sync="dialogVisible"
            :show-close="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @open="handleOpen"
        >
            <div class="line"></div>
            <div class="close" @click="dialogVisible = false">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="time">
                数据采集时间：{{insectCollectTime || '暂无数据'}}
            </div>
            <div class="handleBox">
                <div class="handleBox_item handleBox_select formStyle">
                    <el-select
                        v-model="equipmentId"
                        clearable
                        @change="equipmentChange"
                        placeholder="请选择设备"
                        popper-class="selectStyle_list"
                    >
                        <el-option
                            v-for="item in equipmentIdData"
                            :key="item.equipmentId"
                            :label="item.equipmentName"
                            :value="item.equipmentId"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item searchButtonStyle2" v-if="areaIsJudge">
                    <el-button @click="InsectSituationUploadPicturesDialogOpen">上传图片</el-button>
                </div>
                <div class="handleBox_item searchButtonStyle">
                    <el-button @click="InsectSituationOfHistoryDialogOpen">历史虫情记录</el-button>
                </div>
            </div>
            <div class="chartList">
                <div class="chartList_left">
                    <div class="chartList_item chartList_item1">
                        <div class="item_img" v-if="showData">
                            <img :src="caseRecently.picUrl" alt="" />
                        </div>
                        <div v-else class="item_img item_noImg">
                            <div>
                                <img src="../../assets/image/centralControlPlatform/noImg.png" alt="">
                            </div>
                            <div>暂无获取到照片</div>

                        </div>
                        <div class="item_textList">
                            <div class="textList_item1 ">最近监控虫情图片</div>
                            <div class="textList_item2 ">{{ caseRecently.createTime }}</div>
                            <div class="textList_item2 " v-if="showData">
                                监测数量
                                <span>{{ caseRecently.count }}</span>
                                只
                            </div>
                            <div class="textList_item2 " v-if="showData">
                                监测种类
                                <span>{{ caseRecently.kind }}</span>
                                种
                            </div>
                            <div style="height: 100px;overflow: scroll;">
                                <div class="textList_item2 " v-for="(item, index) in wormList" :key="index">
                                    {{ item.kind | kindFormat }}
                                    <span>{{ item.count }}</span>
                                    只/亩
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="chartList_item chartList_item2 tableStyle">
                        <div class="chartList_item_title">虫情变化趋势</div>
                        <div class="chartList_item_chart">
                            <znyEchart :chartData="WormCaseVariationLineOption" :chartType="'line'"></znyEchart>
                        </div>
                    </div>
                </div>
                <div class="chartList_right">
                    <div class="chartList_item">
                        <div class="chartList_item_title">虫情监测总量图表</div>
                        <div class="chartList_item_select formStyle">
                            <el-select
                                v-model="worm"
                                multiple
                                collapse-tags
                                placeholder="请选择"
                                @change="wormChange"
                                popper-class="selectStyle_list"
                            >
                                <el-option
                                    v-for="item in wormData"
                                    :key="item.value"
                                    :label="item.key"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="chartList_item_chart">
                            <znyEchart :chartData="InsectSituationOverviewBarOption" :chartType="'bar'"></znyEchart>
                        </div>
                    </div>
                    <div class="chartList_item chartList_item1">
                        <div class="chartList_item_title">总虫情种类占比</div>
                        <div class="chartList_item_chart">
                            <znyEchart :chartData="InsectSituationTypePieOption" :chartType="'pie'"></znyEchart>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { chart } from '../../js/chart.js'
import WormCaseService from '../../jaxrs/concrete/com.zny.ia.api.WormCaseService.js'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js'
// import wormType from '../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$虫情种类.js'
export default {
    data() {
        return {
            insectCollectTime:'',
            title: '',
            dialogVisible: false,
            areaIsJudge:true,//判断当前用户能否操作该区域
            areaId: '', //区域id
            equipmentId: '', //设备id
            equipmentIdData: [
                { label: '设备1', value: 1 },
                { label: '设备2', value: 2 },
            ],
            showData:true,//是否显示最近监测虫情图片数据
            caseRecently: {}, //最近监测虫情图片数据
            wormList: [],
            WormCaseVariationLineOption: {
                //虫情变化趋势
                show: true,
                option: {},
            },
            worm: [10000],
            wormData: [
                {key:"全部",value:10000},
                {key:"螟蛾",value:77},
                {key:"蝗虫",value:91},
                {key:"金龟子",value:80},
                {key:"稻飞虱",value:4},
                {key:"蚜虫",value:97},
                {key:"红蜘蛛",value:88},
            ], //虫情监测总量图表选择列表数据
            InsectSituationOverviewBarOption: {
                //虫情监测总量图表数据
                show: true,
                option: {},
            },
            InsectSituationTypePieOption: {
                //总虫情种类占比
                show: true,
                option: {},
            },
        }
    },
    mounted() {
        // 弹窗打开
        this.listen('InsectSituationEMDialogOpen', block => {
            this.areaId = block.areaId
            this.title=block.areaName
            this.dialogVisible = true
            this.getEquipmentData()
            this.areaJudge()
        })
        // 数据上报成功，刷新数据
        this.listen('insectSituationUpdateSuccess', () => {
            this.getWormCaseRecently()
            this.getWormCaseVariation()
            this.getWormCaseHistogram()
            this.getWormCaseProportion()
        })
    },
    methods: {
        // 判断当前用户能否操作该区域
        areaJudge(){
            CommonService.areaJudge(this.areaId)
            .then(res=>{
                // console.log(res);
                this.areaIsJudge=res
            })
        },
        //获取设备
        getEquipmentData() {
            CommonService.wormListByAreaId(this.areaId).then(res => {
                this.equipmentIdData = res
                if (res.length > 0) {
                    this.equipmentId = res[0].equipmentId
                } else {
                    this.equipmentId = ''
                }
                this.getWormCaseRecently()
            })
        },
        // 设备选择
        equipmentChange() {
            this.getWormCaseRecently()
            this.getWormCaseVariation()
            this.getWormCaseHistogram()
            this.getWormCaseProportion()
        },
        // 弹窗打开时调用
        handleOpen() {
            this.$nextTick(() => {
                // let data = wormType._toArray()
                // data.unshift({
                //     key: '全部',
                //     value: null,
                // })
                // this.wormData = data

                // this.getWormCaseRecently()
                this.getWormCaseVariation()
                this.getWormCaseHistogram()
                this.getWormCaseProportion()
            })
        },
        // 获取最近监测虫情图片
        getWormCaseRecently() {
            WormCaseService.wormCaseRecently(this.equipmentId).then(res => {
                if(JSON.stringify(res) === '{}'){
                    this.showData=false
                    this.caseRecently = {}
                    this.insectCollectTime = null
                    this.wormList = null
                }else{
                    this.showData=true
                    this.caseRecently = res
                    this.insectCollectTime = res.createTime
                    this.wormList = res.wormCaseDetails
                }
                
            })
        },
        // 获取虫情变化趋势图表
        getWormCaseVariation() {
            // CommonService.wormCaseVariation(this.areaId,this.equipmentId)
            CommonService.wormCaseVariation(this.areaId).then(res => {
                chart.WormCaseVariation(res).then(data => {
                    this.WormCaseVariationLineOption = data
                })
            })
        },
        // 选择虫情
        wormChange() {
            this.getWormCaseHistogram()
        },
        // 虫情监测总量柱状图
        getWormCaseHistogram() {
            let worm = [...this.worm]
            worm.forEach(v => { 
                if (v == 10000) {
                    worm = []
                }
            })
            WormCaseService.wormCaseHistogram(this.areaId, worm).then(res => {
                chart.InsectSituationOverviewBar(res).then(data => {
                    this.InsectSituationOverviewBarOption = data
                })
            })
        },
        // 获取虫情种类占比数据
        getWormCaseProportion() {
            WormCaseService.wormCaseProportion(this.areaId).then(res => {
                if(res.wormCaseProportionVoList.length==0||res.wormCaseProportionVoList==null){
                    this.InsectSituationTypePieOption = {
                        show: false,
                        option: {}
                    }
                }else{
                    chart.InsectSituationTypePie(res).then(data => {
                        this.InsectSituationTypePieOption = data
                    })
                }
                
            })
        },
        // 上传图片弹窗打开
        InsectSituationUploadPicturesDialogOpen() {
            this.zEmit('InsectSituationUploadPicturesDialogOpen')
        },
        // 打开历史虫情弹窗
        InsectSituationOfHistoryDialogOpen() {
            this.zEmit('InsectSituationOfHistoryDialogOpen', this.areaId)
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/soilEMDialog.less';
@import '../../assets/css/Dialog/InsectSituationEMDialog.less';
</style>

.centralControlPlatform{
  width: 1920px;
  height: 971px;
  display: flex;
  .title{
    height: 39px;
    line-height: 39px;
    position: absolute;
    top: 0;
    img{
      vertical-align: middle;
      margin: -5px 7px 0 9px;
    }
    span{
      font-size: 16px;
      font-weight: bold;
      color: #DFEEF3;
    }
  }
  .bottom_left{
    position: absolute;
    left: -8px;
    bottom: -11px;
  }
  .bottom_right{
    position: absolute;
    right: -8px;
    bottom: -11px;
  }
  &_con{
    width: 1920px;
    height: 922px;
    margin: 9px 0 0 0;
    display: flex;
  }
  &_left{
    width: 461px;
    margin-left: 40px;
    .centralControlPlatform_smartIOTData{
      width: 461px;
      height: 295px;
      margin-top: 9px;
      position: relative;
      .historyButton{
        position: absolute;
        width: 80px;
        height: 32px;
        top: 0;
        right: 0;
        background: rgba(1,18,21,0);
        box-shadow:inset 0px 0px 10px 0px rgba(0,244,253,0.5);
        border-radius: 2px;
        border: 0;
        outline: 0;
        color: #DFEEF3;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          font-size: 14px;
          color: #DFEEF3;
          line-height: 1;
          transform: translateY(0px); // 垂直位置
        }
      }
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 263px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg1.png) no-repeat 100% center;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        &_row{
          width: 100%;
          height: 290px;
          margin-top:33px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 42px;
                height: 60px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_row2{
          width: 100%;
          height: 290px;
          margin-top:12px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 42px;
                height: 60px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_row3{
          width: 102%;
          height: 290px;
          margin-bottom:15px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 29px;
                height: 42px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 18px;
                line-height: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
      }
    }
    .centralControlPlatform_equipmentAlarm{
      width: 461px;
      height: 609px;
      margin-top: 20px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 579px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg2.png) no-repeat 100% center;
      }
    }
  }
  &_center{
    width: 929px;
    margin: 0 22px;
    .centralControlPlatform_demonstrationMap{
      width: 931px;
      height: 922px;
      margin-top: 9px;
      position: relative;
      &_title{
        width: 931px;
        background: url(../image/monitoringCenter/title8.png) no-repeat 100% center;
        background-size: 100% 100%;
        z-index: 100;
      }
      &_btn{
        position: absolute;
        top: 65px;
        right: 129px;
        z-index: 100;
      }
      &_con{
        width: 929px;
        height: 893px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/bg4.png) no-repeat 100% center;
        background-size: 100% 100%;
        z-index: 99;
      }
    }
  }
  &_right{
    width: 401px;
    margin-right: 40px;
    .centralControlPlatform_meteorologyAlarm{
      width: 401px;
      height: 609px;
      margin-top: 9px;
      position: relative;
      &_title{
        width: 401px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
        background-size: 100% 100%;
      }
      &_con{
        width: 401px;
        height: 577px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg6.png) no-repeat 100% center;
        background-size: 100% 100%;
      }
    }
    .centralControlPlatform_seedlingMonitoring{
      width: 401px;
      height: 296px;
      margin-top: 21px;
      position: relative;
      &_title{
        width: 401px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
        background-size: 100% 100%;
      }
      &_con{
        width: 401px;
        height: 264px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg4.png) no-repeat 100% center;
        background-size: 100% 100%;
      }
    }
  }
}

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 设备上传
     * desc: --
     * value: 0
     */
    设备上传: 0,
    /**
     * label: 人工汇报
     * desc: --
     * value: 1
     */
    人工汇报: 1,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
.soilEMDialog,
.InsectSituationEMDialog{
  .el-dialog{
    height: 889px;
    margin-top: 12vh !important;
    .el-dialog__body{
      height: 790px;
      .line{
        width: 1370px;
        margin: auto;
      }
      .time{
        position: absolute;
        top: 20px;
        left: 260px;
        font-size: 18px;
        font-weight: 400;
        color: #DFEEF3;
      }
      .handleBox{
        display: flex;
        margin-top: 30px;
        &_item{
          width: 110px;
          height: 32px;
          margin-right: 30px;
          .el-select{
            width: 180px;
            height: 32px;
            .el-input__icon {
              line-height: 32px;
            }
            .el-input__inner{
              border-radius: 4px;
            }
          }
          .el-button{
            height: 32px;
            line-height: 9px;
            border-radius: 4px !important;
          }
        }
        &_select{
          width: 180px;
        }
      }
      .dataList{
        margin-top: 30px;
        display: flex;
        .list_item{
          width: 14%;
          display: flex;
          .item_img{
            margin-right: 15px;
            img{
              width: 42px;
              height: 55px;
            }
          }
          .item_text{
            width: 140px;
            font-weight: 400;
            text-align: center;
            div:first-child {
              font-size: 20px;
              color: #70A8FF;
            }
            div:last-child {
              margin-top: 10px;
              font-size: 14px;
              color: #B8D4FF;
            }
          }
        }
      }
      .chartList_item_checkBtn{
        position: absolute;
        top: 225px;
        right: 35px;
        display: flex;
        .checkBtn1{
          width: 42px;
          height: 20px;
          background: url(../../image/centralControlPlatform/di1.png) no-repeat 100% center;
        }
        .checkBtn2{
          width: 63px;
          height: 20px;
          background: url(../../image/centralControlPlatform/di3.png) no-repeat 100% center;
        }
        .checkBtn{
          margin-left: 10px;
          font-size: 14px;
          font-weight: 400;
          color: #DFEEF3;
          cursor: pointer;
          text-align: center;
        }
        .active1{
          color: #FFA72A;
          background: url(../../image/centralControlPlatform/di2.png) no-repeat 100% center;
        }
        .active2{
          color: #FFA72A;
          background: url(../../image/centralControlPlatform/di4.png) no-repeat 100% center;
        }
        
      }
      .chartList{
        width: 1372px;
        height: 610px;
        margin-top: 50px;
        display: flex;
        &_left{
          margin-right: 10px;
        }
        &_left,&_right{
          width: 683px;
          height: 590px;
          .chartList_item{
            width: 678px;
            height: 290px;
            background: rgba(255,255,255,0);
            border: 1px solid #00F4FD;
            box-shadow:inset 0px 0px 28px 0px rgba(0,244,253,0.3);
            text-align: center;
            &_title{
              margin-top: 10px;
              font-size: 16px;
              font-weight: 400;
              color: #DFEEF3;
            }
            &_chart{
              width: 678px;
              height: 237px;
              margin-top: 20px;
            }
          }
          .chartList_item:nth-child(1){
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 设备生成报告
     * desc: --
     * value: 0
     */
    设备生成报告: 0,
    /**
     * label: 人工填报数值报告
     * desc: --
     * value: 1
     */
    人工填报数值报告: 1,
    /**
     * label: 人工上传文件报告
     * desc: --
     * value: 2
     */
    人工上传文件报告: 2,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
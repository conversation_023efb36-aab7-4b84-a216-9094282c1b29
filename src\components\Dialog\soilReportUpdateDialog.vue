<template>
  <div class="soilReportUpdateDialog dialogStyle">
    <el-dialog
      title="土壤数据汇报"
      width="24%"
      :visible.sync="dialogVisible"
      center=""
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="closeDialog('form')">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="formList">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
           <el-form-item label="信息项">
              <div>当前监测数值</div>
            </el-form-item>
            <el-form-item label="设备层数：" prop="layer" class="formStyle">
              <el-select 
                v-model="form.layer" 
                clearable 
                placeholder="请选择设备层数"
                popper-class="selectStyle_list">
                  <el-option
                    v-for="item in soilLayerData"
                    :key="item.value"
                    :label="item.key"
                    :value="item.value">
                  </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="土壤温度：" prop="soilTemperature" class="formStyle">
              <el-input v-model="form.soilTemperature">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  ℃
                </i>
              </el-input>
              <!-- <div class="unit">℃</div> -->
            </el-form-item>
            <el-form-item label="土壤湿度：" prop="soilHumidity" class="formStyle">
              <el-input v-model="form.soilHumidity">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  %
                </i>
              </el-input>
              <!-- <div class="unit">%</div> -->
            </el-form-item>
            <el-form-item label="电导率：" prop="conductivity" class="formStyle">
              <el-input v-model="form.conductivity">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  uS/cm
                </i>
              </el-input>
              <!-- <div class="unit">uS/cm</div> -->
            </el-form-item>
            <el-form-item label="PH值：" prop="ph" class="formStyle">
              <el-input v-model="form.ph"></el-input>
            </el-form-item>
            <el-form-item label="氮含量：" prop="nitrogen" class="formStyle">
              <el-input v-model="form.nitrogen">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  mg/kg
                </i>
              </el-input>
              <!-- <div class="unit">mg/kg</div> -->
            </el-form-item>
            <el-form-item label="磷含量：" prop="phosphorus" class="formStyle">
              <el-input v-model="form.phosphorus">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  mg/kg
                </i>
              </el-input>
              <!-- <div class="unit">mg/kg</div> -->
            </el-form-item>
            <el-form-item label="钾含量：" prop="potassium" class="formStyle">
              <el-input v-model="form.potassium">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  mg/kg
                </i>
              </el-input>
              <!-- <div class="unit">mg/kg</div> -->
            </el-form-item>
            <el-form-item label="汇报时间：" prop="reportTime" class="formStyle">
              <el-date-picker
                v-model="form.reportTime"
                type="datetime"
                placeholder="选择日期时间"
                popper-class='datePickerStyle'
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
              <!-- <el-date-picker
                v-model="form.reportTime"
                type="date"
                placeholder="选择日期"
                popper-class='datePickerStyle'
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd">
              </el-date-picker> -->
            </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="searchButtonStyle">
          <el-button @click="cancel('form')">取消</el-button>
        </div>
        <div class="submitButtonStyle">
          <el-button @click="submit('form')">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import layerData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js';
import SoilService from '../../jaxrs/concrete/com.zny.ia.api.SoilService.js';
export default {
  data(){
    return{
      dialogVisible:false,
      soilLayerData:layerData._toArray(),//土壤层数据
      form: {
        areaId:"",
        layer:0,
        soilTemperature: '',
        soilHumidity: '',
        conductivity: '',
        ph: '',
        nitrogen: '',
        phosphorus: '',
        potassium: '',
        reportTime:""
      },
      rules:{
        layer: [
          { required: true, message: '请选择设备层数', trigger: 'change' },
        ],
        soilTemperature: [
          // { required: true, message: '请输入土壤温度', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        soilHumidity: [
          // { required: true, message: '请输入土壤湿度', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        conductivity: [
          // { required: true, message: '请输入电导率', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        ph: [
          // { required: true, message: '请输入PH值', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        nitrogen: [
          // { required: true, message: '请输入氮含量', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        phosphorus: [
          // { required: true, message: '请输入磷含量', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        potassium: [
          // { required: true, message: '请输入钾含量', trigger: 'blur' },
           {
            pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,1})?$)/,
            message: '必须为数字值且保留一位小数',
          },
        ],
        reportTime: [
          { required: true, message: '请选择汇报时间', trigger: 'change' },
        ],
      }
    }
  },
  mounted(){
    this.listen('soilReportUpdateDialogOpen', (areaId) => {
      this.form.areaId=areaId
      this.dialogVisible=true
    })
  },
  methods:{
    // 关闭弹窗
    closeDialog(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
    },
    // 取消
    cancel(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
    },
    // 确定提交
    submit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let po={
            areaId:this.form.areaId,
            layer:this.form.layer,
            soilTemperature:this.form.soilTemperature,
            soilHumidity: this.form.soilHumidity,
            conductivity: this.form.conductivity,
            ph: this.form.ph,
            nitrogen: this.form.nitrogen,
            phosphorus: this.form.phosphorus,
            potassium:this.form.potassium,
            reportTime:this.form.reportTime,
          }
          SoilService.addData(po)
          .then(()=>{
            // console.log(res);
            that.$refs[formName].resetFields();
            that.dialogVisible=false
            that.$message({
              message: '数据上报成功',
              type: 'success'
            });
            that.zEmit('soilUpdateSuccess')
          }).catch(()=>{
            that.$message.error('数据上报失败,请重试!');
          })
        }
      })
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
  @import '../../assets/css/Dialog/soilReportUpdateDialog.less';

</style>
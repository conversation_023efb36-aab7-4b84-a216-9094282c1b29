<template>
    <div class="soilOfHistoryDialog dialogStyle">
        <el-dialog
            title="土壤历史记录"
            :visible.sync="dialogVisible"
            width="90%"
            center
            :show-close="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            @open="handleOpen"
        >
            <div class="line"></div>
            <div class="close" @click="dialogVisible = false">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="handleBox">
                <div class="handleBox_item formStyle">
                    <el-select v-model="areaId" clearable placeholder="请选择种植区域" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in areaIdData"
                            :key="item.areaId"
                            :label="item.areaName"
                            :value="item.areaId"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-select v-model="layer" clearable placeholder="请选择设备层数" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in soilLayerData"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-select v-model="from" clearable placeholder="请选择数据来源" popper-class="selectStyle_list">
                        <el-option
                            v-for="item in fromData"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-date-picker
                        v-model="time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        popper-class="datePickerStyle"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </div>
                <div class="handleBox_item searchButtonStyle">
                    <el-button @click="search">查询</el-button>
                </div>
            </div>
            <div class="tableBox tableStyle">
                <el-table :data="tableData" border style="width: 100%" height="405px">
                    <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                    <el-table-column prop="areaName" align="center" label="区域"></el-table-column>
                    <el-table-column align="center" label="设备层数" width="120">
                        <template slot-scope="scoped">
                            <div>
                                {{ scoped.row.layer | soilLayerFormat }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="土壤温度℃">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.soilTemperature.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.soilTemperature.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.soilTemperature.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.soilTemperature.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.soilTemperature.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="土壤湿度%">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.soilHumidity.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.soilHumidity.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.soilHumidity.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.soilHumidity.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.soilHumidity.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="电导率">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.conductivity.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.conductivity.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.conductivity.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.conductivity.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.conductivity.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="氮含量">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.nitrogen.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.nitrogen.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.nitrogen.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.nitrogen.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.nitrogen.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="磷含量">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.phosphorus.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.phosphorus.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.phosphorus.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.phosphorus.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.phosphorus.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="钾含量">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.potassium.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.potassium.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.potassium.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.potassium.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.potassium.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="PH值">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.ph.isArtificial">
                                <el-tooltip popper-class="tooltipStyle" placement="top">
                                    <div slot="content">
                                        <div class="item">
                                            人工汇报
                                        </div>
                                        <br />
                                        <div class="item">汇报人：{{ scoped.row.ph.loginName }}</div>
                                        <br />
                                        <div class="item">汇报时间：{{ scoped.row.ph.reportTime }}</div>
                                    </div>
                                    <div style="color:#18BBFF">{{ scoped.row.ph.value }}</div>
                                </el-tooltip>
                            </div>
                            <div v-else>
                                {{ scoped.row.ph.value }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" align="center" label="上报时间"></el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import layerData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js'
import dataSources from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$数据来源.js'
import SoilService from '../../jaxrs/concrete/com.zny.ia.api.SoilService.js'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js'
export default {
    data() {
        return {
            dialogVisible: false,
            areaId: null,
            areaIdData: [
                // { label: '玉米种植一区', value: 1 },
                // { label: '玉米种植二区', value: 2 },
            ],
            layer: 0,
            soilLayerData: layerData._toArray(), //土壤层数据
            from: null,
            fromData: dataSources._toArray(), //数据来源
            time: [],
            tableData: [],
        }
    },
    mounted() {
        this.listen('soilOfHistoryDialogOpen', () => {
            this.dialogVisible = true
            this.getAreaData()
        })
    },
    methods: {
        // 弹窗打开时
        handleOpen() {
            this.getListHistoryRecord()
        },
        // 获取所有区域信息
        getAreaData(){
            CommonService.allAreaListInformation()
            .then(res=>{
                this.areaIdData=res
            })
        },
        // 搜索
        search() {
            if (this.layer == '') {
                this.layer = null
            }
            this.getListHistoryRecord()
        },
        // 获取历史记录列表
        getListHistoryRecord() {
            let po = {
                num: 1,
                pageSize: 100,
                condition: {
                    areaId: this.areaId == '' ? null : this.areaId,
                    startTime: this.time.length == 0 ? '' : this.time[0],
                    endTime: this.time.length == 0 ? '' : this.time[1],
                    from: this.from == '' ? null : this.from,
                    layer: this.layer == '' ? null : this.layer,
                },
            }
            SoilService.listHistoryRecord(po).then(res => {
                this.tableData = res.list
            })
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
@import '../../assets/css/Dialog/meteorologyOfHistoryDialog.less';
@import '../../assets/css/Dialog/soilOfHistoryDialog.less';
</style>

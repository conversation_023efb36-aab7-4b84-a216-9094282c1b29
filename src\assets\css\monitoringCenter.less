.monitoringCenter{
  width: 1920px;
  height: 971px;
  display: flex;
  .title{
    height: 39px;
    line-height: 39px;
    position: absolute;
    top: 0;
    img{
      vertical-align: middle;
      margin: -5px 7px 0 9px;
    }
    span{
      font-size: 16px;
      font-weight: bold;
      color: #DFEEF3;
    }
  }
  .bottom_left{
    position: absolute;
    left: -8px;
    bottom: -11px;
  }
  .bottom_right{
    position: absolute;
    right: -8px;
    bottom: -11px;
  }
  &_left{
    width: 461px;
    margin-left: 40px;
    .smartIOTData{
      width: 461px;
      height: 295px;
      margin-top: 9px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 263px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg1.png) no-repeat 100% center;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        &_left,&_right{
          width: 35%;
          height: 290px;
          margin-top: 32px;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 42px;
                height: 60px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_row{
          width: 100%;
          height: 290px;
          margin-top:33px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 42px;
                height: 60px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_row2{
          width: 100%;
          height: 290px;
          margin-top:12px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 42px;
                height: 60px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_row3{
          width: 102%;
          height: 290px;
          margin-bottom:15px;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          &_item{
            display: flex;
            justify-content: space-around;
            .item_img{
              img{
                width: 29px;
                height: 42px;
              }
            }
            .item_text{
              width: 100px;
              font-weight: 400;
              div:first-child {
                font-size: 18px;
                line-height: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        &_left{
          margin-left: 49px;
        }
        &_right{
          margin-left: 49px;
        }
      }
    }
    .equipmentAlarm{
      width: 461px;
      height: 609px;
      margin-top: 20px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 579px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg2.png) no-repeat 100% center;
      }
    }
  }
  &_center{
    width: 874px;
    margin: 0 22px;
    .situationOverview{
      width: 874px;
      height: 252px;
      margin-top: 19px;
      position: relative;
      &_title{
        width: 874px;
        background: url(../image/monitoringCenter/title2.png) no-repeat 100% center;
      }
      &_con{
        width: 874px;
        height: 223px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/bg3.png) no-repeat 100% center;
        text-align: center;
        &_top{
          display: flex;
          position: absolute;
          top: 35px;
          .details{
            width: 119px;
            height: 135px;
            font-weight: 400;
            div:first-child {
              font-size: 16px;
            }
            div:last-child {
              font-size: 14px;
              color: #DFEEF3;
              margin-top: 5px;
            }
          }
          .details:not(:first-child){
            margin-left: 70px;
          }
          .details:nth-child(1){
            background: url(../image/monitoringCenter/icon7.png) no-repeat 100% center;
            margin-left: 90px;
            div:first-child {
              color: #00F1FF;
            }
          }
          // .details:nth-child(2){
          //   background: url(../image/monitoringCenter/icon8.png) no-repeat 100% center;
          //   div:first-child {
          //     color: #F8B854;
          //   }
          // }
          .details:nth-child(2){
            background: url(../image/monitoringCenter/icon9.png) no-repeat 100% center;
            div:first-child {
              color: #4BE497;
            }
          }
          .details:nth-child(3){
            background: url(../image/monitoringCenter/icon10.png) no-repeat 100% center;
            div:first-child {
              color: #FFDA30;
            }
          }
          .details:nth-child(4){
            background: url(../image/monitoringCenter/icon11.png) no-repeat 100% center;
            div:first-child {
              color: #22FFB9;
            }
          }
        }
        &_bottom{
          margin-top: 110px;
        }
      }
    }
    .demonstrationMap{
      width: 874px;
      height: 643px;
      margin-top: 20px;
      position: relative;
      &_title{
        width: 874px;
        background: url(../image/monitoringCenter/title8.png) no-repeat 100% center;
        z-index: 100;
      }
      &_con{
        width: 874px;
        height: 620px;
        position: absolute;
        bottom: 0;
        // background: rgba(1,13,23,0.2);
        background: url(../image/monitoringCenter/bg4.png) no-repeat 100% center;
        z-index: 99;
      }
    }
  }
  &_right{
    width: 461px;
    margin-right: 40px;
    .traceabilityInfo{
      width: 461px;
      height: 294px;
      margin-top: 9px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 264px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg3.png) no-repeat 100% center;
      }
    }
    .transactionProportion{
      width: 461px;
      height: 296px;
      margin-top: 21px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 264px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg4.png) no-repeat 100% center;
      }
    }
    .realPrice{
      width: 461px;
      height: 296px;
      margin-top: 21px;
      position: relative;
      &_title{
        width: 461px;
        background: url(../image/monitoringCenter/title1.png) no-repeat 100% center;
      }
      &_con{
        width: 461px;
        height: 264px;
        position: absolute;
        bottom: 0;
        background: url(../image/monitoringCenter/xbg5.png) no-repeat 100% center;
        .realPrice_noData{
          width: 461px;
          height: 207px;
          font-size: 24px;
          font-weight: 400;
          color: rgba(174, 203, 209, 0.3);
          text-align: center;
          img{
            width: 252px;
            height: 150px;
            margin-top: 20px;
          }
        }
        .realPrice_list{
          width: 461px;
          height: 207px;
          overflow: scroll;
          margin-top: 15px;
          #scroll1,#scroll2{
            .list_item{
              display: flex;
              justify-content: start;
              font-size: 14px;
              font-weight: 400;
              color: #DFEEF3;
              .item{
                padding: 8px 0;
                word-wrap: break-word; /*强制换行*/
                overflow: hidden; /*超出隐藏*/
                text-overflow: ellipsis;/*隐藏后添加省略号*/
                white-space: nowrap;/*强制不换*/
              }
              .item:nth-child(1){
                width: 125px;
                margin-left: 23px;
              }
              .item:nth-child(2){
                width: 30px;
                margin-left: 30px;
              }
              .item:nth-child(3){
                width: 80px;
                color: #E9A31F;
                margin-left:30px;
              }
              .item:nth-child(4){
                width: 80px;
                margin-left:30px;
                margin-right: 23px;
              }
            }
            .list_item:nth-child(2n) {
              background: rgba(255,255,255,0.06);
            }
          }
        }
      }
    }
  }
}
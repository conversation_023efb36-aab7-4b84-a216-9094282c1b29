<template>
    <div class="tracePage">
        <div class="tracePage_header">{{traceDetail.name}}</div>
        <div class="tracePage_code">
            <div class="tracePage_code_name">档案溯源码：</div>
            <div class="tracePage_code_item">
                <div class="qrcode" ref="qrCodeUrl"></div>
                <div class="tracePage_code_item_text">{{traceDetail.serialNumber}}</div>
            </div>
        </div>
        <div class="tracePage_part">
            <div class="tracePage_part_title">
                <span class="tracePage_part_title_line"></span>
                <div class="tracePage_part_title_text">产品简介</div>
                <div class="tracePage_part_title_line"></div>
            </div>
            <div class="tracePage_part_con">
                <div class="tracePage_part_con_left">
                    <el-carousel>
                        <el-carousel-item v-for="item in traceDetail.propagandaPics" :key="item">
                            <img :src=item alt="">
                        </el-carousel-item>
                    </el-carousel>
                </div>
                <div class="tracePage_part_con_right">{{traceDetail.synopsis}}</div>
            </div>
        </div>
        <div class="tracePage_part tracePage_part2">
            <div class="tracePage_part_title">
                <span class="tracePage_part_title_line"></span>
                <div class="tracePage_part_title_text">基本信息</div>
                <div class="tracePage_part_title_line"></div>
            </div>
            <div class="tracePage_part_con">
                <table  border="1">
                    <tr>
                        <th>
                            品种
                        </th>
                        <td>
                            {{traceDetail.variety}}
                        </td>
                        <th>
                            批次产量
                        </th>
                        <td>
                            {{traceDetail.yield}}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            种植批次
                        </th>
                        <td>
                            {{batchId}}
                        </td>
                        <th>
                            生长地点区域
                        </th>
                        <td v-if="traceDetail.areaVo">
                            {{traceDetail.areaVo.areaName}}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            收货批次
                        </th>
                        <td>
                            {{finishId}}
                        </td>
                        <th>
                            上市时间
                        </th>
                        <td>
                            {{traceDetail.listedTime}}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="tracePage_part tracePage_part3">
            <div class="tracePage_part_title">
                <span class="tracePage_part_title_line"></span>
                <div class="tracePage_part_title_text">溯源档案</div>
                <div class="tracePage_part_title_line"></div>
            </div>
            <div class="tracePage_part_con3">
                <div class="tracePage_part_con3_list">
                    <div class="tracePage_part_con3_list_name">档案完整度：</div>
                    <div class="tracePage_part_con3_list_item">
                        <el-progress :percentage=traceDetail.complete :stroke-width=20 color="#00CBB0"></el-progress>
                    </div>
                </div>
                <div class="tracePage_part_con3_list" v-if="traceDetail.growingEnvironmentPics.length>0">
                    <div class="tracePage_part_con3_list_name">生长环境图片：</div>
                    <div class="tracePage_part_con3_list_item">
                        <div class="itemImage" v-for="(item,index) in traceDetail.growingEnvironmentPics" :key="index">
                            <img :src=item alt="">
                        </div>
                    </div>
                </div>
                <div class="tracePage_part_con3_list" v-if="videoUrl&&videoUrl!=''">
                    <div class="tracePage_part_con3_list_name">相关视频：</div>
                    <div class="tracePage_part_con3_list_item">
                        <div class="itemVideo">
                            <video id="myVideo" class="video-js vjs-big-play-centered">
                                <source :src=videoUrl type="video/mp4">
                            </video>
<!--                            <img src="../assets/image/centralControlPlatform/test.jpeg" alt="">-->
                        </div>
                    </div>
                </div>
                <div class="tracePage_part_con3_list tracePage_part_con3_list2">
                    <div class="tracePage_part_con3_list_name"></div>
                    <div class="tracePage_part_con3_list_item">
                        <div class="itemTable1" v-if="irrigationList2.length>0">
                            <div class="itemTable1_name">灌溉记录：</div>
                            <div class="itemTable1_item">
                                <table border="1">
                                    <tr>
                                        <th>灌溉时间点</th>
                                        <th>灌溉量m³</th>
                                    </tr>
                                    <tr v-for="(item,index) in irrigationList2" :key="index">
                                        <td>{{item.showTime}}</td>
                                        <td>{{item.value}}</td>
                                    </tr>
                                </table>
                                <div class="itemTable1_item_expand" v-if="irrigationList2.length<irrigationList.length" @click="expandData()">展开<i class="el-icon-arrow-down" style="font-weight: bold"></i></div>
                            </div>
                        </div>
                        <div class="itemTable1 itemTable2" v-if="traceDetail.waterReportList&&traceDetail.waterReportList.length>0">
                            <div class="itemTable2_name itemTable1_name">水质环境记录：</div>
                            <div class="itemTable2_item itemTable1_item">
                                <table border="1">
                                    <tr v-for="(item,index) in traceDetail.waterReportList" :key="index">
                                        <td @click="reportOpen(item.reportUrl)">{{item.reportName}}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tracePage_part tracePage_part4">
            <div class="tracePage_part_title">
                <span class="tracePage_part_title_line"></span>
                <div class="tracePage_part_title_text">更多认证</div>
                <div class="tracePage_part_title_line"></div>
            </div>
            <div class="tracePage_part4_link">
                <div class="linkItem" @click="reportOpen(traceDetail.traceSourceCertificationVo.examiningReport)">农产品检测报告</div>
                <div class="linkItem" @click="reportOpen(traceDetail.traceSourceCertificationVo.organicCropReport)">有机农产品认证</div>
                <div class="linkItem" @click="reportOpen(traceDetail.traceSourceCertificationVo.greenCropReport)">绿色农产品认证</div>
            </div>
        </div>
    </div>
</template>

<script>
    import TraceSourceCommonService from '../jaxrs/concrete/com.zny.ia.api.TraceSourceCommonService'
    import QRCode from 'qrcodejs2'
    import cropData from '../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类'
    export default {
        name: "tracePage",
        data(){
            return{
                traceId:'',
                traceDetail:{
                    growingEnvironmentPics:[]
                },
                qrCodeUrl:'',
                videoUrl:'',
                batchId:'',
                finishId:'',
                irrigationList:[],
                irrigationList2:[],
            }
        },
        filters:{
            cropFilter(val){
                if(val){
                    return cropData._lableOf(val)
                }
            }
        },
        created() {
            this.traceId=this.$route.query.id;
            this.getTraceDetail()
        },
        mounted() {

        },
        methods:{
            creatQrCode() {
                var qrcode = new QRCode(this.$refs.qrCodeUrl, {
                        text: this.qrCodeUrl, // 需要转换为二维码的内容
                        width: 100,
                        height: 100,
                        colorDark: '#000000',
                        colorLight: '#ffffff',
                        correctLevel: QRCode.CorrectLevel.H
                    })
            },
            //播放视频
            playVideo() {
                let myPlayer = this.$video("myVideo", {
                    controls: true,
                    autoplay: false,//自动播放属性,muted:静音播放
                    //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
                    preload: "auto",//none不预加载任何数据，直到用户开始播放才开始下载
                    width: "800px",
                    height: "400px",
                    loop: false,//循环
                    //poster:""//在视频开始播放之前显示的图像的URL
                });
            },
            //获取溯源信息
            getTraceDetail(){
                TraceSourceCommonService.traceSourceDetail(this.traceId)
                .then(res=>{
                    this.batchId=res.batchIdVo.batchId
                    this.finishId=res.batchIdVo.finishId
                    //处理灌溉记录数据
                    this.irrigationList=[...res.irrigationList]
                    if(res.irrigationList.length>=3){
                        this.irrigationList2=res.irrigationList.slice(0,3)
                    }else{
                        this.irrigationList2=res.irrigationList
                    }
                    let url = 'http://192.168.1.112:8993/#/home?id='
                    this.qrCodeUrl=url+res.id
                    this.videoUrl=res.video
                    var that = this
                    that.$nextTick(function () {
                        setTimeout(function () {
                            that.creatQrCode();
                            that.playVideo()
                        },1000)
                    })
                    this.traceDetail=res
                })
            },
            //展开数据
            expandData(){
                this.irrigationList2=this.irrigationList
            },
            //报告
            reportOpen(url){
                if(url&&url!=""){
                    window.open(url)
                }
            },
        }
    }
</script>

<style lang="less">
@import "../assets/css/tracePage";
</style>
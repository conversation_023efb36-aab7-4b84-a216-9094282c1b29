/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const SoilService = {
    /**
     * 土壤历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'listHistoryRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c4795d7ed0e42a8b3fe2e767784c16d97a4aad9d', po)
            : execute(concreteModuleName, `/SoilService/listHistoryRecord`, 'json', 'POST', po);
    }, 
    /**
     * 土壤报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'listAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '37164de372060ecaad690c9a4adb086568686a9b', po)
            : execute(concreteModuleName, `/SoilService/listAlarmRecord`, 'json', 'POST', po);
    }, 
    /**
     * 灌溉主页面-最近七天累计土壤墒情统计
     * @param {*} areaId 区域id null-全部
     * @returns Promise 
     */
    'cumulativeSoilListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd5d9520ccd45d59b7b0707c066a4075e7763bdbe', areaId)
            : execute(concreteModuleName, `/SoilService/cumulativeSoilListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 土壤数据汇报
     * @param {*} po 
     * @returns Promise 
     */
    'addData': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0e6f2331a53504c0fb496c450461ca738e7858e4', po)
            : execute(concreteModuleName, `/SoilService/addData`, 'json', 'POST', po);
    }, 
    /**
     * 土壤聚合图表
     * @param {*} po 
     * @returns Promise 
     */
    'listChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8109541769fbbbe892d4019a4f9865f901e584c3', po)
            : execute(concreteModuleName, `/SoilService/listChart`, 'json', 'POST', po);
    }, 
    /**
     * 土壤报警
     * @returns Promise 
     */
    'listAlarm': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c4ef5f956dd5579c8cf6be96e6f2018c8e40a1cb')
            : execute(concreteModuleName, `/SoilService/listAlarm`, 'json', 'GET');
    }, 
    /**
     * 获取实时土壤数据
     * @param {*} layer 土壤层
     * @param {*} areaId 区域id null-所有
     * @returns Promise 
     */
    'findNewInfo': function (layer, areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ba99d83cca5131f68502a81bce10edd21ba5f912', {layer, areaId})
            : execute(concreteModuleName, `/SoilService/findNewInfo`, 'json', 'POST', { layer, areaId });
    }
}

export default SoilService

<template>
<zny-rc :width="1920" :height="1080" @scale="scaleChange">
  <div class="home dialogStyle">
    <div class="header">
      <div class="header_con">
        <div class="header_con_bg">
          <img src="../assets/image/monitoringCenter/header.png" alt="">
        </div>
        <div class="header_con_time">
          {{date}} {{time}} {{week}}
        </div>
        <div class="header_con_title">
          {{title}}
        </div>
        <div v-if="showHome" class="header_con_jump header_con_jumpLeft" @click="jump('/centralControlPlatform')">
          {{jumpLeft}}
        </div>
        <div v-if="!showHome" class="header_con_jump header_con_jumpLeft" @click="jump('/monitoringCenter')">
          {{jumpLeft}}
        </div>
        <router-link v-if="role!=2" :to="{path:'/managementSystem'}" target="_blank">
          <div class="header_con_jump header_con_jumpRight">
            {{jumpRight}}
          </div>
        </router-link>
        <div class="header_con_nickname">
          {{nickname}}
        </div>
        <div class="header_con_portrait" @click="showTips=showTips==true?false:true">
          <img src="../assets/image/monitoringCenter/portrait.png" alt="">
        </div>
        <div class="header_con_tips" v-show="showTips">
          <div class="tips_con" @click="changePassword">修改密码</div>
          <div class="tips_bottom_line"></div>
          <div class="tips_con" @click="logout">退出登录</div>
          <div class="tips_bottom_line"></div>
        </div>
      </div>
    </div>
    <router-view />
    
    <el-dialog
      class="changePassDialog"
      title="修改密码"
      center
      width="30%"
      :show-close="false"
      :modal-append-to-body="false"
      :visible.sync="changePassDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="changePassDialog=false">
        <img src="../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="formList">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px" class="demo-ruleForm">
          <el-form-item label="原密码：" prop="oldPassWord" class="formStyle">
            <el-input v-model="ruleForm.oldPassWord" placeholder="请输入原密码"></el-input>
          </el-form-item>
          <el-form-item label="新密码：" prop="newPassWord" class="formStyle">
            <el-input v-model="ruleForm.newPassWord" placeholder="请输入新密码"></el-input>
          </el-form-item>
          <el-form-item label="确认新密码：" prop="newPassWord2" class="formStyle">
            <el-input v-model="ruleForm.newPassWord2" placeholder="请输入新密码"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="searchButtonStyle" @click="cancel()">
          <el-button>取消</el-button>
        </div>
        <div class="submitButtonStyle" @click="confirm('ruleForm')">
          <el-button>提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</zny-rc>
</template>

<script>
import {gettime} from '../js/getTime.js';
import LoginService from '../jaxrs/concrete/com.zny.ia.api.LoginService.js';
export default {
  name: "Home",
  data(){
    return{
      role:localStorage.getItem('userRoles'),
      date:new Date().toLocaleDateString(),//当前日期
      time:"",
      week:"",
      showHome:true,
      title:"日光温室监测中心",
      jumpLeft:"中控平台",
      jumpRight:"管理系统",
      timer:null,
      nickname:localStorage.getItem('nickname'),
      showTips:false,//显示修改密码、退出登录弹窗
      changePassDialog:false,//修改密码弹窗
      ruleForm:{
        oldPassWord:"",
        newPassWord:"",
        newPassWord2:"",
      },
      rules:{
        oldPassWord: [
          { required: true, message: '请输入原密码', trigger: 'blur' },
        ],
        newPassWord: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
        ],
        newPassWord2: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
        ],
      },
    }
  },
  mounted(){
    this.time=gettime.getReallTime()
    this.week=gettime.getWeekDate()
    this.timer=setInterval(()=>{
      this.time=gettime.getReallTime()
    },10000)
    this.jumpLeft = window.location.hash.split("#/")[1]=='monitoringCenter'?'中控平台':'监控中心';
    this.showHome = window.location.hash.split("#/")[1]=='monitoringCenter'?true:false
  },
  methods:{
    //伸缩监听
    scaleChange(val){
      localStorage.setItem('scaleValue',val.scale)
      this.zEmit('scaleChangeWatch',val)
    },
    // 跳转
    jump(url){
      this.showHome = this.showHome ? false : true
      this.title = this.title == '日光温室监测中心' ? '日光温室中控平台' : '日光温室监测中心'
      this.jumpLeft = this.jumpLeft == '中控平台' ? '监控中心' : '中控平台'
      this.$router.push({path:url})
    },
    // 修改密码
    changePassword(){
      this.showTips=false
      this.changePassDialog=true
    },
    //修改密码取消
    cancel(){

    },
    //修改密码确认
    confirm(formName){
      var that = this
      if(that.ruleForm.newPassWord2==that.ruleForm.newPassWord){
        that.$refs[formName].validate((valid) => {
          if (valid) {
            LoginService.passwordUpdate(that.ruleForm.oldPassWord,that.ruleForm.newPassWord)
                    .then(()=>{
                      that.$message({
                        type: 'success',
                        message: '密码修改成功!'
                      });
                      this.$router.push({path:'/'})
                    })
          }
        })
      }else{
       that.$message({
         message:'两次输入的密码不一致，请再次输入',
         type:'warning'
       })
      }

    },
    // 退出登录
    logout(){
      LoginService.logOut()
      .then(()=>{
        this.$router.push({path:'/'})
      })
    },
  },
};
</script>
<style lang="less">
@import '../assets/css/home.less';
</style>

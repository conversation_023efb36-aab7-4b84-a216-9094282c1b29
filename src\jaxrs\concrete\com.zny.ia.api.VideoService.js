/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const VideoService = {
    /**
     * 历史视频 - 返回视频url
     * @param {*} videoId 视频id
     * @returns Promise 
     */
    'detailVideo': function (videoId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c8d97c550436fd774819295524af616572c74ac6', videoId)
            : execute(concreteModuleName, `/VideoService/detailVideo`, 'text', 'POST', { videoId });
    }, 
    /**
     * 历史视频列表
     * @param {*} po 
     * @returns Promise 
     */
    'listVideo': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '08483665fab34744d0e4e504ba756eaa23b3cfb7', po)
            : execute(concreteModuleName, `/VideoService/listVideo`, 'json', 'POST', po);
    }
}

export default VideoService

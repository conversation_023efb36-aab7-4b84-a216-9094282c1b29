import axios from 'axios'

// 瓦片地址获取URL
let url = "https://tiles.zhongnongyun.cn/tile_address_gcj02.txt"

/**
 * 获取地图瓦片URL
 * @param {string} configUrl - 获取瓦片配置的URL
 */
export function getAmapTileUrl(configUrl = url) {
    return axios({
        url: configUrl,
        method: "get",
        timeout: 10000 // 10秒超时
    }).then(res => {
        let amapUrl = res.data.replace('&x=%d&y=%d&z=%d', '&x=[x]&y=[y]&z=[z]')
        localStorage.setItem('amapTileUrl', amapUrl)
        console.log('成功获取瓦片地址:', amapUrl)
        return amapUrl
    }).catch(error => {
        console.error('获取地图瓦片URL失败:', error)
        throw error 
    })
}

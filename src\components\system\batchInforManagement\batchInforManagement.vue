<template>
  <div class="batchInforManagement systemDialogStyle">
    <!-- <div class="batchInforManagement_tabList">
      <div class="tabList_item" v-for="(item,index) in tabList" :key="index">
        <div :class="{active:tabActive==item.areaId}" @click="tabClick(item)">
          {{item.areaName}}
        </div>
      </div>
    </div> -->
      <div class="batchInforManagement_con">
        <div class="handleBox">
          <div class="handleBox_item systemFormStyle">
            <el-select v-model="selectedAreaId" clearable placeholder="请选择区域">
              <el-option
                v-for="item in areaIdList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaId">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item systemFormStyle">
            <el-select v-model="growCrops" clearable placeholder="请选择作物">
              <el-option
                v-for="item in growCropList"
                :key="item.value"
                :label="item.key"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search">查询</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="downloadList">下载</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="plantBatchDialogOpen">生成种植批次</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="plantBatchEndDialogOpen">结束种植批次</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="batchHistoryOpen">批次历史</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="updateGrowStageDialogOpen">更新生长阶段</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="当前种植批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.batchId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="当前收获批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.finishId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="种植区域">
            <template slot-scope="scoped">
              <span v-for="(item,index) in scoped.row.areaVoList" :key="index">
                {{item.areaName}}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="生长阶段">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.growStage}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="种植作物">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.crop | cropFormat}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="产值/元"
            width="120">
            <template slot-scope="scoped">
               <!-- <div @click="outputValueRepord(scoped.row)" style="color:#007EFF;cursor: pointer;">
                填写
              </div> -->
              <div v-if="scoped.row.outputValue==null" @click="outputValueRepord(scoped.row)" style="color:#007EFF;cursor: pointer;">
                填写
              </div>
              <div v-else>
                {{scoped.row.outputValue}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            align="center" 
            label="产量(kg)"
            width="120">
            <template slot-scope="scoped">
              <!-- <div @click="yieldRepord(scoped.row)" style="color:#007EFF;cursor: pointer;">
                填写
              </div> -->
              <div v-if="scoped.row.yield==null" @click="yieldRepord(scoped.row)" style="color:#007EFF;cursor: pointer;">
                填写
              </div>
              <div v-else>
                {{scoped.row.yield}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="200"
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="detailsOpen(scoped.row)">详情</span>
              <span class="viewData" @click="createHarvestBatch(scoped.row)">生成收获批次</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    <!-- <div class="batchInforManagement_con" v-show="administrators=='subAdmin'">
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="growCrops" clearable placeholder="请选择作物">
            <el-option
              v-for="item in growCropList"
              :key="item.value"
              :label="item.key"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search">查询</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="downloadList">下载</el-button>
        </div>
        <div class="handleBox_item handleBox_item1 systemSearchButtonStyle2">
          <el-button @click="batchHistoryOpen">批次历史</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="收获批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.finishId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            align="center" 
            label="种植批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.batchId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="harvestDate"
            align="center" 
            label="收获时间">
          </el-table-column>
          <el-table-column
            prop="plantDate"
            align="center" 
            label="种植时间">
          </el-table-column>
          <el-table-column
            align="center" 
            label="种植作物">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.crop | cropFormat}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="yield"
            align="center" 
            label="产量(kg)">
          </el-table-column>
          <el-table-column
            align="center" 
            width="200"
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="detailsOpen(scoped.row)">详情</span>
              <span class="viewData" @click="outputReport(scoped.row)">产量填报</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div> -->
    <!-- 详情弹窗 -->
    <el-dialog
      class="detailsDialog"
      title="详情"
      :visible.sync="detailsDialog"
      width="83%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="detailsDialog=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <div>种植作物</div>
          <div class="input_item">
            <el-input class="systemFormStyle" v-model="details.cropName" disabled></el-input>
          </div>
        </div>
        <div class="handleBox_item systemFormStyle">
          <div>种植批次</div>
          <div class="input_item">
            <el-input class="systemFormStyle" v-model="details.batchId" disabled></el-input>
          </div>
        </div>
        <div class="handleBox_item systemFormStyle">
          <div>种植时间</div>
          <div class="input_item">
            <el-input class="systemFormStyle" v-model="details.plantDate" disabled></el-input>
          </div>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="detailsList"
          style="width: 100%"
          height="100%">
          <el-table-column
            prop="date"
            align="center"
            label="区域">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.areaVo.areaName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="harvestBatchId"
            align="center"
            label="收获批次">
          </el-table-column>
          <el-table-column
            prop="harvestBatchTime"
            align="center"
            label="生成批次时间">
          </el-table-column>
          <el-table-column
            prop="outputValue"
            align="center"
            label="产值(元)">
          </el-table-column>
          <el-table-column
            prop="yield"
            align="center"
            label="产量(kg)">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 生成收获批次弹窗 -->
    <el-dialog
      class="harvestBatchDialog"
      title="生成收获批次"
      :visible.sync="harvestBatchDialog"
      width="23%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="harvestBatchDialogClose('ruleForm')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        是否对当前种植批次 <span>{{harvestBatchDialogBatchId}}</span> 生成对应收获批次
      </div>
      <div class="formBox">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px" label-position="top">
          <el-form-item label="收获区域" prop="areaId">
            <div class="tips" @click="harvestAdd">
              <img src="../../../assets/image/managementSystem/add-icon.png" alt="">
            </div>
            <div class="systemFormStyle" v-for="(item,index) in ruleForm.areaId" :key="index">
              <el-select 
              v-model="ruleForm.areaId[index]" 
              placeholder="请选择收获区域" 
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in harvestAreaList"
                  :key="item.areaId"
                  :label="item.areaName"
                  :value="item.areaId">
                </el-option>
              </el-select>
              <img @click="harvestReduce(index)" class="reduce-icon" src="../../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
            </div>
          </el-form-item>
          <el-form-item label="收获时间" prop="harvestTime">
            <div class="systemFormStyle">
              <el-date-picker
                v-model="ruleForm.harvestTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="harvestBatchDialogCancel('ruleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="harvestBatchDialogSubmit('ruleForm')">确定</el-button>
          <!-- <el-button @click="harvestBatchDialogSubmit('ruleForm') tipsDialog1=true">确定</el-button> -->
        </div>
      </div>
    </el-dialog>
    <!-- 收获批次提交提示弹窗1 -->
    <el-dialog
      class="tipsDialog"
      title="提示"
      :visible.sync="tipsDialog1"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog1=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        已生成{{harvestBatchDialogAreaName}}收获批次 <span>{{harvestBatchDialogFinishId}}</span> 
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="tipsDialog1=false">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="tipsDialog1Submit">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 收获批次提交提示弹窗2 -->
    <el-dialog
      class="tipsDialog"
      title="提示"
      :visible.sync="tipsDialog2"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog2=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        是否结束当前种植批次 <span>{{harvestBatchDialogBatchId}}</span>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="tipsDialog2=false">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="tipsDialog2=false">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 生成种植批次弹窗 -->
    <el-dialog
      class="plantBatchDialog"
      title="生成种植批次"
      :visible.sync="plantBatchDialog"
      width="23%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="plantBatchDialogClose('ruleForm1')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="ruleForm1" :rules="rules1" ref="ruleForm1" label-width="80px" label-position="top">
          <el-form-item label="作物" prop="crop">
            <div class="systemFormStyle">
              <el-select 
              v-model="ruleForm1.crop" 
              placeholder="请选择作物" 
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in growCropList"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="种植区域" prop="areaId">
            <div class="tips" @click="plantBatchAdd()">
              <img src="../../../assets/image/managementSystem/add-icon.png" alt="">
            </div>
            <div class="systemFormStyle" v-for="(item,index) in ruleForm1.areaId" :key="index">
              <el-select 
              v-model="ruleForm1.areaId[index]" 
              placeholder="请选择种植区域" 
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in plantBatchArea"
                  :key="item.areaId"
                  :label="item.areaName"
                  :value="item.areaId">
                </el-option>
              </el-select>
              <img @click="plantBatchReduce(index)" class="reduce-icon" src="../../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
            </div>
          </el-form-item>
          
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="plantBatchDialogCancel('ruleForm1')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="plantBatchDialogSubmit('ruleForm1')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 已有种植批次提交提示弹窗3 -->
    <el-dialog
      class="tipsDialog tipsDialog3"
      title="提示"
      :visible.sync="tipsDialog3"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog3=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        当前种植1区已有种植批次。无法进行生成新种植批次。
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button>取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="tipsDialog3=false">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 未有种植批次提交提示弹窗4 -->
    <el-dialog
      class="tipsDialog tipsDialog4"
      title="提示"
      :visible.sync="tipsDialog4"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog4=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        <div>已生成对应种植批次</div>
        <div>批次编号： <span>{{plantBatchDialogBatchId}}</span> </div>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button>取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="tipsDialog4=false">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 结束种植批次弹窗 -->
    <el-dialog
      class="plantBatchEndDialog"
      title="结束种植批次"
      :visible.sync="plantBatchEndDialog"
      width="23%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="plantBatchEndDialogClose('ruleForm2')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="ruleForm2" :rules="rules2" ref="ruleForm2" label-width="80px" label-position="top">
          <el-form-item label="作物">
            <div class="systemFormStyle">
              <el-select 
              v-model="ruleForm2.crop" 
              placeholder="请选择作物" 
              @change="cropChange"
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in growCropList"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="种植区域">
            <div class="systemFormStyle">
              <el-select 
              v-model="ruleForm2.area" 
              placeholder="请选择种植区域" 
              @change="areaChange"
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in plantBatchEndAreaList"
                  :key="item.areaId"
                  :label="item.areaName"
                  :value="item.areaId">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="种植批次" prop="batchId">
            <div class="systemFormStyle">
              <el-input v-model="ruleForm2.batchId" placeholder="请选择种植批次" disabled class="systemFormStyle"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="plantBatchEndDialogCancel('ruleForm2')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="plantBatchEndDialogSubmit('ruleForm2')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 结束种植批次提示1 -->
    <el-dialog
      class="tipsDialog tipsDialog3"
      title="提示"
      :visible.sync="tipsDialog5"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog5=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        是否结束种植批次<span>{{ruleForm2.batchId}}</span>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="tipsDialog5=false">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="plantBatchEndCheck">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      class="tipsDialog tipsDialog3"
      title="提示"
      :visible.sync="tipsDialog6"
      width="18%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="tipsDialog6=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tipsText">
        当前种植批次 <span>{{ruleForm2.batchId}}</span>，未生成收获批次
        是否继续结束种植批次
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="tipsDialog6=false">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="plantBatchEndSubmit">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 批次历史记录弹窗 -->
    <el-dialog
      class="batchHistoryDialog"
      title="批次历史记录"
      :visible.sync="batchHistoryDialog"
      width="83%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="batchHistoryDialog=false">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="areaIdOfHistory" placeholder="请选择区域">
            <el-option
              v-for="item in areaIdList"
              :key="item.areaId"
              :label="item.areaName"
              :value="item.areaId">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="growCropsOfHistory" placeholder="请选择作物">
            <el-option
              v-for="item in growCropList"
              :key="item.value"
              :label="item.key"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="searchOfHistory">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="historyList"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            prop="date"
            align="center" 
            label="种植区域名称">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.areaVo.areaName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            align="center" 
            label="种植作物">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.crop | cropFormat}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="plantDate"
            align="center" 
            label="种植时间">
          </el-table-column>
          <el-table-column
            prop="address"
            align="center" 
            label="种植批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.batchId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            align="center" 
            label="收获批次">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.batchIdVo.finishId}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            prop="createDate"
            label="提交时间">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 产值弹窗 -->
    <el-dialog
      class="outputValueDialog"
      title="产值信息填报"
      :visible.sync="outputValueDialog"
      width="20%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="outputValueDialogClose('outputValueRuleForm')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="outputValueRuleForm" :rules="outputValueRules" ref="outputValueRuleForm" label-width="110px" label-position="left">
          <el-form-item label="种植批次信息">
            <div class="systemFormStyle">
              <el-input v-model="outputValueRuleForm.batchId" disabled class="systemFormStyle"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="收获批次">
            <div class="systemFormStyle">
              <el-select 
              v-model="outputValueRuleForm.harvestId" 
              placeholder="请选择收获批次" 
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in harvestIds"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="收货批次产值" prop="outputValue">
            <div class="systemFormStyle">
              <el-input v-model="outputValueRuleForm.outputValue" class="systemFormStyle">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  元
                </i>
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="outputValueDialogCancel('outputValueRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="outputValueDialogSubmit('outputValueRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 产量弹窗 -->
    <el-dialog
      class="yieldDialog"
      title="产量信息填报"
      :visible.sync="yieldDialog"
      width="20%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="yieldDialogClose('yieldRuleForm')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="yieldRuleForm" :rules="yieldRules" ref="yieldRuleForm" label-width="110px" label-position="left">
          <el-form-item label="种植批次信息">
            <div class="systemFormStyle">
              <el-input v-model="yieldRuleForm.batchId" disabled class="systemFormStyle"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="收获批次">
            <div class="systemFormStyle">
              <el-select 
              v-model="yieldRuleForm.harvestId" 
              placeholder="请选择收获批次" 
              popper-class="systemFormStyle">
                <el-option
                  v-for="item in harvestIds"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="收货批次产值" prop="production">
            <div class="systemFormStyle">
              <el-input v-model="yieldRuleForm.production" class="systemFormStyle">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  kg
                </i>
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="yieldDialogCancel('yieldRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="yieldDialogSubmit('yieldRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 区域产量填报 -->
    <el-dialog
      class="outputReportDialog"
      title="产量填报"
      :visible.sync="outputReportDialog"
      width="20%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="outputReportDialogClose('outputReportRuleForm')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="outputReportRuleForm" :rules="outputReportRules" ref="outputReportRuleForm" label-width="110px" label-position="left">
          <el-form-item label="产量填报" prop="production">
            <div class="systemFormStyle">
              <el-input v-model="outputReportRuleForm.production" class="systemFormStyle">
                <i
                  class=" el-input__icon"
                  slot="suffix">
                  kg
                </i>
              </el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="outputReportDialogCancel('outputReportRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="outputReportDialogSubmit('outputReportRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 更新生长阶段弹窗 -->
    <el-dialog
      class="updateGrowStageDialog"
      title="更新批次作物生长阶段"
      :visible.sync="updateGrowStageDialog"
      width="35%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="updateGrowStageDialogClose('updateGrowStageRuleForm')">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formBox">
        <el-form :model="updateGrowStageRuleForm" :rules="updateGrowStageRules" ref="updateGrowStageRuleForm" label-width="80px" label-position="top">
          <el-form-item label="选择作物" prop="crop">
            <div class="systemFormStyle">
              <el-select
              v-model="updateGrowStageRuleForm.crop"
              placeholder="请选择作物"
              @change="onCropChangeForUpdate"
              popper-class="systemFormStyle"
              style="height: 48px;">
                <el-option
                  v-for="item in growCropList"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          
          <!-- 作物信息展示区域 -->
          <div v-if="cropBatchList && cropBatchList.length > 0" class="crop-info-display">
            <div class="info-section">
              <h4 style="margin: 10px 0; color: #333; font-size: 14px;">该作物下的批次信息：</h4>
              <div class="batch-cards">
                <div 
                  v-for="(batch, index) in cropBatchList" 
                  :key="index"
                  :class="['batch-card', { 'selected': updateGrowStageRuleForm.batchId === batch.batchIdVo.batchId }]"
                  @click="selectBatch(batch)">
                  <div class="batch-header">
                    <span class="batch-title">批次: {{ batch.batchIdVo.batchId }}</span>
                    <span class="current-stage">当前阶段: {{ 农作物生长期._lableOf(batch.growStage) || '未知' }}</span>
                  </div>
                  <div class="batch-content">
                    <div class="info-row">
                      <span class="label">种植区域:</span>
                      <span class="value">
                        <span v-for="(area, aIndex) in batch.areaVoList" :key="aIndex">
                          {{ area.areaName }}<span v-if="aIndex < batch.areaVoList.length - 1">、</span>
                        </span>
                      </span>
                    </div>
                    <div class="info-row">
                      <span class="label">收获批次:</span>
                      <span class="value">{{ batch.batchIdVo.finishId || '未生成' }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">产值:</span>
                      <span class="value">{{ batch.outputValue ? batch.outputValue + '元' : '未填写' }}</span>
                    </div>
                    <div class="info-row">
                      <span class="label">产量:</span>
                      <span class="value">{{ batch.yield ? batch.yield + 'kg' : '未填写' }}</span>
                    </div>
                    <div v-if="batch.harvestIds && batch.harvestIds.length > 0" class="info-row">
                      <span class="label">收获批次号:</span>
                      <span class="value">{{ batch.harvestIds.join('、') }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <el-form-item label="新的生长阶段" prop="growStage" v-if="updateGrowStageRuleForm.batchId">
            <div class="systemFormStyle">
              <el-select
              v-model="updateGrowStageRuleForm.growStage"
              placeholder="请选择生长阶段"
              popper-class="systemFormStyle"
              style="height: 48px;">
                <el-option
                  v-for="item in growStageList"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="updateGrowStageDialogCancel('updateGrowStageRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="updateGrowStageDialogSubmit('updateGrowStageRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
import BatchService from '../../../jaxrs/concrete/com.zny.ia.api.BatchService.js';
import BatchCommonService from '../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.BatchService.js';
import AlarmThresholdService from '../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.AlarmThresholdService.js';
import 农作物种类 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
import 农作物生长期 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期.js';
import CropCaseService from '../../../jaxrs/concrete/com.zny.ia.api.CropCaseService.js';
export default {
  data(){
    return{
      administrators:"admin",//管理员：admin->总,subAdmin->分管理员
      tabActive:null,
      tabList:[],
      selectedAreaId:null, // 选择的区域ID
      growCrops:null,
      growCropList:农作物种类._toArray(),
      tableData: [],
      excelData:[],
      // 分页
      pageSize:10,
      currentPage:1,
      total:0,
      pageCount:1,
      // 详情弹窗
      detailsDialog:false,
      details:{},
      detailsList:[],
      // 生产收获批次弹窗
      harvestBatchDialog:false,
      harvestBatchDialogBatchId:'',//收获弹窗种植批次
      harvestBatchDialogFinishId:"",//收获弹窗收获批次
      harvestAreaList:[],//收获弹窗区域列表
      harvestBatchDialogAreaName:"",//收获弹窗已选择区域名称
      ruleForm:{
        areaId:[''],
        harvestTime:'',
      },
      rules:{
        areaId: [
          { required: true, message: '请选择收获区域', trigger: 'change' },
        ],
        harvestTime: [
          { required: true, message: '请选择收获时间', trigger: 'change' },
        ],
      },
      tipsDialog1:false,
      tipsDialog2:false,
      // 种植批次弹窗
      plantBatchDialog:false,
      plantBatchDialogBatchId:"",//种植批次弹窗提交生成的种植批次
      plantBatchArea:[],//种植批次弹窗可选种植区域
      ruleForm1:{
        areaId:[''],
        crop:null,
      },
      rules1:{
        areaId: [
          { required: true, message: '请选择收获区域', trigger: 'change' },
        ],
        crop: [
          { required: true, message: '请选择作物', trigger: 'change' },
        ],
      },
      tipsDialog3:false,
      tipsDialog4:false,
      // 结束种植批次弹窗
      plantBatchEndDialog:false,
      plantBatchEndAreaList:[],
      ruleForm2:{
        crop:null,
        area:'',
        batchId:"",
      },
      rules2:{
        batchId: [
          { required: true, message: '请选择种植批次', trigger: 'blur' },
        ],
      },
      tipsDialog5:false,
      tipsDialog6:false,
      //批次历史弹窗
      batchHistoryDialog:false,
      historyList:[],//批次历史列表
      areaIdOfHistory:null,
      areaIdList:[],
      growCropsOfHistory:null,
      //产值弹窗
      outputValueDialog:false,
      harvestIds:[],
      outputValueRuleForm:{
        batchId:"",
        harvestId:'',
        outputValue:"",
      },
      outputValueRules:{
        outputValue: [
          { required: true, message: '请输入收货批次产值', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
      },
      //产量弹窗
      yieldDialog:false,
      yieldRuleForm:{
        batchId:"",
        harvestId:'',
        production:"",
      },
      yieldRules:{
        production: [
          { required: true, message: '请输入收货批次产量', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
      },
      //产量填报
      outputReportDialog:false,
      outputReportRuleForm:{
        finishId:"",
        production:"",
      },
      outputReportRules:{
        production: [
          { required: true, message: '请输入产量填报', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
      },
      //更新生长阶段
      updateGrowStageDialog:false,
      updateGrowStageRuleForm:{
        crop:"",
        batchId:"",
        growStage:"",
      },
      updateGrowStageRules:{
        crop: [
          { required: true, message: '请选择作物', trigger: 'change' },
        ],
        growStage: [
          { required: true, message: '请选择生长阶段', trigger: 'change' },
        ],
      },
      growStageList:[],//生长阶段列表
      updateBatchList:[],//更新生长阶段可选的种植批次列表
      cropBatchList:[], //选择作物后获取的批次信息列表
    }
  },
  mounted(){
    // 注释掉权限判断逻辑，统一使用admin权限
    // let role = localStorage.getItem('userRoles')
    // if(role.indexOf(0) == -1){
    //   this.administrators='subAdmin'
    // }else{
    //   this.administrators='admin'
    // }
    // switch (role) {
    //   case '0':
    //     this.administrators=='admin'
    //     break;
    //   case '1':
    //   case '2':
    //     this.administrators=='subAdmin'
    //     break;
    // }
    this.getAllAreaListInformation()
  },
  methods:{
    // 获取所有区域列表
    getAllAreaListInformation(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        this.areaIdList=[...res]
        // this.tabList=[...res]
        // if(this.administrators=='admin'){
        //   this.tabList.unshift({
        //     areaId: '0', areaName: '基地总览'
        //   })
        // }
        // this.tabActive=this.tabList[0].areaId
        this.getBatchList()
      })
    },
    // tabClick(item){
    //   this.tabActive=item.areaId
    //   this.currentPage=1
    //   if(item.areaId=='0'){
    //     this.administrators='admin'
    //   }else{
    //     this.administrators='subAdmin'
    //   }
    //   this.getBatchList()
    // },
    // 查询
    search(){
      this.currentPage=1
      this.getBatchList()
    },
    // 获取批次列表数据
    getBatchList(){
      let pageRequest={
        num:this.currentPage,
        pageSize:this.pageSize,
        condition:{
          areaId:this.selectedAreaId, // 使用选择的区域ID
          growCrops:this.growCrops
        }
      }
      BatchService.batchOverviewListInformation(pageRequest)
      .then(res=>{
        this.tableData=res.list
        this.pageCount=res.count
        this.total=res.total
      })
      // if(this.administrators=='admin'){
      //   let pageRequest={
      //     num:this.currentPage,
      //     pageSize:this.pageSize,
      //     condition:{
      //       areaId:null,
      //       growCrops:this.growCrops
      //     }
      //   }
      //   BatchService.batchOverviewListInformation(pageRequest)
      //   .then(res=>{
      //     this.tableData=res.list
      //     this.pageCount=res.count
      //     this.total=res.total
      //   })
      // }else if(this.administrators=='subAdmin'){
      //   let pageRequest={
      //     num:this.currentPage,
      //     pageSize:this.pageSize,
      //     condition:{
      //       areaId:this.tabActive,
      //       growCrops:this.growCrops
      //     }
      //   }
      //   BatchService.batchAreaListInformation(pageRequest)
      //   .then(res=>{
      //     this.tableData=res.list
      //   })
      // }
    },
    handleSizeChange(){

    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.getBatchList()
    },
    // 点击显示详情弹窗
    detailsOpen(row){
      this.detailsDialog=true
      BatchService.batchInformationByBatchId(row.batchIdVo.batchId)
      .then(res=>{
        this.details=res
        this.details.cropName=农作物种类._lableOf(res.crop)
        this.detailsList=res.harvestBatchVoList
      })
    },
    // 批次历史弹窗打开
    batchHistoryOpen(){
      this.batchHistoryDialog=true
      this.getBatchHistoryList()
    },
    //更新批次生长阶段弹窗
    updateGrowStageDialogOpen(){
      this.updateGrowStageDialog = true
      // 重置表单数据
      this.updateGrowStageRuleForm = {
        crop: "",
        batchId: "",
        growStage: "",
      }
      this.updateBatchList = []
      this.growStageList = []
      this.cropBatchList = []
    },
    // 更新生长阶段弹窗中作物选择改变
    async onCropChangeForUpdate(){
      // 重置种植批次和生长阶段
      this.updateGrowStageRuleForm.batchId = ""
      this.updateGrowStageRuleForm.growStage =null
      this.cropBatchList = []
      
      if(this.updateGrowStageRuleForm.crop){
        try {
          // 调用 findBatchListByCrop 获取该作物下的批次信息
          const batchRes = await BatchCommonService.findBatchListByCrop(this.updateGrowStageRuleForm.crop)
          this.cropBatchList = batchRes || []
          
          // 获取该作物对应的生长阶段列表
          await this.getGrowStageListByCrop(this.updateGrowStageRuleForm.crop)
        } catch (error) {
          console.error('获取作物批次信息失败:', error)
          this.cropBatchList = []
          this.growStageList = []
        }
      } else {
        this.updateBatchList = []
        this.growStageList = []
        this.cropBatchList = []
      }
    },
    // 选择批次
    selectBatch(batch) {
      this.updateGrowStageRuleForm.batchId = batch.batchIdVo.batchId
      this.updateGrowStageRuleForm.growStage = null // 重置生长阶段选择
    },
    // 根据作物获取生长阶段列表
    async getGrowStageListByCrop(crop){
      try {
        // 使用系统提供的接口根据作物获取对应的生长阶段
        const stages = await AlarmThresholdService.listGrowStageByCrop(crop)
        if (stages && stages.length > 0) {
          // 接口返回的是数字数组，使用常量文件获取对应的标签
          this.growStageList = stages.map(stageValue => ({
            label: 农作物生长期._lableOf(stageValue),
            value: stageValue,
            key: 农作物生长期._lableOf(stageValue)
          }))
        } else {
          // 如果没有获取到生长阶段，设置为空数组
          this.growStageList = []
        }
      } catch (error) {
        console.error('获取生长阶段失败:', error)
        // 获取失败时设置为空数组
        this.growStageList = []
      }
    },
    // 更新批次生长阶段弹窗关闭
    updateGrowStageDialogClose(formName){
      this.$refs[formName].resetFields();
      this.updateGrowStageDialog = false
    },
    // 更新批次生长阶段弹窗取消
    updateGrowStageDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.updateGrowStageDialog = false
    },
    // 更新批次生长阶段弹窗提交
    updateGrowStageDialogSubmit(formName){
      const that = this
      
      // 验证是否选择了批次
      if (!that.updateGrowStageRuleForm.batchId) {
        that.$message.error('请先选择一个批次')
        return
      }
      
      that.$refs[formName].validate((valid) => {
        if (valid) {
          BatchCommonService.updateGrowStage(that.updateGrowStageRuleForm.batchId, that.updateGrowStageRuleForm.growStage)
          .then(() => {
            that.$message({
              message: '更新生长阶段成功',
              type: 'success'
            });
            that.updateGrowStageDialog = false
            that.$refs[formName].resetFields();
            that.getBatchList()
          })
          .catch(error => {
            console.error('更新生长阶段失败:', error)
            that.$message.error('更新生长阶段失败，请重试')
          })
        }
      })
    },
    // 历史记录弹窗查询
    searchOfHistory(){
      this.getBatchHistoryList()
    },
    // 获取批次历史列表数据
    getBatchHistoryList(){
      let pageRequest={
        num:this.currentPage,
        pageSize:this.pageSize,
        condition:{
          areaId:this.areaIdOfHistory,
          growCrops:this.growCropsOfHistory,
        }
      }
      BatchService.batchHistoryListInformation(pageRequest)
      .then(res=>{
        this.historyList=res.list
      })
    },
    // 打开生成种植批次弹窗
    plantBatchDialogOpen(){
      this.plantBatchDialog=true
      this.getPlantBatchArea()
    },
    // 获取种植批次弹窗可选种植区域
    getPlantBatchArea(){
      BatchService.generateBatchChooseArea()
      .then(res=>{
        this.plantBatchArea=res
      })
    },
    // 生成种植批次弹窗增加
    plantBatchAdd(){
      this.ruleForm1.areaId.push('')
    },
    // 生成种植批次弹窗减少
    plantBatchReduce(index){
      this.ruleForm1.areaId.splice(index,1)
    },
    // 生成种植批次弹窗关闭
    plantBatchDialogClose(formName){
      this.$refs[formName].resetFields();
      this.plantBatchDialog=false
    },
    // 生成种植批次弹窗取消
    plantBatchDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.plantBatchDialog=false
    },
    // 生成种植批次弹窗确定
    plantBatchDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let generateBatchPo={
            areaId:that.ruleForm1.areaId,
            crop:that.ruleForm1.crop
          }
          BatchService.generateBatch(generateBatchPo)
          .then(res=>{
            that.plantBatchDialogBatchId=res
            that.plantBatchDialog=false
            that.tipsDialog4=true
            that.$refs[formName].resetFields();
          })
        }
      })
    },
    // 结束种植批次弹窗->选择作物->获取种植区域
    cropChange(){
      BatchService.areaAndBatchByCorp(this.ruleForm2.crop)
      .then(res=>{
        this.plantBatchEndAreaList=res
      })
    },
    //结束种植批次弹窗-> 选择种植区域 ->拿到种植批次赋值
    areaChange(){
      this.plantBatchEndAreaList.forEach(v=>{
        if(this.ruleForm2.area==v.areaId){
          this.ruleForm2.batchId=v.batchId
        }
      })
    },
    // 结束种植批次关闭
    plantBatchEndDialogClose(formName){
      this.$refs[formName].resetFields();
      this.plantBatchEndDialog=false
    },
    // 结束种植批次取消
    plantBatchEndDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.plantBatchEndDialog=false
    },
    // 结束种植批次确定
    plantBatchEndDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          that.plantBatchEndDialog=false
          that.tipsDialog5=true
        }
      })
    },
    // 结束种植批次 提示1确定->确认当前种植批次是否有收获批次
    plantBatchEndCheck(){
      BatchService.checkHarvestBatch(this.ruleForm2.batchId)
      .then(res=>{
        this.tipsDialog5=false
        if(res){
          this.plantBatchEndSubmit()
        }else{
          this.tipsDialog6=true
        }
      })
    },
    // 结束种植批次 提示2确定->没有收获批次 并且要结束种植批次
    plantBatchEndSubmit(){
      BatchService.closedBatch(this.ruleForm2.batchId)
      .then(()=>{
        this.$message({
          message: '结束种植批次操作成功',
          type: 'success'
        });
        this.tipsDialog6=false
      })
    },
    // 打开结束种植批次弹窗
    plantBatchEndDialogOpen(){
      this.plantBatchEndDialog = true
      this.ruleForm2.crop = null
      this.ruleForm2.area = ''
      this.ruleForm2.batchId = ''
    },
    // 生成收获批次
    createHarvestBatch(row){
      this.harvestBatchDialogBatchId=row.batchIdVo.batchId
      this.harvestAreaList=row.areaVoList
      this.harvestBatchDialog=true
    },
    // 收获批次->收获批次增加
    harvestAdd(){
      this.ruleForm.areaId.push('')
    },
    // 收获批次->收获批次减少
    harvestReduce(index){
      this.ruleForm.areaId.splice(index,1)
    },
    // 收获批次弹窗关闭
    harvestBatchDialogClose(formName){
      this.$refs[formName].resetFields();
      this.harvestBatchDialog=false
    },
    // 收获批次取消
    harvestBatchDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.harvestBatchDialog=false
    },
    // 收获批次确定
    harvestBatchDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let harvestBatchPo={
            areaId:that.ruleForm.areaId,
            batchId:that.harvestBatchDialogBatchId,
            harvestTime:that.ruleForm.harvestTime
          }
          BatchService.generateHarvestBatch(harvestBatchPo)
          .then(res=>{
            that.harvestBatchDialog=false
            that.harvestBatchDialogFinishId=res
            let areaName=[]
            that.harvestAreaList.forEach(v=>{
              that.ruleForm.areaId.forEach(e=>{
                if(e==v.areaId){
                  areaName.push(v.areaName)
                }
              })
            })
            that.harvestBatchDialogAreaName=areaName.toString()
            that.tipsDialog1=true
            that.$refs[formName].resetFields();
          })
        }
      })
    },
    // 收获批次提示弹窗1确定
    tipsDialog1Submit(){
      this.tipsDialog1=false
      this.tipsDialog2=true
    },
    // 产值弹窗打开
    outputValueRepord(row){
      this.outputValueDialog=true
      this.outputValueRuleForm.batchId=row.batchIdVo.batchId
      this.harvestIds=row.harvestIds
    },
    // 产值弹窗关闭
    outputValueDialogClose(formName){
      this.$refs[formName].resetFields();
      this.outputValueDialog=false
    },
    // 产值弹窗取消
    outputValueDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.outputValueDialog=false
    },
    // 产值弹窗确定
    outputValueDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          BatchService.fillInOutputValue(this.outputValueRuleForm.harvestId,this.outputValueRuleForm.outputValue)
          .then(()=>{
            that.$refs[formName].resetFields();
            that.outputValueDialog=false
            that.$message({
              message: '产值填报完成',
              type: 'success'
            });
            that.getBatchList()
          })
        }
      })
    },
    // 产量弹窗打开
    yieldRepord(row){
      this.yieldDialog=true
      this.yieldRuleForm.batchId=row.batchIdVo.batchId
      this.harvestIds=row.harvestIds
    },
    // 产量弹窗关闭
    yieldDialogClose(formName){
      this.$refs[formName].resetFields();
      this.yieldDialog=false
    },
    // 产量弹窗取消
    yieldDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.yieldDialog=false
    },
    // 产量弹窗确定
    yieldDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          BatchService.fillInProduction(this.yieldRuleForm.harvestId,this.yieldRuleForm.production)
          .then(()=>{
            that.$refs[formName].resetFields();
            that.yieldDialog=false
            that.$message({
              message: '产量填报完成',
              type: 'success'
            });
            that.getBatchList()
          })
        }
      })
    },
    // 产量填报
    outputReport(row){
      this.outputReportRuleForm.finishId=row.batchIdVo.finishId
      this.outputReportDialog=true
    },
    // 产量填报弹窗关闭
    outputReportDialogClose(formName){
      this.$refs[formName].resetFields();
      this.outputReportDialog=false
    },
    // 产量填报弹窗取消
    outputReportDialogCancel(formName){
      this.$refs[formName].resetFields();
      this.outputReportDialog=false
    },
    // 产量填报弹窗确定
    outputReportDialogSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          BatchService.fillInProduction(that.outputReportRuleForm.finishId,that.outputReportRuleForm.production)
          .then(()=>{
            that.$refs[formName].resetFields();
            that.outputReportDialog=false
            that.$message({
              message: '产量填报完成',
              type: 'success'
            });
            that.getBatchList()
          })
        }
      })
    },
    // 下载列表
    downloadList(){
      let pageRequest={
        num:0,
        pageSize:0,
        condition:{
          areaId:this.selectedAreaId, // 使用选择的区域ID
          growCrops:this.growCrops
        }
      }
      BatchService.batchOverviewListInformation(pageRequest)
      .then(res=>{
        res.list.forEach(v=>{
          v.batchId=v.batchIdVo.batchId
          v.finishId=v.batchIdVo.finishId
          let areaName=[]
          v.areaVoList.forEach(e=>{
            areaName.push(e.areaName)
          })
          v.areaName=areaName.toString()
          v.cropName=农作物种类._lableOf(v.crop)
        })
        this.excelData=res.list
        this.export2Excel()
      })
      // if(this.administrators=='admin'){
      //   let pageRequest={
      //     num:0,
      //     pageSize:0,
      //     condition:{
      //       areaId:null,
      //       growCrops:this.growCrops
      //     }
      //   }
      //   BatchService.batchOverviewListInformation(pageRequest)
      //   .then(res=>{
      //     res.list.forEach(v=>{
      //       v.batchId=v.batchIdVo.batchId
      //       v.finishId=v.batchIdVo.finishId
      //       let areaName=[]
      //       v.areaVoList.forEach(e=>{
      //         areaName.push(e.areaName)
      //       })
      //       v.areaName=areaName.toString()
      //       v.cropName=农作物种类._lableOf(v.crop)
      //     })
      //     this.excelData=res.list
      //     this.export2Excel()
      //   })
      // }else if(this.administrators=='subAdmin'){
      //   let pageRequest={
      //     num:0,
      //     pageSize:0,
      //     condition:{
      //       areaId:this.tabActive,
      //       growCrops:this.growCrops
      //     }
      //   }
      //   BatchService.batchAreaListInformation(pageRequest)
      //   .then(res=>{
      //     res.list.forEach(v=>{
      //       v.batchId=v.batchIdVo.batchId
      //       v.finishId=v.batchIdVo.finishId
      //       v.cropName=农作物种类._lableOf(v.crop)
      //     })
      //     this.excelData=res.list
      //     this.export2Excel()
      //   })
      // }
    },
    //表格数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const {
          export_json_to_excel_productPlan
        } = require("@/excel/Export2Excel.js");
        var tHeader = ["当前种植批次","当前收获批次","种植区域","种植作物","产值(元)","产量(kg)"]
        var filterVal = [
          "batchId",
          "finishId",
          "areaName",
          "cropName",
          "outputValue",
          "yield"
        ];
        // if(this.administrators=='admin'){
        //   tHeader = ["当前种植批次","当前收获批次","种植区域","种植作物","产值(元)","产量(kg)"]
        //   filterVal = [
        //     "batchId",
        //     "finishId",
        //     "areaName",
        //     "cropName",
        //     "outputValue",
        //     "yield"
        //   ];
        // }else if(this.administrators=='subAdmin'){
        //   tHeader = ["收获批次","种植批次","收获时间","种植时间","种植作物","产量(kg)"]
        //   filterVal = [
        //     "finishId",
        //     "batchId",
        //     "harvestDate",
        //     "plantDate",
        //     "cropName",
        //     "yield"
        //   ];
        // }
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        export_json_to_excel_productPlan(tHeader, data, "批次信息数据表");
      });
    },
    //格式转换，直接复制即可,不需要修改什么
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
  },
}
</script>
<style lang="less">
@import '../../../assets/css/system/batchInforManagement.less';

// 更新生长阶段弹窗样式
.updateGrowStageDialog {
  .crop-info-display {
    margin: 15px 0;
    
    .info-section {
      h4 {
        margin: 10px 0;
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }
      
      .batch-cards {
        max-height: 300px;
        overflow-y: auto;
        
        .batch-card {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          margin-bottom: 10px;
          padding: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #fff;
          
          &:hover {
            border-color: #007eff;
            box-shadow: 0 2px 8px rgba(0, 126, 255, 0.15);
          }
          
          &.selected {
            border-color: #007eff;
            background: #f0f9ff;
            box-shadow: 0 2px 8px rgba(0, 126, 255, 0.2);
          }
          
          .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
            
            .batch-title {
              font-weight: 600;
              color: #007eff;
              font-size: 14px;
            }
            
            .current-stage {
              font-size: 12px;
              color: #666;
              background: #f5f5f5;
              padding: 2px 8px;
              border-radius: 12px;
            }
          }
          
          .batch-content {
            .info-row {
              display: flex;
              margin-bottom: 6px;
              font-size: 12px;
              
              .label {
                min-width: 70px;
                color: #666;
                font-weight: 500;
              }
              
              .value {
                color: #333;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1280px){
  .batchInforManagement{
    .harvestBatchDialog{
      .el-dialog{
        width: 33% !important;
      }
    }
    .plantBatchDialog{
      .el-dialog{
        width: 33% !important;
      }
    }
    .plantBatchEndDialog{
      .el-dialog{
        width: 30% !important;
      }
    }
    .outputValueDialog{
      .el-dialog{
        width: 26% !important;
      }
    }
    .yieldDialog{
      .el-dialog{
        width: 26% !important;
      }
    }
    .outputReportDialog{
      .el-dialog{
        width: 26% !important;
      }
    }
  } 
}
@media screen and (min-width: 1280px) and (max-width: 1680px){
  .batchInforManagement{
    .harvestBatchDialog{
      .el-dialog{
        .el-dialog__body{
          .formBox{
            .systemFormStyle{
              width: 305px;
            }
          }
        }
      }
    }
    .plantBatchDialog{
      .el-dialog{
        .el-dialog__body{
          .formBox{
            .systemFormStyle{
              width: 305px;
            }
          }
        }
      }
    }
    .plantBatchEndDialog{
      .el-dialog{
        .el-dialog__body{
          .formBox{
            .systemFormStyle{
              width: 337px;
            }
          }
        }
      }
    }
    .outputValueDialog{
      .el-dialog{
        .el-dialog__body{
          .systemFormStyle{
            width: 178px;
          }
        }
      }
    }
    .yieldDialog{
      .el-dialog{
        .el-dialog__body{
          .systemFormStyle{
            width: 178px;
          }
        }
      }
    }
    .outputReportDialog{
      .el-dialog{
        .el-dialog__body{
          .el-form-item{
            .systemFormStyle{
              width: 178px;
            }
          }
        }
      }
    }
  } 
}
</style>
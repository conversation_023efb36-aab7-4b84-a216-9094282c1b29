/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WormCaseService = {
    /**
     * 点击区域后区域名称和采集时间
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'areaInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7425382c5151283f151af6c1352b602ccf32b35a', areaId)
            : execute(concreteModuleName, `/WormCaseService/areaInfo`, 'json', 'POST', { areaId });
    }, 
    /**
     * 首页虫情数据
     * @param {*} areaId 区域id,为null是总览
     * @returns Promise 
     */
    'wormCaseData': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '526eb192cc6eda50d2272c8eb8724cfed980ffec', areaId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseData`, 'json', 'POST', { areaId });
    }, 
    /**
     * 报警汇总
     * @returns Promise 
     */
    'alarmCollect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b0641aed42b2adef2cedfb66373091242f2662c8')
            : execute(concreteModuleName, `/WormCaseService/alarmCollect`, 'json', 'GET');
    }, 
    /**
     * 虫情历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'wormCaseHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4730de646a255f0424cf4444d4490f79afb66090', po)
            : execute(concreteModuleName, `/WormCaseService/wormCaseHistory`, 'json', 'POST', po);
    }, 
    /**
     * 最近监测虫情图片
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'wormCaseRecently': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ec663037344e7bab484d5a3a45bc90940dc90dde', equipmentId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseRecently`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取虫情种类
     * @returns Promise 
     */
    'findWormCaseKind': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '770efb22c4190bfa7ee3e2df1b594aba1a33487c')
            : execute(concreteModuleName, `/WormCaseService/findWormCaseKind`, 'json', 'GET');
    }, 
    /**
     * 虫情上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'wormCasePicImport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '359f0bffcfee6b3d77caa6e8b4a03e741bda07cd', po)
            : execute(concreteModuleName, `/WormCaseService/wormCasePicImport`, 'json', 'POST', po);
    }, 
    /**
     * 总虫情种类占比
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'wormCaseProportion': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7af1bc87e9ad875ebfa5583192373c067a9f3b9d', areaId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseProportion`, 'json', 'POST', { areaId });
    }, 
    /**
     * 虫情种类柱状图
     * @param {*} areaId 区域id
     * @param {*} kinds 虫情种类集合,传null为全部
     * @returns Promise 
     */
    'wormCaseHistogram': function (areaId, kinds) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '257fb703a47dba762a0b88f70eacbd68b8fdd65e', {areaId, kinds})
            : execute(concreteModuleName, `/WormCaseService/wormCaseHistogram`, 'json', 'POST', { areaId, kinds });
    }, 
    /**
     * 虫情报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'wormCaseAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '14429a3b567d222d4f5fb12898cc1f2fa73921ff', po)
            : execute(concreteModuleName, `/WormCaseService/wormCaseAlarmRecord`, 'json', 'POST', po);
    }
}

export default WormCaseService

<template>
  <div class="traceRecordUploadDialog dialogStyle">
    <el-dialog
      :title="title"
      width="763px"
      center
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="line"></div>
      <div class="close" @click="dialogVisible=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="input_tip">
        <img src="../../assets/image/centralControlPlatform/warning-tip.png" alt="" class="input_tip_icon">
        <div class="input_tip_text">请查询溯源档案填写</div>
      </div>
      <div class="addFrom">
        <el-form ref="form" :model="ruleForm" label-width="120px">
          <el-form-item label="档案溯源码：" class="form_select formStyle">
            <el-input placeholder="请选择档案溯源码"></el-input>
          </el-form-item>
          <el-form-item label="种植批次：" class="form_select formStyle">
            <el-select v-model="ruleForm.plantBatch" popper-class="selectStyle_list" placeholder="请选择种植批次">
              <el-option
                      v-for="item in plantBatchData"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建档时间段：" class="form_select formStyle">
            <el-date-picker
                    v-model="time"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    popper-class='datePickerStyle'>
            </el-date-picker>
          </el-form-item>
          <el-form-item label="作物名称：" class="form_select formStyle">
            <el-input placeholder="请输入作物名称"></el-input>
          </el-form-item>
          <el-form-item label="选择符合条件档案：" class="form_select formStyle">
            <el-select v-model="ruleForm.area" popper-class="selectStyle_list" placeholder="请选择符合条件档案">
              <el-option
                      v-for="item in areaData"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-show="title=='生长环境图片上传档案'" prop="photoList" label="添加图片：" class="formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    list-type="picture-card"
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <img src="../../assets/image/centralControlPlatform/upload-add.png" alt="">
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='相关视频上传档案'" prop="photoList" label="相关视频上传：" class="form_upload formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <el-input readonly placeholder="点击选择视频文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='认证文件上传档案'" prop="photoList" label="农产品检测报告：" class="form_upload formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <el-input readonly placeholder="点击选择文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='认证文件上传档案'" prop="photoList" label="有机农产品认证：" class="form_upload formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <el-input readonly placeholder="点击选择文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='认证文件上传档案'" prop="photoList" label="绿色农产品认证：" class="form_upload formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <el-input readonly placeholder="点击选择文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='水质环境记录上传档案'" prop="photoList" label="水质检测报告：" class="form_upload formStyle" :class="{hide:hideUpload}">
            <el-upload
                    class="uploadCard128"
                    action=""
                    :file-list="fileList"
                    :limit="6"
                    :on-exceed="handleExceed"
                    :on-change="handleChange"
                    :auto-upload="false"
                    :on-remove="handleRemove">
              <el-input readonly placeholder="点击选择文件"></el-input>
            </el-upload>
          </el-form-item>
          <el-form-item v-show="title=='灌溉记录上传档案'" label="灌溉数据：" class="form_item2 formStyle">
            <img src="../../assets/image/centralControlPlatform/add-icon1.png" alt="" class="form_item2_add">
            <div class="form_item2_list" v-for="(item,index) in 3" :key="index">
              <div class="form_item2_list_date formStyle">
                <el-date-picker
                        v-model="time"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        popper-class='datePickerStyle'
                >
                </el-date-picker>
              </div>
              <div class="form_item2_list_input">
                <el-input placeholder="请输入灌溉量"></el-input>
              </div>
              <div class="form_item2_list_unit">m³</div>
              <img src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="form_item2_list_delete">
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="submit_button searchButtonStyle">
          <el-button>取消</el-button>
        </div>
        <div class="submit_button submitButtonStyle">
          <el-button>提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name:'traceRecordUploadDialog',
  data(){
    return{
      title:"",
      dialogVisible:false,
      area:1,
      areaData:[
        {name:'小麦种植1区',value:1},
        {name:'小麦种植2区',value:2},
        {name:'小麦种植3区',value:3},
      ],
      plantBatchData:[
        {name:'种植批次编号1',value:1},
        {name:'种植批次编号2',value:2},
        {name:'种植批次编号3',value:3},
      ],
      traceData:[
        {name:'种植1区小麦',value:1},
        {name:'种植2区小麦',value:2},
        {name:'种植3区小麦',value:3},
      ],
      ruleForm:{
        name:'',
        plantBatch:"",
        trace:'',
        imagesPath:[],
        litPicUrl:"",
        photoList:[],
      },
      fileList:[],
      hideUpload:false,
      time:'',
    }
  },
  mounted(){
    this.listen('traceRecordUploadDialogOpen', (title) => {
      this.title=title+"上传档案"
      this.dialogVisible=true
    })
  },
  methods:{
    //处理上传的文件
    updatePicProperties(fileList) {
      this.ruleForm.imagesPath = fileList.filter(f => f._type).map(f => {
        return {type: f._type, content: f._content}
      });
      this.ruleForm.litPicUrl = fileList.filter(e => !e._type).map(e => e.url).join(',')
    },
    // 移除文件
    handleRemove(files,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 6
      this.updatePicProperties(fileList)
    },
    // 上传文件个数限制
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制上传 6 张图片`);
    },
    handleChange(file,fileList){
      this.ruleForm.photoList=fileList
      this.hideUpload = fileList.length >= 9
      file._type = file.name.slice(file.name.lastIndexOf(".") + 1);
      this.getBase64(file.raw).then(res => {
        file._content = res.slice(res.indexOf(",") + 1);
        this.updatePicProperties(fileList)
      })
    },
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        if (file) {
          reader.readAsDataURL(file);
        } else {

        }
        reader.onload = function() {
          imgResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(imgResult);
        };
      });
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/traceRecordUploadDialog.less';
</style>
/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WormCaseService = {
    /**
     * 点击区域后区域名称和采集时间
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'areaInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '27e568956415cb4257e1839f7688720f89a65889', areaId)
            : execute(concreteModuleName, `/WormCaseService/areaInfo`, 'json', 'POST', { areaId });
    }, 
    /**
     * 首页虫情数据
     * @param {*} areaId 区域id,为null是总览
     * @returns Promise 
     */
    'wormCaseData': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ba7e88142e63c3068e2622d3f6885141a164b545', areaId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseData`, 'json', 'POST', { areaId });
    }, 
    /**
     * 报警汇总
     * @returns Promise 
     */
    'alarmCollect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '28968b896805f008f95d42dbfdbcb27d9143c301')
            : execute(concreteModuleName, `/WormCaseService/alarmCollect`, 'json', 'GET');
    }, 
    /**
     * 虫情历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'wormCaseHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a2db31f98a3d82433c87c910c3f8f2c0e8c66306', po)
            : execute(concreteModuleName, `/WormCaseService/wormCaseHistory`, 'json', 'POST', po);
    }, 
    /**
     * 最近监测虫情图片
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'wormCaseRecently': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1debc79e8e8967fc44e59f79019575a4d41605f6', equipmentId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseRecently`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取虫情种类
     * @returns Promise 
     */
    'findWormCaseKind': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1394fad3875b70607bd55746eab55d4fdfef35ed')
            : execute(concreteModuleName, `/WormCaseService/findWormCaseKind`, 'json', 'GET');
    }, 
    /**
     * 虫情上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'wormCasePicImport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ea807f8cd46dcc6ccd871e860f2d89aba699a9cb', po)
            : execute(concreteModuleName, `/WormCaseService/wormCasePicImport`, 'json', 'POST', po);
    }, 
    /**
     * 总虫情种类占比
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'wormCaseProportion': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5fccd44b653b4794d4723f484558bf6af1ff5c8f', areaId)
            : execute(concreteModuleName, `/WormCaseService/wormCaseProportion`, 'json', 'POST', { areaId });
    }, 
    /**
     * 虫情种类柱状图
     * @param {*} areaId 区域id
     * @param {*} kinds 虫情种类集合,传null为全部
     * @returns Promise 
     */
    'wormCaseHistogram': function (areaId, kinds) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1f86de370e2dc0cc43f282102d4e1bc6bbcbeade', {areaId, kinds})
            : execute(concreteModuleName, `/WormCaseService/wormCaseHistogram`, 'json', 'POST', { areaId, kinds });
    }, 
    /**
     * 虫情报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'wormCaseAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b8919d72a416a3015b6fe0aeef9db0142050c0c6', po)
            : execute(concreteModuleName, `/WormCaseService/wormCaseAlarmRecord`, 'json', 'POST', po);
    }
}

export default WormCaseService

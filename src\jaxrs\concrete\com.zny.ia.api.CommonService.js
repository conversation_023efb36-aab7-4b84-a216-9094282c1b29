/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const CommonService = {
    /**
     * 虫情变化趋势
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'wormCaseVariation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd39747e0f1b07d92d8e8c72f6af56cb071075ce5', areaId)
            : execute(concreteModuleName, `/CommonService/wormCaseVariation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取视频监控设备
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'listVideoEquipment': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2386b1260fb13ef54b15b3226ff5f32fe6794aa1', areaId)
            : execute(concreteModuleName, `/CommonService/listVideoEquipment`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取用户自己管理的区域的设备
     * @param {*} type 指标类型
     * @returns Promise 
     */
    'listUsersAreaEquipment': function (type) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'fb2b08c4b4c041d40e4d92b0227eeaf273ef1b9f', type)
            : execute(concreteModuleName, `/CommonService/listUsersAreaEquipment`, 'json', 'POST', { type });
    }, 
    /**
     * 根据指标类型获取该类型下的设备（不传指标类型则获取所有）
     * @param {*} type 设备类型
     * @returns Promise 
     */
    'listEquipment': function (type) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1b6937d17df7996c739879cde1c76235cd6c5726', type)
            : execute(concreteModuleName, `/CommonService/listEquipment`, 'json', 'POST', { type });
    }, 
    /**
     * 判断当前用户能否操作该区域
     * @param {*} areaId 区域id  返回能否进行操作 false-不能 true-可以
     * @returns Promise 
     */
    'areaJudge': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ef16c8b9965e1e603a01e359faaf0fbb6fb4556a', areaId)
            : execute(concreteModuleName, `/CommonService/areaJudge`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取所有区域基本信息
     * @returns Promise 
     */
    'allAreaListInformation': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ad61357b3573414f2ab6eb637c3edb956ac2b4cc')
            : execute(concreteModuleName, `/CommonService/allAreaListInformation`, 'json', 'GET');
    }, 
    /**
     * 根据报警指标项id获取报警阈值
     * @param {*} targetId 报警指标项id
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findAlarmThreshold': function (targetId, areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4d839ffd038648e455adf4256712761299d4167a', {targetId, areaId})
            : execute(concreteModuleName, `/CommonService/findAlarmThreshold`, 'json', 'POST', { targetId, areaId });
    }, 
    /**
     * 获取当前用户下管理的所有区域基本信息
     * @returns Promise 
     */
    'allAreaOfCurrentUserManage': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9f943363e39383d78f347231dded113e28e798d4')
            : execute(concreteModuleName, `/CommonService/allAreaOfCurrentUserManage`, 'json', 'GET');
    }, 
    /**
     * 设备总览
     * @returns Promise 
     */
    'findEquipmentTotal': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '082cdbad880330777c83f9eb333db258a8fe80f5')
            : execute(concreteModuleName, `/CommonService/findEquipmentTotal`, 'json', 'GET');
    }, 
    /**
     * 获取待完成生产计划的区域列表
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'listWaitAreas': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c5f93eea154df457c0aed0601a9e0d234f168594', planId)
            : execute(concreteModuleName, `/CommonService/listWaitAreas`, 'json', 'POST', { planId });
    }, 
    /**
     * 获取虫情报警的虫子
     * @returns Promise 
     */
    'listAlarmBugs': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c24db25f6c3cc92ec773f23eba8b95fe84ac6601')
            : execute(concreteModuleName, `/CommonService/listAlarmBugs`, 'json', 'GET');
    }, 
    /**
     * online
     * @returns Promise 
     */
    'online': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e815536e3fecb4922100d0c1c84b64205ec2a502')
            : execute(concreteModuleName, `/CommonService/online`, 'json', 'GET');
    }, 
    /**
     * 获取区域下的虫情设备下拉框
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'wormListByAreaId': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '371148f92b79f1c3eb906dc9257d103ceba1b95c', areaId)
            : execute(concreteModuleName, `/CommonService/wormListByAreaId`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取区域下的苗情设备下拉框
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'equipmentListByAreaId': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c7652d2197d94f3655c5771966f1d2552288c4c5', areaId)
            : execute(concreteModuleName, `/CommonService/equipmentListByAreaId`, 'json', 'POST', { areaId });
    }, 
    /**
     * 情况概况
     * @returns Promise 
     */
    'homeCaseLook': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2260f894dc83b8d807972ab861b3d221bbb9fd9a')
            : execute(concreteModuleName, `/CommonService/homeCaseLook`, 'json', 'GET');
    }, 
    /**
     * weatherCatch
     * @returns Promise 
     */
    'weatherCatch': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '80a13fac4023a995a69906a0a229496ebae98e00')
            : execute(concreteModuleName, `/CommonService/weatherCatch`, 'json', 'GET');
    }, 
    /**
     * 获取所有设备类型
     * @returns Promise 
     */
    'deviceTypeListInformation': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '604cf6726f566038faa49454d5a3123363bdf489')
            : execute(concreteModuleName, `/CommonService/deviceTypeListInformation`, 'json', 'GET');
    }
}

export default CommonService

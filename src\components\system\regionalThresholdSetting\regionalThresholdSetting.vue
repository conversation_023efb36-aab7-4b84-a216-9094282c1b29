<template>
    <div id="regionalThresholdSetting" class="productionPlan systemDialogStyle">
        <div class="regional">
            <div class="regional_left">
                <div class="regional_left_box">相关作物阈值</div>
                <div class="regional_left_table">
                    <div
                        class="regional_left_box cursor"
                        v-for="(item, index) in menuList"
                        :key="index"
                        :class="{ itemActive: activeIndex == index }"
                    >
                        <div class="thread" v-show="activeIndex == index"></div>
                        <div @click="cropClick(item.cropId, index)" class="regional_left_text">
                            {{ item.cropId | cropFormat }}
                        </div>
                        <div class="regional_left_img">
                            <!-- <img src="../../assets/image/eppoWisdom/delete.png" alt="" v-show="activeIndex !== index" /> -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="handleBox_item systemSearchButtonStyle2">
                    <el-button @click="dialogVisible = true">新 建</el-button>
                </div> -->
            <!-- 生长阶段列表 -->
            <div v-if="showGrowthStages" class="growth_stage_panel">
                <div class="growth_stage_header">生长阶段</div>
                <div class="growth_stage_list">
                    <div
                        class="growth_stage_item cursor"
                        v-for="(stage, index) in growthStageList"
                        :key="index"
                        :class="{ itemActive: selectedStageIndex == index }"
                    >
                        <div class="thread" v-show="selectedStageIndex == index"></div>
                        <div @click="selectGrowthStage(stage, index)" class="growth_stage_text">
                            {{ stage.label }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 选择了作物就显示右侧区域 -->
            <div v-if="activeIndex !== undefined" class="regional_right">
                <!-- 正常内容 -->
                <div class="regional_right_box" v-loading="loading" element-loading-text="加载中...">
                    <!-- 顶部按钮区域 -->
                    <div class="regional_right_button">
                        <div class="handleBox_item systemSearchButtonStyle2 regional_right_systemSearchButtonStyle2">
                            <el-button @click="inputDisable = false">编 辑</el-button>
                        </div>
                        <div class="handleBox_item systemSearchButtonStyle2 regional_right_systemSearchButtonStyle2">
                            <el-button @click="delDialog = true">恢复默认</el-button>
                        </div>
                    </div>

                    <!-- 可滚动的内容区域 -->
                <div class="regional_right_content_wrapper">
                     <div class="regional_right_relevance">
                        <div class="regional_right_relevance_text">当前设置:</div>
                        <div class="regional_right_relevance_text1">
                            {{ associatedCrops | cropFormat }}
                            <span v-if="selectedGrowthStage" style="color: #0093bc; margin-left: 8px;">
                                - {{ selectedGrowthStage.label }}
                            </span>
                            <span v-else style="color: #f56c6c; margin-left: 8px;">
                                - 全部生长阶段
                            </span>
                        </div>
                    </div>

                    <div class="regional_right_headline">
                        气象报警阈值:
                    </div>
                    <!-- 气象的 -->
                    <div class="regional_right_matter">
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">温度</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">高温</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[1].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(1, 1)"
                                    >
                                        <span slot="suffix">℃</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">低温</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[0].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(0, 3)"
                                    >
                                        <span slot="suffix">℃</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">湿度</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">潮湿</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[18].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(18, 1)"
                                    >
                                        <span slot="suffix">%</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">干燥</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[17].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(17, 1)"
                                    >
                                        <span slot="suffix">%</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">降雨量</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">降雨过量</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[3].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(3, 0)"
                                    >
                                        <span slot="suffix">ml/h</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">风力</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">大风报警</div>
                                <div class="right_input margin_left">
                                    <!-- <el-input
                                        v-model="regionallist[2].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                    >
                                        <span slot="suffix">级</span>
                                    </el-input> -->
                                    <div class="systemFormStyle">
                                        <el-select
                                            v-model="regionallist[2].threshold"
                                            popper-class="systemFormStyle"
                                            :disabled="inputDisable"
                                            class="systemFormStyle"
                                        >
                                            <el-option
                                                v-for="item in windLevel"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="regional_right_headline">
                        虫情报警阈值(数值内不报警):
                        <img
                            src="../../../assets/image/eppoWisdom/tianjia.png"
                            alt=""
                            @click="plantToAdd"
                            v-show="!inputDisable"
                        />
                    </div>
                    <!-- 虫情 -->
                    <div class="regional_right_content">
                        <div class="regional_right_content_row1" v-for="(item, index) in plantlsit" :key="index">
                            <div class="text" v-show="inputDisable">{{ item.thresholdCode | alarmTypeFormat }}</div>
                            <div class="systemFormStyle" v-show="!inputDisable">
                                <el-select
                                    v-model="item.thresholdCode"
                                    popper-class="systemFormStyle"
                                    @change="getFocus"
                                >
                                    <el-option
                                        v-for="item in plantDropdown"
                                        :key="item.key"
                                        :label="item.name"
                                        :value="item.key"
                                        :disabled="item.disabled"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div class="text margin_right_left">≤</div>

                            <div class="right_input margin_right">
                                <el-input
                                    v-model="item.threshold"
                                    class="systemFormStyle"
                                    :disabled="inputDisable"
                                    @input="changePricess(index)"
                                ></el-input>
                            </div>
                            <div class="text">
                                只/亩
                                <img
                                    src="../../../assets/image/eppoWisdom/jianshao.png"
                                    alt=""
                                    v-show="!inputDisable"
                                    @click="plantToDeler(index, item)"
                                />
                            </div>
                        </div>
                        <!-- <div class="regional_right_content_row1 ">
                            <div class="text">螟蛾螟蛾螟蛾</div>
                            <div class="text margin_right_left">≤</div>

                            <div class="right_input margin_right">
                                <el-input v-model="input" class="systemFormStyle" :disabled="true"></el-input>
                            </div>
                            <div class="text">
                                只/亩
                                <img src="../../assets/image/eppoWisdom/jianshao.png" alt="" />
                            </div>
                        </div> -->
                    </div>
                    <!-- 土壤 -->
                    <div class="regional_right_headline">
                        土壤报警阈值:
                    </div>
                    <div class="regional_right_matter">
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">土温</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[5].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(5, 1)"
                                    >
                                        <span slot="suffix">℃</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">过低</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[4].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(4, 3)"
                                    >
                                        <span slot="suffix">℃</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">墒情</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[7].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(7, 1)"
                                    >
                                        <span slot="suffix">%</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">过低</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[6].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(6, 1)"
                                    >
                                        <span slot="suffix">%</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">氮含量</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[12].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(12, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">过低</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[11].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(11, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="regional_right_matter">
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">磷含量</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[14].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(14, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">过低</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[13].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(13, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">钾含量</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[16].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(16, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">过低</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[15].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(15, 0)"
                                    >
                                        <span slot="suffix">mg/kg</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                        <div class="border"></div>
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">PH值</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">碱性报警</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[10].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(10, 1)"
                                    ></el-input>
                                </div>
                                <div class="regional_right_matter_box_row_text">酸性报警</div>
                                <div class="right_input margin_left">
                                    <el-input
                                        v-model="regionallist[9].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(9, 1)"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="regional_right_matter">
                        <div class="regional_right_matter_box">
                            <div class="regional_right_matter_box_header">电导率</div>
                            <div class="regional_right_matter_box_row">
                                <div class="regional_right_matter_box_row_text">过高</div>
                                <div class="right_input margin_right_left">
                                    <el-input
                                        v-model="regionallist[8].threshold"
                                        class="systemFormStyle"
                                        :disabled="inputDisable"
                                        @input="changePrice(8, 0)"
                                    >
                                        <span slot="suffix">μS/cm</span>
                                    </el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>

                    <!-- 固定在底部的按钮 -->
                    <div class="regional_right_button1" v-show="!inputDisable">
                        <div class="handleBox_item systemSearchButtonStyle2 regional_right_systemSearchButtonStyle1">
                            <el-button @click="clickCancel">取消</el-button>
                        </div>
                        <div class="handleBox_item systemSearchButtonStyle2 regional_right_systemSearchButtonStyle2">
                            <el-button @click="determine">确定</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="delDialog">
            <el-dialog
                title="提示"
                :visible.sync="delDialog"
                width="16%"
                center
                :modal-append-to-body="false"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <div class="close" @click="delDialog = false">
                    <img src="../../../assets/image/managementSystem/close.png" alt="" />
                </div>
                <div class="text">
                    <div>是否确定恢复默认？</div>
                </div>
                <div class="btnBox">
                    <div class="btnItem systemResetButtonStyle2">
                        <el-button @click="delDialog = false">取消</el-button>
                    </div>
                    <div class="btnItem systemSearchButtonStyle2">
                        <el-button @click="restoreDefault">确定</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import AlarmThresholdService from '../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.AlarmThresholdService'
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService'
import 农作物生长期 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期'
import 农作物种类 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类'
export default {
    data() {
        return {
            menuList: [],
            activeIndex: undefined,
            activeStageIndex: undefined,
            dialogVisible: false,
            ruleForm: {},
            // 生长阶段相关
            showGrowthStages: false,
            selectedGrowthStage: null,
            selectedStageIndex: null,
            growthStageList: [],
            // 加载状态
            loading: false,
            // 回显修改需要用到的字段
            regionallist: [
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 1,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 2,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 3,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 4,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 5,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 6,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 7,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 8,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 9,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 10,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 11,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 12,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 13,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 14,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 15,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 16,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 17,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 18,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 19,
                },
            ],
            // 关联作物
            associatedCrops: undefined,
            // 输入框禁用
            inputDisable: true,
            plantlsit: [],
            plantDropdown: [],
            // 用于点击取消重新赋值一下虫情
            toAssignValue: [],
            toAssignValuess: [],
            windLevel: [
                { label: '1级', value: 1 },
                { label: '2级', value: 2 },
                { label: '3级', value: 3 },
                { label: '4级', value: 4 },
                { label: '5级', value: 5 },
                { label: '6级', value: 6 },
                { label: '7级', value: 7 },
                { label: '8级', value: 8 },
                { label: '9级', value: 9 },
                { label: '10级', value: 10 },
                { label: '11级', value: 11 },
                { label: '12级', value: 12 },
                { label: '13级', value: 13 },
                { label: '14级', value: 14 },
                { label: '15级', value: 15 },
                { label: '16级', value: 16 },
                { label: '17级', value: 17 },
            ],
            meteorological: [],
            soillist: [],
            delDialog: false,
        }
    },
    created() {
        this.obtainingThreshold()
    },
    mounted() {},
    methods: {
        // 作物点击 - 新逻辑：直接显示阈值设置
        async cropClick(cropId, index) {
            this.activeIndex = index
            this.associatedCrops = cropId
            this.showGrowthStages = true
            this.selectedGrowthStage = null
            this.selectedStageIndex = null

            // 重置编辑状态
            this.inputDisable = true

            // 获取该作物的生长阶段列表
            await this.loadGrowthStages(cropId)

            // 直接加载作物的阈值数据（全部生长阶段）
            await this.loadCropThresholdData()
        },

        // 获取作物的生长阶段列表
        async loadGrowthStages(cropId) {
            try {
                const stages = await AlarmThresholdService.listGrowStageByCrop(cropId)
                if (stages && stages.length > 0) {
                    // 接口返回的是数字数组，使用常量文件获取对应的标签
                    this.growthStageList = stages.map(stageValue => ({
                        label: 农作物生长期._lableOf(stageValue),
                        value: stageValue
                    }))
                } else {
                    // 如果没有获取到生长阶段，设置为空数组
                    this.growthStageList = []
                }
            } catch (error) {
                console.error('获取生长阶段失败:', error)
                // 获取失败时设置为空数组
                this.growthStageList = []
            }
        },

        // 选择生长阶段 - 新逻辑：加载特定生长阶段的阈值
        async selectGrowthStage(stage, index) {
            this.selectedStageIndex = index
            this.selectedGrowthStage = stage

            // 重置编辑状态
            this.inputDisable = true

            // 加载特定生长阶段的阈值数据
            await this.loadStageThresholdData(stage.value)
        },

        // 加载作物阈值数据（全部生长阶段）
        async loadCropThresholdData() {
            try {
                this.loading = true
                this.resetRightSideData()

                // 使用现有接口，传入null作为生长阶段参数获取默认阈值
                console.log('加载作物阈值数据，作物ID:', this.associatedCrops, '生长阶段: 无')
                const res = await AlarmThresholdService.detailAlarmThresholdInfo(this.associatedCrops, null)
                this.processThresholdData(res)

            } catch (error) {
                console.error('获取作物阈值数据失败:', error)
                this.$message.error('获取数据失败，请重试')
            } finally {
                this.loading = false
            }
        },

        // 加载特定生长阶段阈值数据
        async loadStageThresholdData(stageValue) {
            try {
                this.loading = true
                this.resetRightSideData()

                // 使用现有接口，传入生长阶段参数获取特定阶段阶段值
                console.log('加载生长阶段阈值')
                console.log('- 作物ID:', this.associatedCrops, '(' + 农作物种类._lableOf(this.associatedCrops) + ')')
                console.log('- 生长阶段值:', stageValue, '(' + 农作物生长期._lableOf(stageValue) + ')')
                console.log('- 调用参数详情:', { cropId: this.associatedCrops, growStage: stageValue })
                console.log('- 参数类型检查 - cropId类型:', typeof this.associatedCrops, 'growStage类型:', typeof stageValue)
                const res = await AlarmThresholdService.detailAlarmThresholdInfo(this.associatedCrops, stageValue)
                this.processThresholdData(res)

            } catch (error) {
                console.error('获取生长阶段阶段值数据失败:', error)
                this.$message.error('获取数据失败，请重试')
            } finally {
                this.loading = false
            }
        },

        // 数据处理方法
        processThresholdData(res) {
            if (!res || !res.list) {
                this.resetRightSideData()
                return
            }

            // 分类处理不同类型的数据
            const meteorological = res.list.filter(item => item.type === 1)
            const plantData = res.list.filter(item => item.type === 4)
            const soilData = res.list.filter(item => item.type === 2)

            // 更新各类数据
            this.updateMeteorologicalData(meteorological)
            this.updatePlantData(plantData)
            this.updateSoilData(soilData)
        },

        // 更新气象数据
        updateMeteorologicalData(data) {
            this.meteorological = data
            data.forEach(item => {
                if (item.thresholdCode == 18) {
                    this.regionallist[17].threshold = item.threshold
                } else if (item.thresholdCode == 19) {
                    this.regionallist[18].threshold = item.threshold
                } else {
                    const index = item.thresholdCode - 1
                    if (index >= 0 && index < this.regionallist.length) {
                        this.regionallist[index].threshold = item.threshold
                    }
                }
            })
        },

        // 更新虫情数据
        updatePlantData(data) {
            this.plantlsit = [...data]
            this.toAssignValue = [...data]
            this.getFocus()
        },

        // 更新土壤数据
        updateSoilData(data) {
            this.soillist = data
            data.forEach(item => {
                const index = item.thresholdCode - 1
                if (index >= 0 && index < this.regionallist.length) {
                    this.regionallist[index].threshold = item.threshold
                }
            })
        },

        // 重置右侧数据
        resetRightSideData() {
            this.reset()
            this.plantlsit = []
            this.toAssignValue = []
            this.meteorological = []
            this.soillist = []
        },

        // 兼容方法 - 根据当前状态加载数据
        async loadThresholdSettings() {
            if (this.selectedGrowthStage) {
                await this.loadStageThresholdData(this.selectedGrowthStage.value)
            } else {
                await this.loadCropThresholdData()
            }
        },



        getFocus() {
            this.plantDropdown.forEach(item => {
                item.disabled = false
                this.plantlsit.forEach(value => {
                    if (item.key == value.thresholdCode) {
                        item.disabled = true
                    }
                })
            })
        },
        changePrice(name, value) {
            if (value == 0) {
                this.regionallist[name].threshold = this.regionallist[name].threshold.replace(/\D/g, '') //清除非数字字符
                this.this.regionallist[name].threshold = this.this.regionallist[name].threshold.replace(/\.{2,}/g, '.') //清除第二个小数点
            } else if (value == 1) {
                this.regionallist[name].threshold = this.regionallist[name].threshold
                    .replace(/[^0-9.]/g, '')
                    .replace(/([0-9]+\.[0-9]{1})[0-9]*/, '$1')
                this.regionallist[name].threshold = this.regionallist[name].threshold.replace(/\.{2,}/g, '.') //清除第二个小数点
            } else if (value == 2) {
                this.regionallist[name].threshold = this.regionallist[name].threshold
                    .replace(/[^0-9.]/g, '')
                    .replace(/([0-9]+\.[0-9]{2})[0-9]*/, '$1')
                this.regionallist[name].threshold = this.regionallist[name].threshold.replace(/\.{2,}/g, '.') //清除第二个小数点
            } else if (value == 3) {
                this.regionallist[name].threshold = this.regionallist[name].threshold
                    .replace(/-[^0-9.]/g, '')
                    .replace(/([0-9]+\.[0-9]{1})[0-9]*/, '$1')
                this.regionallist[name].threshold = this.regionallist[name].threshold.replace(/\.{2,}/g, '.') //清除第二个小数点
            }
        },
        // 虫情保留一位小数
        changePricess(name) {
            this.plantlsit[name].threshold = this.plantlsit[name].threshold
                .replace(/[^0-9.]/g, '')
                .replace(/([0-9]+\.[0-9]{1})[0-9]*/, '$1')
            this.plantlsit[name].threshold = this.plantlsit[name].threshold.replace(/\.{2,}/g, '.') //清除第二个小数点
        },
        // 通用的安全API调用方法
        async safeApiCall(apiMethod, params, errorMessage = '操作失败') {
            try {
                this.loading = true

                const result = await apiMethod(params)
                return result

            } catch (error) {
                console.error(errorMessage, error)
                this.$message.error(errorMessage)
                throw error
            } finally {
                this.loading = false
            }
        },

        // 获取阈值的作物列表 - 新增默认选择第一个作物
        async obtainingThreshold() {
            try {
                // 获取作物列表
                const cropRes = await AlarmThresholdService.listAlarmThresholdCrop()
                this.menuList = cropRes

                // 获取虫情下拉选项
                const bugsRes = await CommonService.listAlarmBugs()
                bugsRes.forEach(item => {
                    this.$set(item, 'disabled', false)
                })
                this.plantDropdown = bugsRes

                // 默认选择第一个作物
                if (this.menuList && this.menuList.length > 0) {
                    await this.cropClick(this.menuList[0].cropId, 0)
                }

            } catch (error) {
                console.error('获取初始数据失败:', error)
                this.$message.error('获取初始数据失败，请刷新页面重试')
            }
        },
        // 虫情添加
        plantToAdd() {
            this.plantlsit.push({
                threshold: undefined,
                thresholdCode: null,
                type: 4,
            })
        },
        plantToDeler(index, item) {
            this.plantDropdown.find(value => {
                if (value.key == item.thresholdCode) {
                    value.disabled = false
                }
            })
            this.plantlsit.splice(index, 1)
        },
        // 取消编辑 - 新逻辑：重新加载当前数据
        async clickCancel() {
            // 禁用编辑
            this.inputDisable = true

            // 重新加载当前选择的阈值设置
            if (this.selectedGrowthStage) {
                await this.loadStageThresholdData(this.selectedGrowthStage.value)
            } else {
                await this.loadCropThresholdData()
            }
        },
        reset() {
            this.regionallist = [
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 1,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 2,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 3,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 4,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 5,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 6,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 7,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 8,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 9,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 10,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 11,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 12,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 13,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 14,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 15,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 16,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 17,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 18,
                },
                {
                    // 阈值
                    threshold: undefined,
                    // 报警类型
                    thresholdCode: 19,
                },
            ]
        },
        // 编辑修改 - 新逻辑：区分全部生长阶段和单个生长阶段
        async determine() {
            try {
                this.loading = true

                // 准备提交数据
                this.plantlsit.forEach(res => {
                    this.regionallist.push(res)
                })

                const item = {
                    cropId: this.associatedCrops,
                    list: this.regionallist,
                }

                if (this.selectedGrowthStage) {
                    // 单个生长阶段的阈值设置，添加生长阶段参数
                    item.growStage = this.selectedGrowthStage.value
                    await AlarmThresholdService.updateAlarmThreshold(item)
                } else {
                    // 全部生长阶段的阈值设置，不传生长阶段参数
                    await AlarmThresholdService.updateAlarmThreshold(item)
                }

                this.$message({
                    message: '修改成功',
                    type: 'success',
                })

                this.inputDisable = true

                // 重新加载当前选择的阈值设置
                if (this.selectedGrowthStage) {
                    await this.loadStageThresholdData(this.selectedGrowthStage.value)
                } else {
                    await this.loadCropThresholdData()
                }

            } catch (error) {
                console.error('保存失败:', error)
                this.$message.error('保存失败，请重试')
            } finally {
                this.loading = false
            }
        },
        // 恢复默认 - 新逻辑：区分全部生长阶段和单个生长阶段
        async restoreDefault() {
            try {
                this.loading = true

                if (this.selectedGrowthStage) {
                    // 恢复单个生长阶段的默认值，传入生长阶段参数
                    await AlarmThresholdService.resetAlarmThreshold(this.associatedCrops, this.selectedGrowthStage.value)
                } else {
                    // 恢复全部生长阶段的默认值，传入null作为生长阶段参数
                    await AlarmThresholdService.resetAlarmThreshold(this.associatedCrops, null)
                }

                this.$message({
                    message: '恢复默认成功',
                    type: 'success',
                })

                // 重新加载当前选择的阈值设置
                if (this.selectedGrowthStage) {
                    await this.loadStageThresholdData(this.selectedGrowthStage.value)
                } else {
                    await this.loadCropThresholdData()
                }

                this.delDialog = false

            } catch (error) {
                console.error('恢复默认失败:', error)
                this.$message.error('恢复默认失败，请重试')
                this.delDialog = false
            } finally {
                this.loading = false
            }
        },
        // 列表点击 - 兼容方法，现在使用cropClick
        async menuClick(item, index) {
            await this.cropClick(item, index)
        },
    },
}
</script>

<style scoped lang="less">
.meteorologyEMDialog::v-deep .el-dialog {
    margin-top: 400px !important;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .systemFormStyle {
    height: 40px;
    ::v-deep .is-disabled .el-input__inner {
        background: #dfdfdf !important;
    }
}
</style>
<style lang="less">
@import '../../../assets/css/system/regionalThresholdSetting.less';
@import '../../../assets/css/system/productionPlan.less';
</style>

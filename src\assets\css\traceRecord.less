.traceRecord{
  width: 1520px;
  height: 919px;
  .title{
    height: 39px;
    line-height: 39px;
    position: absolute;
    top: 0;
    img{
      vertical-align: middle;
      margin: 0 7px 0 16px;
    }
    span{
      font-size: 16px;
      font-weight: bold;
      color: #DFEEF3;
    }
  }
  &_container{
    width: 100%;
    height: 919px;
    position: relative;
    &_title{
      width: 100%;
      background: url(../image/monitoringCenter/title5.png) no-repeat 100% center;
    }
    &_back{
      position: absolute;
      top: 50px;
      right: 11px;
      width: 60px;
      height: 60px;
      cursor: pointer;
    }
    &_con{
      width: 100%;
      height: 890px;
      position: absolute;
      bottom: 0;
      background: url(../image/centralControlPlatform/bg8.png) no-repeat 100% center;
      .handleBox{
        width: 100%;
        margin-top: 30px;
        display: flex;
        &_input{
          width: 850px;
          height: 48px;
          margin-left: 30px;
          .el-input__inner{
            border-radius: 10px!important;
          }
        }
        &_item{
          width: 124px;
          height: 48px;
          margin-left: 30px;
          .el-button{
            border-radius: 10px!important;
          }
        }
      }
      .data_list{
        width: 1428px;
        height: 719px;
        margin-top: 19px;
        margin-left: 31px;
        background: rgba(255,255,255,0);
        border: 1px solid #00F4FD;
        box-shadow: inset 0px 0px 28px 0px rgba(0,244,253,0.3);
        .searchBox{
          width: 100%;
          margin-top: 30px;
          display: flex;
          &_select{
            margin-left: 30px;
            display: flex;
            &_name{
              margin-right: 10px;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #DFEEF3;
              line-height: 36px;
            }
            &_item{
              width: 180px;
              height: 32px;
            }
            &_date{
              width: 350px;
              height: 32px;
              .el-date-editor,
              .el-range-separator{
                height: 32px !important;
                .el-input__icon {
                  line-height: 27px;
                }
              }
            }
          }
          &_item{
            width: 110px;
            height: 32px;
            margin-left: 30px;
          }
        }
        .tableContainer{
          width: 1367px;
          height: 628px;
          overflow-y: scroll;
          margin: 22px 30px 0 30px;
          display: flex;
          flex-wrap: wrap;
          .tableList{
            width: 301px;
            height: 306px;
            margin: 8px 20px;
            position: relative;
            &_item{
              width: 301px;
              height: 306px;
              position: absolute;
              top: 0;
              background: #00464C;
              &_image{
                width: 280px;
                height: 225px;
                margin: 0px 10px 0 10px;
                position: relative;
                &_con{
                  width: 100%;
                  height: 215px;
                  position: absolute;
                  top: 10px;
                  img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }
                &_name{
                  width: 280px;
                  height: 32px;
                  background: rgba(0,0,0,.4);
                  position: absolute;
                  bottom: 0;
                  font-size: 14px;
                  font-family: Microsoft YaHei;
                  font-weight: 400;
                  color: #DFEEF3;
                  line-height: 32px;
                  text-align: center;
                }
              }
              &_productName{
                width: 264px;
                margin: 12px 20px 0 20px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #DFEEF3;
              }
              &_info{
                width: 264px;
                margin: 8px 20px 0 20px;
                display: flex;
                justify-content: space-between;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #DFEEF3;
                line-height: 20px;
                .infoLeft{
                  width: 160px;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
                .infoRight{
                  width: 80px;
                }
              }
            }
            &_editor{
              width: 78px;
              height: 38px;
              background: #F79B24;
              border-radius: 19px 0px 0px 19px;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #DFEEF3;
              line-height: 38px;
              text-align: center;
              position: absolute;
              right: 0;
              top: 0px;
              cursor: pointer;
            }
            &_del{
              width: 40px;
              height: 40px;
              position: absolute;
              right: 0;
              top: 0px;
              cursor: pointer;
              img{
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }
      .pageChange{
        width: 100%;
        height: 64px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .delDialog{
    .el-dialog{
      height: 310px;
      margin-top: 320px !important;
      .el-dialog__body{
        height: 220px;
        .text{
          height: 152px;
          text-align: center;
          line-height: 152px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #FFFFFF;
        }
        .btnBox{
          width: 100%;
          height: 84px;
          display: flex;
          justify-content: center;
          align-items: center;
          .submit_button{
            width: 100px;
            height: 32px;
            .el-button{
              width: 100px;
              height: 32px;
            }
          }
          .submit_button:nth-child(1){
            margin-right: 60px;
          }
        }
      }
    }
  }
}
<template>
  <div class="baseEquipmentHistoryDialog">
    <el-dialog
      class="soilHistoryDialog"
      title="土壤历史纪录"
      :visible.sync="soilHistoryDialog"
      width="85%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="soilHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <!-- <div class="handleBox_item systemFormStyle">
          <el-select v-model="value" placeholder="请选择种植区域">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div> -->
        <div class="handleBox_item systemFormStyle">
          <el-select 
          v-model="layer" 
          clearable 
          placeholder="请选择设备层数">
            <el-option
              v-for="item in soilLayerData"
              :key="item.value"
              :label="item.key"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="from" placeholder="请选择数据来源">
            <el-option
              v-for="item in fromData"
              :key="item.value"
              :label="item.key"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(2)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            prop="areaName"
            align="center" 
            label="区域">
          </el-table-column>
          <el-table-column
            align="center" 
            label="设备层数"
            width="120">
            <template slot-scope="scoped">
              <div>
                {{ scoped.row.layer | soilLayerFormat }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="土壤温度℃">
            <template slot-scope="scoped">
              <div v-if="scoped.row.soilTemperature.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.soilTemperature.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.soilTemperature.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.soilTemperature.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.soilTemperature.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="土壤湿度%">
            <template slot-scope="scoped">
              <div v-if="scoped.row.soilHumidity.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.soilHumidity.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.soilHumidity.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.soilHumidity.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.soilHumidity.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="电导率">
            <template slot-scope="scoped">
              <div v-if="scoped.row.conductivity.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.conductivity.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.conductivity.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.conductivity.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.conductivity.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="氮含量">
            <template slot-scope="scoped">
              <div v-if="scoped.row.nitrogen.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.nitrogen.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.nitrogen.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.nitrogen.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.nitrogen.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="磷含量">
            <template slot-scope="scoped">
              <div v-if="scoped.row.phosphorus.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.phosphorus.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.phosphorus.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.phosphorus.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.phosphorus.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="钾含量">
            <template slot-scope="scoped">
              <div v-if="scoped.row.potassium.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.potassium.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.potassium.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.potassium.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.potassium.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="PH值">
            <template slot-scope="scoped">
              <div v-if="scoped.row.ph.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.ph.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.ph.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.ph.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.ph.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            align="center" 
            label="上报时间">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog
      class="InsectSituationHistoryDialog"
      title="虫情历史记录"
      :visible.sync="InsectSituationHistoryDialog"
      width="85%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="InsectSituationHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="equipmentId" placeholder="请选择监控设备">
            <el-option
              v-for="item in epData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="from" placeholder="请选择数据来源">
            <el-option
              v-for="item in fromData"
              :key="item.value"
              :label="item.key"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(4)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="时间">
            <template slot-scope="scoped">
              <div v-if="scoped.row.reportUser==''">
                {{scoped.row.createTime}}
              </div>
              <div v-else>
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="bottom">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.reportUser}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">
                    {{scoped.row.createTime}}
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="种类">
            <template slot-scope="scoped">
              <div v-if="scoped.row.reportUser==''">
                {{scoped.row.kind}}
              </div>
              <div v-else>
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="bottom">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.reportUser}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">
                    {{scoped.row.kind}}
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="数量">
            <template slot-scope="scoped">
              <div v-if="scoped.row.reportUser==''">
                {{scoped.row.count}}
              </div>
              <div v-else>
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="bottom">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.reportUser}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">
                    {{scoped.row.count}}
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="图片">
            <template slot-scope="scoped">
              <div>
                <img class="img" :src="scoped.row.picUrl" alt="">
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog
      class="meteorologicalHistoryDialog"
      title="气象历史记录"
      :visible.sync="meteorologicalHistoryDialog"
      width="95%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="meteorologicalHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <!-- <div class="handleBox_item systemFormStyle">
          <el-select v-model="value" placeholder="请选择种植区域">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div> -->
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="from" placeholder="请选择数据来源">
            <el-option
              v-for="item in fromData"
              :key="item.value"
              :label="item.key"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(1)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号"
            :resizable="false"
            align="center" 
            width="90">
          </el-table-column>
          <!-- <el-table-column
            prop="date"
            label="种植区域">
          </el-table-column> -->
          <el-table-column
            align="center"
            :resizable="false"
            label="光照度lx">
            <template slot-scope="scoped">
              <div v-if="scoped.row.illuminance.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.illuminance.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.illuminance.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.illuminance.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.illuminance.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="温度℃">
            <template slot-scope="scoped">
              <div v-if="scoped.row.temperature.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.temperature.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.temperature.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.temperature.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.temperature.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="湿度%">
            <template slot-scope="scoped">
              <div v-if="scoped.row.humidity.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.humidity.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.humidity.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.humidity.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.humidity.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="pm2.5ug/m³">
            <template slot-scope="scoped">
              <div v-if="scoped.row.pm2_5.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.pm2_5.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.pm2_5.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.pm2_5.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.pm2_5.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="风速m/s">
            <template slot-scope="scoped">
              <div v-if="scoped.row.windSpeed.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.windSpeed.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.windSpeed.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.windSpeed.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.windSpeed.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="风向">
            <template slot-scope="scoped">
              <div v-if="scoped.row.windDirection.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.windDirection.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.windDirection.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.windDirection.value | windDirectionFormat}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.windDirection.value | windDirectionFormat}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="光和有效辐射量µmol/(㎡·s)">
            <template slot-scope="scoped">
              <div v-if="scoped.row.photosyntheticRadiation.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.photosyntheticRadiation.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.photosyntheticRadiation.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.photosyntheticRadiation.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.photosyntheticRadiation.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="太阳总辐射W/㎡">
            <template slot-scope="scoped">
              <div v-if="scoped.row.solarRadiation.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.solarRadiation.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.solarRadiation.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.solarRadiation.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.solarRadiation.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="降雨量mm">
            <template slot-scope="scoped">
              <div v-if="scoped.row.precipitation.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.precipitation.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.precipitation.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.precipitation.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.precipitation.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="大气压hap">
            <template slot-scope="scoped">
              <div v-if="scoped.row.barometricPressure.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.barometricPressure.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.barometricPressure.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.barometricPressure.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.barometricPressure.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            prop="carbonDioxide"
            label="CO₂ug/m³">
            <template slot-scope="scoped">
              <div v-if="scoped.row.carbonDioxide.isArtificial">
                <el-tooltip popper-class="systemTooltipStyle" effect="light" placement="top">
                  <div slot="content">
                    <div class="item">
                      人工汇报
                    </div>
                    <br/>
                    <div class="item">
                      汇报人：{{scoped.row.carbonDioxide.loginName}}
                    </div>
                    <br/>
                    <div class="item">
                      汇报时间：{{scoped.row.carbonDioxide.reportTime}}
                    </div>
                  </div>
                  <div style="color:#18BBFF">{{scoped.row.carbonDioxide.value}}</div>
                </el-tooltip>
              </div>
              <div v-else>
                {{scoped.row.carbonDioxide.value}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="collectTime"
            label="提交时间">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog
      class="irrigationHistoryDialog"
      title="灌溉历史记录"
      :visible.sync="irrigationHistoryDialog"
      width="65%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="irrigationHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <!-- <div class="handleBox_item systemFormStyle">
          <el-select v-model="value" placeholder="请选择上传设备">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div> -->
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time2"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(3)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="种植区域">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.areaVo.areaName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            prop="irrigateTime"
            label="灌溉时间">
          </el-table-column>
          <el-table-column
            align="center" 
            prop="irrigateSum"
            label="灌溉量m³">
          </el-table-column>
          <el-table-column
            align="center" 
            prop="createTime"
            label="提交时间">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog
      class="seedlingGrowthHistoryDialog"
      title="苗情历史记录"
      :visible.sync="seedlingGrowthHistoryDialog"
      width="75%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="seedlingGrowthHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="epName" placeholder="请选择设备">
            <el-option
              v-for="item in epData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="from" placeholder="请选择数据来源">
            <el-option
              v-for="item in fromData"
              :key="item.value"
              :label="item.key"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(6)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
         <el-table-column
            prop="areaName"
            label="所属区域"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="equipmentName"
            label="当前设备"
            align="center"
            >
          </el-table-column>
          <el-table-column
            label="拍摄图片"
            align="center"
            >
            <template slot-scope="scope">
              <img :src="scope.row.picture" alt="" class="image">
            </template>
          </el-table-column>
          <el-table-column
            prop="shootTime"
            label="拍摄时间"
            align="center"
            >
          </el-table-column>
          <el-table-column
            label="相关视频"
            align="center"
            >
            <template slot-scope="scope">
              <div class="link_text" @click="openVideo(scope.row.videoAbout)">链接</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 水质历史记录 -->
    <el-dialog
      class="waterMonitorHistoryDialog"
      title="水质历史记录"
      :visible.sync="waterMonitorHistoryDialog"
      width="70%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="waterMonitorHistoryDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-select v-model="state" placeholder="请选择达标状态">
            <el-option
              v-for="item in stateData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >
            </el-option>
          </el-select>
        </div>
        <div class="handleBox_item systemFormStyle">
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class='datePickerStyle'
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search(5)">查询</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
         <el-table-column
            prop="createTime"
            label="时间"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="name"
            label="报告文件"
            align="center"
            >
          </el-table-column>
          <el-table-column
            label="达标状态"
            align="center"
            >
            <template slot-scope="scoped">
              <div v-if="scoped.row.isStandard">
                已达标
              </div>
              <div v-else>
                未达标
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="shootTime"
            label="操作"
            align="center"
            >
            <template slot-scope="scoped">
              <div class="details" @click="detailsDialogOpen(scoped.row)">
                查看详情
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 水质监测详情 -->
    <el-dialog
      class="detailsDialog"
      :title="detailsDialogTitle"
      :visible.sync="detailsDialog"
      width="25%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="detailsDialog=false">
        <img src="../../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tableBox systemTableStyle1">
        <el-table
          :data="detailsList"
          style="width: 100%"
          height="100%">
         <el-table-column
            prop="typeName"
            label="监测项"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="standard"
            label="监测标准"
            align="center"
            >
          </el-table-column>
          <el-table-column
            prop="value"
            label="监测数值"
            align="center"
            >
          </el-table-column>
        </el-table>
      </div>
      <div class="tips">
        数据来源：水质检测测报/总经理填报
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CropCaseService from "../../../../jaxrs/concrete/com.zny.ia.api.CropCaseService.js";
import WeatherService from '../../../../jaxrs/concrete/com.zny.ia.api.WeatherService.js';
import SoilService from '../../../../jaxrs/concrete/com.zny.ia.api.SoilService.js';
import WormCaseService from '../../../../jaxrs/concrete/com.zny.ia.api.WormCaseService.js';
import IrrigationService from '../../../../jaxrs/concrete/com.zny.ia.api.IrrigationService.js';
import WaterService from '../../../../jaxrs/concrete/com.zny.ia.api.WaterService.js';
import 数据来源 from '../../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$数据来源.js';
import 土壤层 from '../../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js';
import 水质检测项 from '../../../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$水质检测项.js';
export default {
  data(){
    return{
      areaId:"",
      tableData:[],
      //土壤
      soilHistoryDialog:false,
      layer:0,
      soilLayerData:土壤层._toArray(),//土壤层数据
      //虫情
      InsectSituationHistoryDialog:false,
      equipmentId:"",
      epData:[
        {label:"设备1",value:1},
      ],
      from:0,
      fromData:数据来源._toArray(),
      time:"",
      time2:"",
      //气象
      meteorologicalHistoryDialog:false,
      //灌溉
      irrigationHistoryDialog:false,
      //苗情
      seedlingGrowthHistoryDialog:false,
      epName:"",
      // 水质
      waterMonitorHistoryDialog:false,
      state:"",
      stateData:[
        {label:"正常",value:1},
        {label:"未达标",value:0},
      ],
      // 水质监测报告详情
      detailsDialog:false,
      detailsDialogTitle:'',
      detailsList:[],
    }
  },
  mounted(){
    this.listen('openbaseEquipmentHistoryDialog', data => {
      this.areaId=data.areaId
      switch(data.deviceType){
        case 1:
          this.meteorologicalHistoryDialog=true
          this.getWeatherHistory()
        break;
        case 2:
          this.soilHistoryDialog=true
          this.getListHistoryRecord()
        break;
        case 3:
          this.irrigationHistoryDialog=true
          this.getIrrigationHistoryList()
        break;
        case 4:
          this.InsectSituationHistoryDialog=true
          this.getWormCaseHistory()
        break;
        case 5:
          this.waterMonitorHistoryDialog=true
          this.getWaterMonitorHistory()
        break;
        case 6:
          this.seedlingGrowthHistoryDialog=true
          this.getCropCaseHistory()
        break;
      }
    })
  },
  methods:{
    // 气象	1	
    // 土壤	2	
    // 灌排	3	
    // 虫情	4	
    // 水质	5	
    // 苗情	6

    // 查询
    search(num){
      switch(num){
        case 1:
          this.meteorologicalHistoryDialog=true
          this.getWeatherHistory()
        break;
        case 2:
          this.soilHistoryDialog=true
          this.getListHistoryRecord()
        break;
        case 3:
          this.irrigationHistoryDialog=true
          this.getIrrigationHistoryList()
        break;
        case 4:
          this.InsectSituationHistoryDialog=true
          this.getWormCaseHistory()
        break;
        case 5:
          this.waterMonitorHistoryDialog=true
          this.getWaterMonitorHistory()
        break;
        case 6:
          this.seedlingGrowthHistoryDialog=true
          this.getCropCaseHistory()
        break;
      }
    },
    // 获取土壤历史记录列表
    getListHistoryRecord(){
      let po={
        num:1,
        pageSize:100,	
        condition:{
          areaId:this.areaId,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],	
          from:this.from,
          layer:this.layer,
        }
      }
      SoilService.listHistoryRecord(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 获取气象历史数据
    getWeatherHistory(){
      let po={
        num:1,
        pageSize:100,	
        condition:{
          areaId:this.areaId,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],	
          from:this.from,
        }
      }
      WeatherService.weatherHistory(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 获取虫情历史记录
    getWormCaseHistory(){
      let po={
        num:1,
        pageSize:1000,
        condition:{
          areaId:this.areaId,
          equipmentId:this.equipmentId,
          from:this.from,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],	
        }
      }
      WormCaseService.wormCaseHistory(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    //获取苗情历史记录列表
    getCropCaseHistory(){
      var param={
        num:1,
        pageSize:10,
        condition:{
          areaId:this.areaId,
          endTime:this.endTime,
          equipmentId:this.epName,
          from:this.fromId,
          startTime:this.startTime,
        }
      }
      CropCaseService.cropCaseHistory(param)
      .then((res)=>{
        this.tableData=res.list
      })
    },
    // 获取灌溉历史记录列表
    getIrrigationHistoryList(){
      let pageRequest={
        num:1,
        pageSize:100,	
        condition:{
          // areaId:this.areaId,
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
        }
      }
      IrrigationService.irrigationHistoryList(pageRequest)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 获取水质历史记录
    getWaterMonitorHistory(){
      let po={
        num:1,
        pageSize:100,	
        condition:{
          startTime:this.time.length==0?"":this.time[0],
          endTime:this.time.length==0?"":this.time[1],
          state:this.state==1?true:false
        }
      }
      WaterService.listWaterReport(po)
      .then(res=>{
        this.tableData=res.list
      })
    },
    // 查看水质监测报告详情
    detailsDialogOpen(row){
      this.detailsDialog=true
      this.detailsDialogTitle=row.name
      WaterService.findWaterDetailInfo(row.reportId)
      .then(res=>{
        res.waterDataList.forEach(e => {
          e.typeName=水质检测项._lableOf(e.type)
        });
        this.detailsList=res.waterDataList
      })
    },
  },
}
</script>
<style lang="less">
  @import '../../../../assets/css/system/baseEquipmentHistoryDialog.less';

</style>
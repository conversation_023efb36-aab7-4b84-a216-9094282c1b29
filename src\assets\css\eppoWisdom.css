.mask_layer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 100;
  background: rgba(208, 243, 255, 0.3);
}
.v-modal {
  background: rgba(208, 243, 255, 0.3);
}
.Dialog_contetrt_hover :nth-child(1) {
  margin-top: 0px;
}
.hover_row {
  display: flex;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  margin-top: 15px;
}
.hover_row .hover_row_col1 {
  width: 38%;
}
.el-table::before {
  height: 0px;
}
.systemTableStyle ::v-deep .el-table th.el-table__cell > .cell {
  color: #dfeef3 !important;
}
.meteorology_left_smartIOTData_con,
.soil_left_smartIOTData_con,
.InsectSituation_left_smartIOTData_con {
  height: 220px;
  top: 39px;
}
.meteorology_left_smartIOTData,
.soil_left_smartIOTData,
.InsectSituation_left_smartIOTData {
  height: 262px;
}
.meteorology_left_equipmentAlarm_con,
.soil_left_equipmentAlarm_con,
.InsectSituation_left_equipmentAlarm_con {
  width: 461px;
  height: 600px;
  position: absolute;
  bottom: 0;
  background: rgba(1, 13, 23, 0.2);
  /* background: url(img/bg2.f692130e.png) no-repeat 100% center; */
}
.district {
  width: 100%;
  box-sizing: border-box;
  padding-left: 20px;
  padding-top: 20px;
}
.district .district-label {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
}
.district .district-label-line {
  line-height: 34px;
}
.district .district-text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
}
.district .district-row2 {
  margin-top: 26px;
}
.district .district-row3 {
  margin-top: 30px;
}
.district .district-row4 {
  margin-top: 17px;
}
.prevent {
  padding-left: 20px;
  padding-right: 20px;
  box-sizing: border-box;
}
.prevent .prevent-table {
  margin-top: 30px;
  display: flex;
}
.prevent .prevent-table .prevent-button {
  width: 110px;
  height: 36px;
  background: rgba(1, 18, 21, 0);
  border: 1px solid rgba(254, 194, 48, 0.8);
  box-shadow: 0px 0px 9px 1px rgba(255, 165, 45, 0.8) inset;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  line-height: 36px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
}
.prevent .prevent-table .prevent-radius {
  border-radius: 2px 0px 0px 2px;
}
.prevent .prevent-table .prevent-radius2 {
  border-radius: 0px 2px 2px 0px;
}
.prevent .prevent-table .prevent-button-two {
  width: 110px;
  height: 36px;
  background: rgba(1, 18, 21, 0);
  border: 1px solid rgba(0, 244, 253, 0.8);
  box-shadow: 0px 0px 9px 1px rgba(0, 244, 253, 0.8) inset;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  line-height: 36px;
  box-sizing: border-box;
  text-align: center;
  opacity: 0.6;
  cursor: pointer;
}
.prevent .moreknowledge {
  margin-top: 16px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #007eff;
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
}
.prevent .content {
  overflow-y: scroll;
  height: 478px;
}
.prevent .content .content-box img {
  width: 190px;
  height: 140px;
  margin-bottom: 19px;
  object-fit: cover;
}
.prevent .content .content-box .content-box-text1 {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
}
.prevent .content .content-box .content-box-margin {
  margin-top: 18px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}
.prevent .content .content-box .content-box-text2 {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
}
.prevent .content .content-box .hide_description {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
  word-break: break-all;
  overflow: hidden;
}
.prevent .content .content-box .wire {
  width: 422px;
  height: 1px;
  margin-bottom: 22px;
  background: radial-gradient(circle, #00dfff, rgba(0, 223, 255, 0));
  margin-top: 20px;
  margin-bottom: 20px;
}
.prevent .content .content-box .more {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #007eff;
  text-align: center;
  cursor: pointer;
  margin-top: 10px;
}
.farming {
  width: 100%;
  box-sizing: border-box;
}
.farming .farming_position {
  width: 100%;
  position: absolute;
  z-index: 2;
  padding: 0px 29px;
  box-sizing: border-box;
}
.farming .eppoWisdom {
  width: 230px;
  background: url('../../assets/image/eppoWisdom/eppoWisdom2.png') no-repeat;
  background-size: 100% 100%;
  font-size: 15px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #f3fcff;
  margin-top: 30px;
  height: 28px;
  line-height: 28px;
  padding-left: 15px;
}
.farming .recentEppo {
  display: flex;
}
.farming .recentEppo :nth-child(n + 2) {
  margin-left: 10px;
}
.farming .recentEppo .recentEppo-box {
  width: 240px;
  height: 111px;
  background: url('../../assets/image/eppoWisdom/recentEppo1.png') no-repeat;
  text-align: center;
  margin-top: 18px;
}
.farming .recentEppo .recentEppo-box .recentEppo-time {
  font-size: 30px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #00fcff;
  margin-top: 13px;
}
.farming .recentEppo .recentEppo-box .recentEppo-area {
  width: 135px;
  height: 28px;
  background: linear-gradient(90deg, #006ae1, #16b7ff);
  border-radius: 4px 4px 4px 4px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  line-height: 28px;
  text-align: center;
  margin: auto;
  margin-top: 18px;
  cursor: pointer;
}
.farming .meteorology_right {
  margin: 0;
}
.farming .meteorology_right .eppoWisdom_twoDimensionalMap {
  width: 950px;
  height: 545px;
  background: url('../../assets/image/monitoringCenter/ploat.png') no-repeat;
  background-size: 100%;
  margin: 62px 0px 0px 19px;
}
.Dialog {
  position: fixed;
  right: 240px;
  top: 242px;
  width: 480px;
  z-index: 100;
  background: rgba(0, 61, 66, 0.96);
  box-shadow: 0px 0px 14px 0px rgba(0, 244, 253, 0.6) inset;
  padding-bottom: 30px;
}
.Dialog .clone {
  float: right;
  margin-top: 10px;
  margin-right: 10px;
}
.Dialog .content {
  margin-top: 26px;
  padding: 0px 21px;
}
.Dialog .content .correlation_area {
  display: flex;
  margin-top: 6px;
}
.Dialog .content .correlation_area .correlation_area_text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
}
.Dialog .content .correlation_area .correlation_area_left {
  margin-top: 14px;
}
.Dialog .content .correlation_area .correlation_area_right .right_row {
  display: flex;
  margin-top: 14px;
}
.Dialog .content .correlation_area .correlation_area_right .right_col1 {
  margin-left: 69px;
}
.Dialog .content .Dialog-text1 {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
  text-align: center;
}
.Dialog .content .Dialog-text2 {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  margin-top: 18px;
  max-height: 97px;
  overflow: auto;
}
.Dialog .content .Dialog-text3 {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  margin-top: 18px;
  text-align: center;
}
.Dialog .content .Dialog-text3 :nth-child(1) {
  margin-right: 10px;
}
.Dialog .content .Dialog-text3 :nth-child(3) {
  margin-right: 5px;
  margin-left: 5px;
}
.Dialog .content .wire {
  width: 450px;
  height: 2px;
  background: linear-gradient(90deg, rgba(0, 252, 255, 0.02), rgba(0, 252, 255, 0.96), rgba(0, 252, 255, 0));
  margin-top: 20px;
}
.Dialog .content .image {
  display: flex;
  flex-wrap: wrap;
}
.Dialog .content .image :nth-child(n + 2) {
  margin-left: 10px;
}
.Dialog .content .image :nth-child(4) {
  margin-left: 0px;
}
.Dialog .content .image img {
  width: 133px;
  height: 84px;
  border: 1px solid #00fff6;
  margin-top: 20px;
}
.meteorologyEMDialog ::v-deep .el-dialog {
  height: 669px;
  margin-top: 30vh !important;
}
.meteorologyEMDialog ::v-deep .el-dialog .el-dialog__body {
  height: 0px;
}
.meteorologyEMDialog ::v-deep .el-dialog .clone {
  position: absolute;
  top: 10px;
  right: 10px;
}
.meteorologyEMDialog ::v-deep .el-dialog .header-text {
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #dfeef3;
  position: absolute;
  top: 22px;
  left: 300px;
}
.meteorologyEMDialog ::v-deep .el-dialog .wire {
  height: 2px;
  background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
}
.meteorologyEMDialog ::v-deep .el-dialog .content {
  box-sizing: border-box;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 {
  display: flex;
  margin-top: 18px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 {
  display: flex;
  margin-top: 16px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 .topList_item:nth-child(2) {
  margin-left: 30px;
  margin-right: 20px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 .topList_item {
  display: flex;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 .topList_item .item_text {
  margin-left: 22px;
  width: 100px;
  font-weight: 400;
  text-align: center;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 .topList_item .item_text :nth-child(1) {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #70a8ff;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList1 .topList_item .item_text :nth-child(2) {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #b8d4ff;
  margin-top: 10px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList2 {
  display: flex;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList2 .topList2_item {
  width: 196px;
  height: 81px;
  background: url('../../assets/image/eppoWisdom/number.png') no-repeat;
  background-size: 100%;
  text-align: center;
  margin-left: 20px;
  padding-top: 12px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList2 .topList2_item .topList2_item_text1 {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #feffff;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row1 .topList2 .topList2_item .topList2_item_text2 {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #b8d4ff;
  margin-top: 8px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 {
  margin-top: 32px;
  display: flex;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .table .table_text {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
  margin-bottom: 19px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_wire {
  width: 1px;
  height: 383px;
  background: radial-gradient(circle, rgba(199, 206, 216, 0.8), rgba(199, 206, 216, 0));
  position: absolute;
  left: 50.6%;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right {
  width: 100%;
  margin-left: 40px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box1 {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box1 .eppoWisdom {
  width: 200px;
  background: url('../../assets/image/eppoWisdom/eppoWisdom1.png') no-repeat;
  font-size: 15px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #00fcff;
  height: 28px;
  line-height: 28px;
  padding-left: 15px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box1 .text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #007eff;
  cursor: pointer;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box2 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box2 .center1 {
  width: 297px;
  height: 94px;
  background: url('../../assets/image/eppoWisdom/zhibao1.png') no-repeat;
  background-size: 100%;
  font-size: 24px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #ffc90c;
  text-align: center;
  line-height: 94px;
  cursor: pointer;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box2 .center2 {
  width: 297px;
  height: 94px;
  background: url('../../assets/image/eppoWisdom/zhibao2.png') no-repeat;
  background-size: 100%;
  font-size: 24px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #00fcff;
  text-align: center;
  line-height: 94px;
  cursor: pointer;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box3 {
  width: 100%;
  margin-top: 25px;
  background: url('../../assets/image/eppoWisdom/center.png') no-repeat;
  padding-top: 20px;
  background-size: 100%;
  background-size: 100% 100%;
  width: 650px;
  height: 196px;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box3 .text1 {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
  text-align: center;
}
.meteorologyEMDialog ::v-deep .el-dialog .content .row2 .row2_right .row2_right_box3 .text2 {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #dfeef3;
  margin-top: 24px;
  padding: 0px 25px;
}
::v-deep .el-table th {
  text-align: center;
  color: #49c3e3;
  font-size: 16px;
  border-bottom: 0px solid #dfe6ec !important;
}
::v-deep .el-table tr,
::v-deep .el-table td {
  background-color: #003d42 !important;
}

import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 空气温度
     * desc: --
     * value: 1
     */
    空气温度: 1,
    /**
     * label: 空气湿度
     * desc: --
     * value: 2
     */
    空气湿度: 2,
    /**
     * label: 降水量
     * desc: --
     * value: 3
     */
    降水量: 3,
    /**
     * label: 风速
     * desc: --
     * value: 4
     */
    风速: 4,
    /**
     * label: 风力
     * desc: --
     * value: 5
     */
    风力: 5,
    /**
     * label: 风向
     * desc: --
     * value: 6
     */
    风向: 6,
    /**
     * label: 光照度
     * desc: --
     * value: 7
     */
    光照度: 7,
    /**
     * label: 二氧化碳浓度
     * desc: --
     * value: 8
     */
    二氧化碳浓度: 8,
    /**
     * label: 日照小时数
     * desc: --
     * value: 9
     */
    日照小时数: 9,
    /**
     * label: 太阳辐射量
     * desc: --
     * value: 10
     */
    太阳辐射量: 10,
    /**
     * label: 大气压
     * desc: --
     * value: 11
     */
    大气压: 11,
    /**
     * label: pm2_5
     * desc: --
     * value: 12
     */
    pm2_5: 12,
    /**
     * label: 土壤温度
     * desc: --
     * value: 13
     */
    土壤温度: 13,
    /**
     * label: 土壤湿度
     * desc: --
     * value: 14
     */
    土壤湿度: 14,
    /**
     * label: 电导率
     * desc: --
     * value: 15
     */
    电导率: 15,
    /**
     * label: 土壤ph值
     * desc: --
     * value: 16
     */
    土壤ph值: 16,
    /**
     * label: 氮含量
     * desc: --
     * value: 17
     */
    氮含量: 17,
    /**
     * label: 磷含量
     * desc: --
     * value: 18
     */
    磷含量: 18,
    /**
     * label: 钾含量
     * desc: --
     * value: 19
     */
    钾含量: 19,
    /**
     * label: 累计种类
     * desc: --
     * value: 20
     */
    累计种类: 20,
    /**
     * label: 累计数量
     * desc: --
     * value: 21
     */
    累计数量: 21,
    /**
     * label: 虫情密度
     * desc: --
     * value: 22
     */
    虫情密度: 22,
    /**
     * label: 水质pH值
     * desc: --
     * value: 23
     */
    水质pH值: 23,
    /**
     * label: 水温
     * desc: --
     * value: 24
     */
    水温: 24,
    /**
     * label: 悬浮物
     * desc: --
     * value: 25
     */
    悬浮物: 25,
    /**
     * label: 五日生化需氧量
     * desc: --
     * value: 26
     */
    五日生化需氧量: 26,
    /**
     * label: 化学需氧量
     * desc: --
     * value: 27
     */
    化学需氧量: 27,
    /**
     * label: 阴离子表面活性剂
     * desc: --
     * value: 28
     */
    阴离子表面活性剂: 28,
    /**
     * label: 氯化物
     * desc: --
     * value: 29
     */
    氯化物: 29,
    /**
     * label: 硫化物
     * desc: --
     * value: 30
     */
    硫化物: 30,
    /**
     * label: 全盐量
     * desc: --
     * value: 31
     */
    全盐量: 31,
    /**
     * label: 总铅
     * desc: --
     * value: 32
     */
    总铅: 32,
    /**
     * label: 总镉
     * desc: --
     * value: 33
     */
    总镉: 33,
    /**
     * label: 六价铬
     * desc: --
     * value: 34
     */
    六价铬: 34,
    /**
     * label: 总汞
     * desc: --
     * value: 35
     */
    总汞: 35,
    /**
     * label: 总砷
     * desc: --
     * value: 36
     */
    总砷: 36,
    /**
     * label: 粪大肠菌群数
     * desc: --
     * value: 37
     */
    粪大肠菌群数: 37,
    /**
     * label: 蛔虫卵数
     * desc: --
     * value: 38
     */
    蛔虫卵数: 38,
    /**
     * label: 瞬时流量
     * desc: --
     * value: 39
     */
    瞬时流量: 39,
    /**
     * label: 瞬时流速
     * desc: --
     * value: 40
     */
    瞬时流速: 40,
    /**
     * label: 生长期
     * desc: --
     * value: 41
     */
    生长期: 41,
    /**
     * label: 杂草占比
     * desc: --
     * value: 42
     */
    杂草占比: 42,
    /**
     * label: 叶色状况
     * desc: --
     * value: 43
     */
    叶色状况: 43,
    /**
     * label: 苗株长相
     * desc: --
     * value: 44
     */
    苗株长相: 44,
    /**
     * label: 作物苗高
     * desc: --
     * value: 45
     */
    作物苗高: 45,
    /**
     * label: 光合有效辐射量
     * desc: --
     * value: 46
     */
    光合有效辐射量: 46,
    /**
     * label: 控制状态
     * desc: --
     * value: 47
     */
    控制状态: 47,
    /**
     * label: 卷帘
     * desc: --
     * value: 48
     */
    卷帘: 48,
    /**
     * label: 卷膜
     * desc: --
     * value: 49
     */
    卷膜: 49,
    /**
     * label: 通风
     * desc: --
     * value: 50
     */
    通风: 50,
    /**
     * label: 上通风
     * desc: --
     * value: 51
     */
    上通风: 51,
    /**
     * label: 下通风
     * desc: --
     * value: 52
     */
    下通风: 52,
    /**
     * label: 内遮阳
     * desc: --
     * value: 53
     */
    内遮阳: 53,
    /**
     * label: 外遮阳
     * desc: --
     * value: 54
     */
    外遮阳: 54,
    /**
     * label: 水帘
     * desc: --
     * value: 55
     */
    水帘: 55,
    /**
     * label: 电磁阀
     * desc: --
     * value: 56
     */
    电磁阀: 56,
    /**
     * label: 补光灯
     * desc: --
     * value: 57
     */
    补光灯: 57,
    /**
     * label: 照明
     * desc: --
     * value: 58
     */
    照明: 58,
    /**
     * label: 备用设备
     * desc: --
     * value: 59
     */
    备用设备: 59,
    /**
     * label: 内保温
     * desc: --
     * value: 60
     */
    内保温: 60,
    /**
     * label: 外保温
     * desc: --
     * value: 61
     */
    外保温: 61,
    /**
     * label: 拖膜
     * desc: --
     * value: 62
     */
    拖膜: 62,
    /**
     * label: 水泵
     * desc: --
     * value: 63
     */
    水泵: 63,
    /**
     * label: 搅拌机
     * desc: --
     * value: 64
     */
    搅拌机: 64,
    /**
     * label: 顶开窗
     * desc: --
     * value: 65
     */
    顶开窗: 65,
    /**
     * label: 侧开窗
     * desc: --
     * value: 66
     */
    侧开窗: 66,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
import 虫情种类 from '../jaxrs/constants/com.zny.ia.constant.DeviceConstant$虫情种类.js';
import 农作物种类 from '../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
let publicMethods={
  kindFormats (msg){
    let data=虫情种类._toArray()
    let name
    data.forEach(v=>{
        if(v.value==msg){
            name=v.key
        }
    })
    return name
  },
  cropFormats (msg){
    let data=农作物种类._toArray()
    let name
    data.forEach(v=>{
        if(v.value==msg){
            name=v.key
        }
    })
    return name
  },
}
export {publicMethods}
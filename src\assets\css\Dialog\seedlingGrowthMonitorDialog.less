.seedlingGrowthMonitorDialog{
  .el-dialog{
    height: 629px;
    margin-top: 226px !important;
    .el-dialog__body{
      height: 530px;
      .line{
        width: 1256px;
        margin: auto;
      }
      .jumpPhoto{
        position: absolute;
        top: 20px;
        left: 161px;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #007EFF;
        line-height: 20px;
        cursor: pointer;
      }
      .el-select{
        width: 100%;
        height: 100%;
      }
      .handleBox{
        display: flex;
        margin-top: 30px;
        &_item{
          width: 110px;
          height: 32px;
          margin-right: 30px;
        }
        &_item2{
          width: 140px;
        }
        &_select{
          width: 180px;
          height: 32px;
          margin-right: 30px;

        }
      }
      .containerBox{
        display: flex;
        justify-content: space-between;
        margin-top: 29px;
        &_left{
          width: 556px;
          height: 441px;
          border: 2px solid;
          border-image: linear-gradient(0deg, #00E4FF, #1BFFF7) 10 10;
          .video-js{
            width: 100%;
            height: 100%;
          }
        }
        &_right{
          width: 612px;
          height: 441px;
          &_info{
            width: 100%;
            height: 277px;
            display: flex;
            justify-content: space-between;
            &_image{
              width: 360px;
              height: 277px;
              img{
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            &_con{
              width: 242px;
              .info_header{
                width: 100%;
                display: flex;
                justify-content: space-between;
                &_name{
                  width: 95px;
                  font-size: 16px;
                  font-family: Microsoft YaHei;
                  font-weight: bold;
                  color: #FFFFFF;
                  line-height: 20px;
                }
                &_button{
                  width: 110px;
                  height: 32px;
                }
              }
              .info_text{
                height: 212px;
                padding: 10px;
                border: 1px dashed #DFEEF3;
                overflow-y: scroll;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #FEFFFF;
                line-height: 22px;
                margin-top: 13px;
              }
              .info_edit{
                width: 100%;
                height: 100px;
                margin-top: 13px;
                display: flex;
                .el-textarea{
                  width: 168px;
                  height: 100%;
                  border: 1px dashed rgba(223,238,243,.6);
                  .el-textarea__inner{
                    height: 100%;
                    border: 0;
                    background-color: transparent;
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #DFEEF3;
                    line-height: 20px;
                  }
                }
              }
            }
          }
          &_imageList{
            display: flex;
            margin-top: 18px;
            &_item{
              width: 190px;
              height: 146px;
              margin-right: 14px;
              img{
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            &_item:last-child{
              margin-right: 0px;
            }
          }
        }
      }
      .monitorBox{
        height: 832px;
        overflow-y: scroll;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        &_list{
          width: 570px;
          height: 427px;
          border: 2px solid;
          border-image: linear-gradient(0deg, #00E4FF, #1BFFF7) 10 10;
          //background: linear-gradient(0deg, #00E4FF 0%, #1BFFF7 100%);
          opacity: 0.8;
          margin-top: 29px;
          .video-js{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
.seedlingGrowthMonitorDialog2{
  .el-dialog {
    height: 995px;
    margin-top: 47px !important;
    .el-dialog__body {
      height: 898px;
    }
  }
}
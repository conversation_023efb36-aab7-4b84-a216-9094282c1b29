import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 低温报警
     * desc: --
     * value: 1
     */
    低温报警: 1,
    /**
     * label: 高温报警
     * desc: --
     * value: 2
     */
    高温报警: 2,
    /**
     * label: 大风报警
     * desc: --
     * value: 3
     */
    大风报警: 3,
    /**
     * label: 降雨过多报警
     * desc: --
     * value: 4
     */
    降雨过多报警: 4,
    /**
     * label: 土温过低报警
     * desc: --
     * value: 5
     */
    土温过低报警: 5,
    /**
     * label: 土温过高报警
     * desc: --
     * value: 6
     */
    土温过高报警: 6,
    /**
     * label: 土湿过低报警
     * desc: --
     * value: 7
     */
    土湿过低报警: 7,
    /**
     * label: 土湿过高报警
     * desc: --
     * value: 8
     */
    土湿过高报警: 8,
    /**
     * label: 电导率过高报警
     * desc: --
     * value: 9
     */
    电导率过高报警: 9,
    /**
     * label: PH过低报警
     * desc: --
     * value: 10
     */
    PH过低报警: 10,
    /**
     * label: PH过高报警
     * desc: --
     * value: 11
     */
    PH过高报警: 11,
    /**
     * label: 氮浓度过低报警
     * desc: --
     * value: 12
     */
    氮浓度过低报警: 12,
    /**
     * label: 氮浓度过高报警
     * desc: --
     * value: 13
     */
    氮浓度过高报警: 13,
    /**
     * label: 磷浓度过低报警
     * desc: --
     * value: 14
     */
    磷浓度过低报警: 14,
    /**
     * label: 磷浓度过高报警
     * desc: --
     * value: 15
     */
    磷浓度过高报警: 15,
    /**
     * label: 钾浓度过低报警
     * desc: --
     * value: 16
     */
    钾浓度过低报警: 16,
    /**
     * label: 钾浓度过高报警
     * desc: --
     * value: 17
     */
    钾浓度过高报警: 17,
    /**
     * label: 空气湿度过低报警
     * desc: --
     * value: 18
     */
    空气湿度过低报警: 18,
    /**
     * label: 空气湿度过高报警
     * desc: --
     * value: 19
     */
    空气湿度过高报警: 19,
    /**
     * label: 水虿报警
     * desc: --
     * value: 20
     */
    水虿报警: 20,
    /**
     * label: 粉蝶报警
     * desc: --
     * value: 21
     */
    粉蝶报警: 21,
    /**
     * label: 蜻蜓报警
     * desc: --
     * value: 22
     */
    蜻蜓报警: 22,
    /**
     * label: 稻纵卷报警
     * desc: --
     * value: 23
     */
    稻纵卷报警: 23,
    /**
     * label: 稻飞虱报警
     * desc: --
     * value: 24
     */
    稻飞虱报警: 24,
    /**
     * label: 木虱报警
     * desc: --
     * value: 25
     */
    木虱报警: 25,
    /**
     * label: 蚂蚁报警
     * desc: --
     * value: 26
     */
    蚂蚁报警: 26,
    /**
     * label: 北京油葫芦报警
     * desc: --
     * value: 27
     */
    北京油葫芦报警: 27,
    /**
     * label: 青尺蛾报警
     * desc: --
     * value: 28
     */
    青尺蛾报警: 28,
    /**
     * label: 斑娥报警
     * desc: --
     * value: 29
     */
    斑娥报警: 29,
    /**
     * label: 斑衣蜡蝉报警
     * desc: --
     * value: 30
     */
    斑衣蜡蝉报警: 30,
    /**
     * label: 螳螂报警
     * desc: --
     * value: 31
     */
    螳螂报警: 31,
    /**
     * label: 美国白蛾报警
     * desc: --
     * value: 32
     */
    美国白蛾报警: 32,
    /**
     * label: 眉纹天蚕蛾报警
     * desc: --
     * value: 33
     */
    眉纹天蚕蛾报警: 33,
    /**
     * label: 草蜻蛉报警
     * desc: --
     * value: 34
     */
    草蜻蛉报警: 34,
    /**
     * label: 粘虫报警
     * desc: --
     * value: 35
     */
    粘虫报警: 35,
    /**
     * label: 蝶角蛉报警
     * desc: --
     * value: 36
     */
    蝶角蛉报警: 36,
    /**
     * label: 菜青虫报警
     * desc: --
     * value: 37
     */
    菜青虫报警: 37,
    /**
     * label: 油蝉报警
     * desc: --
     * value: 38
     */
    油蝉报警: 38,
    /**
     * label: 羽娥报警
     * desc: --
     * value: 39
     */
    羽娥报警: 39,
    /**
     * label: 甜菜夜蛾报警
     * desc: --
     * value: 40
     */
    甜菜夜蛾报警: 40,
    /**
     * label: 果蝇报警
     * desc: --
     * value: 41
     */
    果蝇报警: 41,
    /**
     * label: 飞蚁报警
     * desc: --
     * value: 42
     */
    飞蚁报警: 42,
    /**
     * label: 独角仙报警
     * desc: --
     * value: 43
     */
    独角仙报警: 43,
    /**
     * label: 麻步甲报警
     * desc: --
     * value: 44
     */
    麻步甲报警: 44,
    /**
     * label: 龙虱报警
     * desc: --
     * value: 45
     */
    龙虱报警: 45,
    /**
     * label: 甘薯天蛾报警
     * desc: --
     * value: 46
     */
    甘薯天蛾报警: 46,
    /**
     * label: 呼报警
     * desc: --
     * value: 47
     */
    呼报警: 47,
    /**
     * label: 烟草天蛾报警
     * desc: --
     * value: 48
     */
    烟草天蛾报警: 48,
    /**
     * label: 斑鱼蛉报警
     * desc: --
     * value: 49
     */
    斑鱼蛉报警: 49,
    /**
     * label: 石蝇报警
     * desc: --
     * value: 50
     */
    石蝇报警: 50,
    /**
     * label: 蒙古寒蝉报警
     * desc: --
     * value: 51
     */
    蒙古寒蝉报警: 51,
    /**
     * label: 虻报警
     * desc: --
     * value: 52
     */
    虻报警: 52,
    /**
     * label: 犀金龟报警
     * desc: --
     * value: 53
     */
    犀金龟报警: 53,
    /**
     * label: 苹果蠹蛾报警
     * desc: --
     * value: 54
     */
    苹果蠹蛾报警: 54,
    /**
     * label: 叩头虫报警
     * desc: --
     * value: 55
     */
    叩头虫报警: 55,
    /**
     * label: 隐翅虫报警
     * desc: --
     * value: 56
     */
    隐翅虫报警: 56,
    /**
     * label: 蓟马报警
     * desc: --
     * value: 57
     */
    蓟马报警: 57,
    /**
     * label: 蜂锰报警
     * desc: --
     * value: 58
     */
    蜂锰报警: 58,
    /**
     * label: 角蝉报警
     * desc: --
     * value: 59
     */
    角蝉报警: 59,
    /**
     * label: 锹形虫报警
     * desc: --
     * value: 60
     */
    锹形虫报警: 60,
    /**
     * label: 斗蟋报警
     * desc: --
     * value: 61
     */
    斗蟋报警: 61,
    /**
     * label: 卷叶蛾报警
     * desc: --
     * value: 62
     */
    卷叶蛾报警: 62,
    /**
     * label: 地老虎报警
     * desc: --
     * value: 63
     */
    地老虎报警: 63,
    /**
     * label: 犀牛甲虫报警
     * desc: --
     * value: 64
     */
    犀牛甲虫报警: 64,
    /**
     * label: 草铃报警
     * desc: --
     * value: 65
     */
    草铃报警: 65,
    /**
     * label: 马蛹报警
     * desc: --
     * value: 66
     */
    马蛹报警: 66,
    /**
     * label: 灯蛾报警
     * desc: --
     * value: 67
     */
    灯蛾报警: 67,
    /**
     * label: 甘薯蚁象报警
     * desc: --
     * value: 68
     */
    甘薯蚁象报警: 68,
    /**
     * label: 射炮步甲报警
     * desc: --
     * value: 69
     */
    射炮步甲报警: 69,
    /**
     * label: 舞毒蛾报警
     * desc: --
     * value: 70
     */
    舞毒蛾报警: 70,
    /**
     * label: 萝蚊报警
     * desc: --
     * value: 71
     */
    萝蚊报警: 71,
    /**
     * label: 黑蚌蝉报警
     * desc: --
     * value: 72
     */
    黑蚌蝉报警: 72,
    /**
     * label: 蚕蛾报警
     * desc: --
     * value: 73
     */
    蚕蛾报警: 73,
    /**
     * label: 稻三化螟报警
     * desc: --
     * value: 74
     */
    稻三化螟报警: 74,
    /**
     * label: 稻二化螟报警
     * desc: --
     * value: 75
     */
    稻二化螟报警: 75,
    /**
     * label: 竹节虫报警
     * desc: --
     * value: 76
     */
    竹节虫报警: 76,
    /**
     * label: 枯叶蛾报警
     * desc: --
     * value: 77
     */
    枯叶蛾报警: 77,
    /**
     * label: 长脚蚊报警
     * desc: --
     * value: 78
     */
    长脚蚊报警: 78,
    /**
     * label: 蟪蛄报警
     * desc: --
     * value: 79
     */
    蟪蛄报警: 79,
    /**
     * label: 中华婪步甲报警
     * desc: --
     * value: 80
     */
    中华婪步甲报警: 80,
    /**
     * label: 蝼蛄报警
     * desc: --
     * value: 81
     */
    蝼蛄报警: 81,
    /**
     * label: 田鳖报警
     * desc: --
     * value: 82
     */
    田鳖报警: 82,
    /**
     * label: 镬嫂报警
     * desc: --
     * value: 83
     */
    镬嫂报警: 83,
    /**
     * label: 蚂蜂报警
     * desc: --
     * value: 84
     */
    蚂蜂报警: 84,
    /**
     * label: 蝉虫报警
     * desc: --
     * value: 85
     */
    蝉虫报警: 85,
    /**
     * label: 飞虱报警
     * desc: --
     * value: 86
     */
    飞虱报警: 86,
    /**
     * label: 磕头虫报警
     * desc: --
     * value: 87
     */
    磕头虫报警: 87,
    /**
     * label: 鹿蛾报警
     * desc: --
     * value: 88
     */
    鹿蛾报警: 88,
    /**
     * label: 尺娥报警
     * desc: --
     * value: 89
     */
    尺娥报警: 89,
    /**
     * label: 蜷报警
     * desc: --
     * value: 90
     */
    蜷报警: 90,
    /**
     * label: 夜蛾报警
     * desc: --
     * value: 91
     */
    夜蛾报警: 91,
    /**
     * label: 水黾报警
     * desc: --
     * value: 92
     */
    水黾报警: 92,
    /**
     * label: 硬壳虫报警
     * desc: --
     * value: 93
     */
    硬壳虫报警: 93,
    /**
     * label: 蠹虫报警
     * desc: --
     * value: 94
     */
    蠹虫报警: 94,
    /**
     * label: 飞蚂蚁报警
     * desc: --
     * value: 95
     */
    飞蚂蚁报警: 95,
    /**
     * label: 衣蛾报警
     * desc: --
     * value: 96
     */
    衣蛾报警: 96,
    /**
     * label: 螟蛾报警
     * desc: --
     * value: 97
     */
    螟蛾报警: 97,
    /**
     * label: 瓢虫报警
     * desc: --
     * value: 98
     */
    瓢虫报警: 98,
    /**
     * label: 草蛉虫报警
     * desc: --
     * value: 99
     */
    草蛉虫报警: 99,
    /**
     * label: 金龟子报警
     * desc: --
     * value: 100
     */
    金龟子报警: 100,
    /**
     * label: 拟步甲虫报警
     * desc: --
     * value: 101
     */
    拟步甲虫报警: 101,
    /**
     * label: 蚤蝇报警
     * desc: --
     * value: 102
     */
    蚤蝇报警: 102,
    /**
     * label: 蛾蚋报警
     * desc: --
     * value: 103
     */
    蛾蚋报警: 103,
    /**
     * label: 蜜蜂报警
     * desc: --
     * value: 104
     */
    蜜蜂报警: 104,
    /**
     * label: 蠓虫报警
     * desc: --
     * value: 105
     */
    蠓虫报警: 105,
    /**
     * label: 蚊子报警
     * desc: --
     * value: 106
     */
    蚊子报警: 106,
    /**
     * label: 苍蝇报警
     * desc: --
     * value: 107
     */
    苍蝇报警: 107,
    /**
     * label: 红蜘蛛报警
     * desc: --
     * value: 108
     */
    红蜘蛛报警: 108,
    /**
     * label: 红蚁报警
     * desc: --
     * value: 109
     */
    红蚁报警: 109,
    /**
     * label: 白蚁报警
     * desc: --
     * value: 110
     */
    白蚁报警: 110,
    /**
     * label: 蝗虫报警
     * desc: --
     * value: 111
     */
    蝗虫报警: 111,
    /**
     * label: 白蛾报警
     * desc: --
     * value: 112
     */
    白蛾报警: 112,
    /**
     * label: 玉米螟报警
     * desc: --
     * value: 113
     */
    玉米螟报警: 113,
    /**
     * label: 棉铃虫报警
     * desc: --
     * value: 114
     */
    棉铃虫报警: 114,
    /**
     * label: 嫜螂报警
     * desc: --
     * value: 115
     */
    嫜螂报警: 115,
    /**
     * label: 飞蛾报警
     * desc: --
     * value: 116
     */
    飞蛾报警: 116,
    /**
     * label: 蚜虫报警
     * desc: --
     * value: 117
     */
    蚜虫报警: 117,
    /**
     * label: 介壳虫报警
     * desc: --
     * value: 118
     */
    介壳虫报警: 118,
    /**
     * label: 天牛报警
     * desc: --
     * value: 119
     */
    天牛报警: 119,
    /**
     * label: 小灰蛾报警
     * desc: --
     * value: 120
     */
    小灰蛾报警: 120,
    /**
     * label: 小白蛾报警
     * desc: --
     * value: 121
     */
    小白蛾报警: 121,
    /**
     * label: 毛毛虫报警
     * desc: --
     * value: 122
     */
    毛毛虫报警: 122,
    /**
     * label: 七星瓢虫报警
     * desc: --
     * value: 123
     */
    七星瓢虫报警: 123,
    /**
     * label: 二氧化碳过高报警
     * desc: --
     * value: 201
     */
    二氧化碳过高报警: 201,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
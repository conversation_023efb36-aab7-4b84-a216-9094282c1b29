<template>
  <div class="meteorologicalAlarmDialog dialogStyle">
    <el-dialog
      title="报警记录"
      width="68%"
      :visible.sync="dialogVisible"
      center
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
        <div class="line"></div>
        <div class="close" @click="dialogVisible=false">
          <img src="../../assets/image/centralControlPlatform/close.png" alt="">
        </div>
        <div class="handleBox">
          <div class="handleBox_item formStyle">
            <el-select
            v-model="value" 
            clearable 
            placeholder="请选择" 
            popper-class="selectStyle_list">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                >
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item formStyle">
            <el-select 
            v-model="value" 
            clearable 
            placeholder="请选择"
            popper-class="selectStyle_list">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item formStyle">
            <el-date-picker
              v-model="value1"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              popper-class='datePickerStyle'>
            </el-date-picker>
          </div>
          <div class="handleBox_item searchButtonStyle">
            <el-button>查询</el-button>
          </div>
        </div>
        <div class="tableBox tableStyle">
          <el-table
          :data="tableData"
          border
          style="width: 100%"
          height="405px">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="50">
          </el-table-column>
          <el-table-column
            prop="date"
            label="报警类型">
          </el-table-column>
          <el-table-column
            prop="name"
            label="种植区域">
          </el-table-column>
          <el-table-column
            prop="address"
            label="报警状态">
          </el-table-column>
          <el-table-column
            label="报警数值">
            <template slot-scope="scope">
              <div >
                <el-tooltip popper-class="tooltipStyle" placement="top">
                  <div slot="content">
                    <div class="item">
                      温度阈值0℃~40℃
                    </div>
                  </div>
                  <div>{{scope.row.address}}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="起始时间">
          </el-table-column>
          <el-table-column
            prop="address"
            label="结束时间">
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data(){
    return{
      dialogVisible:false,
      value:"",
      value1:"",
      options:[
        {label:"dsfjh",value:2},
        {label:"dsfjh",value:3},
        {label:"dsfjh",value:4},
        {label:"dsfjh",value:1},
      ],
      tableData: [{
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄'
        }, {
          date: '2016-05-04',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄'
        },{
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }, {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄'
        }]
    }
  },
  mounted(){
    this.listen('meteorologicalAlarmDialogOpen', () => {
      this.dialogVisible=true
    })
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/meteorologicalAlarmDialog.less';
</style>
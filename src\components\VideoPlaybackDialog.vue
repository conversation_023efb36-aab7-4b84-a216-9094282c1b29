<template>
    <div class="video-playback-dialog dialogStyle">
        <!-- 视频监控弹窗 -->
        <VideoMonitoringDialog
            :visible.sync="videoMonitoringVisible"
            :video-data="selectedVideoData"
            @go-back="handleVideoMonitoringBack"
            @go-to-realtime="handleGoToRealtime"
        />

        <el-dialog
            title="视频回放"
            width="1256px"
            :visible.sync="dialogVisible"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :modal-append-to-body="false"
            center
        >
            <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="closeDialog" />

            <div class="wire"></div>
            <div class="content">
                <!-- 主要内容区域 -->
                <div class="main-container">
                    <!-- 第一行：查询表单 -->
                    <div class="form-row">
                        <div class="form-left">
                            <div class="form-item formStyle">
                                <el-select
                                    v-model="selectedArea"
                                    placeholder="种植1区"
                                    popper-class="selectStyle_list"
                                    @change="onAreaChange"
                                >
                                    <el-option
                                        v-for="area in areaOptions"
                                        :key="area.value"
                                        :label="area.label"
                                        :value="area.value"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div class="form-item formStyle">
                                <el-select
                                    v-model="selectedDevice"
                                    placeholder="苗情监控设备1"
                                    popper-class="selectStyle_list"
                                    @change="onDeviceChange"
                                >
                                    <el-option
                                        v-for="device in deviceOptions"
                                        :key="device.value"
                                        :label="device.label"
                                        :value="device.value"
                                    ></el-option>
                                </el-select>
                            </div>
                            <div class="form-item formStyle">
                                <el-select
                                    v-model="selectedDate"
                                    placeholder="2025-07-08"
                                    popper-class="selectStyle_list"
                                    @change="onDateChange"
                                >
                                    <el-option
                                        v-for="date in dateOptions"
                                        :key="date.value"
                                        :label="date.label"
                                        :value="date.value"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="form-right">
                            <div class="realtime-btn searchButtonStyle2">
                                <el-button @click="goToRealtime">实时</el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 视频列表 -->
                    <div class="video-list">
                        <div class="list-body">
                            <div
                                class="list-item"
                                v-for="(item, index) in videoList"
                                :key="index"
                                :class="{ 'even-row': item.isEven }"
                            >
                                <div class="item-content time-column">{{ item.timeRange }}</div>
                                <div class="item-content action-column">
                                    <button class="play-btn" @click="playVideo(item)">
                                        <img src="../assets/image/monitoringCenter/videoPlay.png" alt="播放" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页组件 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        class="pageChange"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="sizes, prev, pager, next, jumper"
                        :total="total"
                        background
                    >
                    </el-pagination>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import VideoMonitoringDialog from './VideoMonitoringDialog.vue'

export default {
    name: 'VideoPlaybackDialog',
    components: {
        VideoMonitoringDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dialogVisible: false,
            selectedArea: '',
            selectedDevice: '',
            selectedDate: '',
            
            // 分页数据
            currentPage: 1,
            pageSize: 15,
            total: 120,
            
            // 下拉选项
            areaOptions: [
                { value: 'area1', label: '种植1区' },
                { value: 'area2', label: '种植2区' },
                { value: 'area3', label: '种植3区' }
            ],
            deviceOptions: [
                { value: 'device1', label: '苗情监控设备1' },
                { value: 'device2', label: '苗情监控设备2' },
                { value: 'device3', label: '苗情监控设备3' }
            ],
            dateOptions: [
                { value: '2025-07-08', label: '2025-07-08' },
                { value: '2025-07-07', label: '2025-07-07' },
                { value: '2025-07-06', label: '2025-07-06' },
                { value: '2025-07-05', label: '2025-07-05' }
            ],
            
            // 视频列表数据
            videoList: [],

            // 视频监控弹窗相关
            videoMonitoringVisible: false,
            selectedVideoData: {}
        }
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal
            if (newVal) {
                this.initData()
            }
        },
        dialogVisible(newVal) {
            this.$emit('update:visible', newVal)
        }
    },
    mounted() {
        this.generateMockData()
    },
    methods: {
        // 初始化数据
        initData() {
            this.selectedArea = 'area1'
            this.selectedDevice = 'device1'
            this.selectedDate = '2025-07-08'
            this.generateMockData()
        },
        
        // 生成模拟数据
        generateMockData() {
            this.videoList = []
            for (let i = 0; i < this.pageSize; i++) {
                const hour = String(Math.floor(Math.random() * 24)).padStart(2, '0')
                const minute = String(Math.floor(Math.random() * 60)).padStart(2, '0')
                const second = String(Math.floor(Math.random() * 60)).padStart(2, '0')

                this.videoList.push({
                    timeRange: `${hour}:${minute}:${second} - ${hour}:${minute}:${second}`,
                    startTime: `${hour}:${minute}:${second}`,
                    endTime: `${hour}:${minute}:${second}`,
                    isEven: i % 2 === 0 // 从第一行开始每隔一行变色（第1、3、5...行有背景色）
                })
            }
        },
        
        // 关闭弹窗
        closeDialog() {
            this.dialogVisible = false
        },
        
        // 返回实时监控
        goToRealtime() {
            this.dialogVisible = false
            this.$emit('go-to-realtime')
        },
        
        // 播放视频
        playVideo(item) {
            console.log('播放视频:', item)
            this.selectedVideoData = item
            this.dialogVisible = false  // 隐藏当前弹窗
            this.videoMonitoringVisible = true
        },

        // 视频监控弹窗返回
        handleVideoMonitoringBack() {
            this.videoMonitoringVisible = false
            this.dialogVisible = true  // 重新显示视频回放弹窗
        },

        // 从视频监控跳转到实时监控
        handleGoToRealtime() {
            this.videoMonitoringVisible = false
            this.dialogVisible = false
            this.$emit('go-to-realtime')
        },
        
        // 区域选择改变
        onAreaChange() {
            this.generateMockData()
        },
        
        // 设备选择改变
        onDeviceChange() {
            this.generateMockData()
        },
        
        // 日期选择改变
        onDateChange() {
            this.generateMockData()
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val
            this.generateMockData()
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val
            this.generateMockData()
        }
    }
}
</script>

<style lang="less" scoped>
// 视频回放弹窗样式
.video-playback-dialog ::v-deep .el-dialog {
    height:780px;
    margin-top: calc(50vh - 360px) !important;
    

    .el-dialog__header {
        padding: 23px 30px 23px 30px;
        text-align: left;

        .el-dialog__title {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #DFEEF3;
            position: relative;
        }
    }

    .el-dialog__body {
        height: calc(100% - 74px);
        // padding: 13px 52px 30px 52px;

        .clone {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            z-index: 10;
        }

        .wire {
            height: 2px;
            position: relative;
            top: -15px;
            background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
            margin-bottom: 20px;
        }

        .content {
            height: calc(100% - 22px);

            // 主要内容区域
            .main-container {
                padding: 0 22px;
                height: calc(100% - 60px);

                // 第一行表单样式
                .form-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 24px;
                    height: 32px;

                    .form-left {
                        display: flex;
                        align-items: center;
                        gap: 20px;

                        .form-item {
                            width: 180px;
                            height: 32px;
                        }
                    }

                    .form-right {
                        .realtime-btn {
                            width: 80px;
                            height: 32px;
                        }
                    }
                }

                // 视频列表样式
                .video-list {
                    width: 100%;
                    margin-bottom: 30px;

                    .list-body {
                        max-height: 520px; // 10条数据的高度 (52px * 10)
                        overflow-y: auto;
                        .list-item {
                            display: flex;
                            height: 52px;
                            align-items: center;
                            border-bottom: 1px solid rgba(0, 244, 253, 0.1);

                            &:last-child {
                                border-bottom: none;
                            }

                            &.even-row {
                                background: rgba(0, 244, 253, 0.06);
                            }

                            .item-content {
                                font-size: 14px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #DFEEF3;

                                &.time-column {
                                    flex: 1;
                                    text-align: left;
                                    padding-left: 20px;
                                }

                                &.action-column {
                                    width: 120px;
                                    text-align: center;

                                    .play-btn {
                                        width: 24px;
                                        height: 24px;
                                        background: transparent;
                                        border: none;
                                        cursor: pointer;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin: 0 auto;

                                        img {
                                            width: 12px;
                                            height: 12px;
                                            object-fit: contain;
                                        }

                                        &:hover {
                                            opacity: 0.8;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 分页组件样式
            .pagination-wrapper {
                display: flex;
                justify-content: center;
                margin-top: -36px;
            }
        }
    }
}

// 自定义下拉选择框文字颜色
.video-playback-dialog ::v-deep .formStyle {
    .el-input__inner {
        color: rgba(254, 255, 255, 0.6) !important;
    }

    .el-select .el-input .el-select__caret {
        color: rgba(254, 255, 255, 0.6) !important;
    }
}

// 分页组件额外样式
.video-playback-dialog ::v-deep .pageChange {
    .el-pagination__sizes {
        color: rgba(223, 238, 243, 1) !important;
    }

    .el-pagination__sizes .el-input__inner {
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        color: rgba(223,238,243) !important;
        border: 0 !important;
    }

    // 分页按钮统一样式
    .btn-prev, .btn-next {
        width: 32px !important;
        height: 32px !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
        color: rgba(223,238,243) !important;

        &:hover {
            background: rgba(0,245,255,0.1) !important;
        }

        &:disabled {
            background: rgba(0,245,255,0) !important;
            color: rgba(223,238,243,0.3) !important;
        }
    }

    // 页码按钮样式
    .el-pager li {
        width: 32px !important;
        height: 32px !important;
        background: rgba(0,245,255,0) !important;
        box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
        border-radius: 2px !important;
        border: 0 !important;
        color: rgba(223,238,243) !important;
        margin: 0 4px !important;

        &:hover {
            background: rgba(0,245,255,0.1) !important;
        }

        &.active {
            background: rgba(0,245,255,0.3) !important;
            color: #FFFFFF !important;
        }
    }

    // 跳转输入框样式
    .el-pagination__jump {
        color: rgba(223, 238, 243, 1) !important;

        .el-input__inner {
            width: 50px !important;
            height: 32px !important;
            background: rgba(0,245,255,0) !important;
            box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5) !important;
            border-radius: 2px !important;
            color: rgba(223,238,243) !important;
            border: 0 !important;
            text-align: center !important;
        }
    }

    // .el-pagination__jump::before {
    //     content: "跳至" !important;
    // }
    .el-pagination__editor.el-input{
        margin-right: 8px;
    }
}
</style>

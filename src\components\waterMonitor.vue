<template>
    <div class="InsectSituation waterMonitor">
        <div class="InsectSituation_left">
            <!-- 智慧物联 -->
            <div class="InsectSituation_left_smartIOTData">
                <div class="title InsectSituation_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>近期检测报告</span>
                </div>
                <div class="InsectSituation_left_smartIOTData_con">
                    <div class="list_con2" style="height:98%">
                        <div class="table_header_box">
                            <div class="table_header_box_row1">
                                <div class="handleBox_item formStyle">
                                    <el-select
                                        v-model="district"
                                        clearable
                                        placeholder="区域"
                                        popper-class="selectStyle_list"
                                        @change="selectionRegion(district)"
                                    >
                                        <el-option
                                            v-for="(item, index) in allcities"
                                            :key="index"
                                            :label="item.areaName"
                                            :value="item.areaId"
                                        ></el-option>
                                    </el-select>
                                </div>
                                <div class="row1_text">
                                    {{ districtlist.createTime }}
                                </div>
                            </div>
                            <div class="table_header">
                                <div class="item">检测项</div>
                                <div class="item">检测标准</div>
                                <div class="item">检测数值</div>
                            </div>
                        </div>

                        <div id="scroll_box" style="height: 88%;">
                            <vue-seamless-scroll
                                :data="districtlist.waterDataList"
                                class="seamless-warp"
                                style="width: 100%"
                                :class-option="classOption"
                            >
                                <div id="scroll1">
                                    <div
                                        class="list_item"
                                        v-for="(item, index) in districtlist.waterDataList"
                                        :key="index"
                                    >
                                        <div class="item" style="width:33%" :class="{ item_color: item.isAlarm }">
                                            {{ item.type | waterQualityDetectionFormat }}
                                        </div>
                                        <div class="item" style="width:33%" :class="{ item_color: item.isAlarm }">
                                            {{ item.standard }}
                                        </div>
                                        <div class="item" style="width:33%" :class="{ item_color: item.isAlarm }">
                                            <span v-if="item.value == 0">{{ item.value }}</span>
                                            <span v-else>{{ item.value || wire }}</span>
                                        </div>
                                    </div>
                                </div>
                            </vue-seamless-scroll>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="soil_right">
            <div class="title soil_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="soil_right_con">
                <div class="farming">
                    <div class="farming_position">
                        <div class="eppoWisdom">近期水质检测时间</div>
                        <div class="recentEppo">
                            <div class="recentEppo-box" v-for="(item, index) in waterQualitytime" :key="index">
                                <div class="recentEppo-time">{{ item.dateTime }}</div>
                                <div class="recentEppo-area" @click="checkDetails(item.id)">{{ item.areaName }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="meteorology_right">
                        <znyAMap :type="5"></znyAMap>
                    </div>
                </div>
            </div>
        </div>
        <div class="meteorologyEMDialog dialogStyle waterMonitorDialog">
            <el-dialog
                :title="title"
                width="86%"
                :visible.sync="dialogVisibles"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="dialogVisibles = false" />

                <div class="wire"></div>
                <div class="content">
                    <el-row>
                        <el-col :span="8">
                            <div class="row1_time">最近监测时间：{{ tablelist.createTime }}</div>
                            <div class="tableBox systemTableStyle table table_left">
                                <el-table
                                    :data="tablelist.waterDataList"
                                    height="680"
                                    style="width:100%"
                                    :cell-style="cellStyles"
                                    :header-cell-style="headerCellStyles"
                                >
                                    <el-table-column prop="date" label="检测项" align="center">
                                        <template slot-scope="scope">
                                            <div :class="{ item_color: scope.row.isAlarm }">
                                                {{ scope.row.type | waterQualityDetectionFormat }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="name" label="检测标准" align="center">
                                        <template slot-scope="scope">
                                            <div :class="{ item_color: scope.row.isAlarm }">
                                                {{ scope.row.standard }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="检测数值" align="center">
                                        <template slot-scope="scope">
                                            <div :class="{ item_color: scope.row.isAlarm }">
                                                <span v-if="scope.row.value == 0">{{ scope.row.value }}</span>
                                                <span v-else>{{ scope.row.value || wire }}</span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-col>
                        <el-col :span="1">
                            <div class="row_wire"></div>
                        </el-col>
                        <el-col :span="15">
                            <div class="handleBox" style="margin-top:0px">
                                <div class="handleBox_item formStyle">
                                    <div class="status">时间：</div>
                                    <el-date-picker
                                        v-model="payTime"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        style="width:87%"
                                        popper-class="datePickerStyle"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        @change="GetzhifuTime"
                                    ></el-date-picker>
                                </div>
                                <div class="handleBox_item formStyle">
                                    <div class="status">运行状态：</div>
                                    <el-select
                                        v-model="querlist.condition.state"
                                        clearable
                                        popper-class="selectStyle_list"
                                        style="width:64%"
                                    >
                                        <el-option
                                            v-for="(item, index) in runningState"
                                            :key="index"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </div>
                                <div class="handleBox_item searchButtonStyle">
                                    <el-button @click="testReportList" style="width:110px">查询</el-button>
                                </div>
                                <div class="handleBox_item searchButtonStyle" v-if="areaIsJudge">
                                    <el-button @click="report" style="width:110px">汇报</el-button>
                                </div>
                            </div>
                            <div class="tableBox systemTableStyle table">
                                <el-table
                                    :data="waterQualityInspectionList"
                                    height="590"
                                    style="width:100%"
                                    :cell-style="cellStyle"
                                    :header-cell-style="headerCellStyle"
                                >
                                    <el-table-column
                                        type="index"
                                        label="序号"
                                        align="center"
                                        width="80"
                                    ></el-table-column>
                                    <el-table-column prop="createTime" label="时间" align="center"></el-table-column>
                                    <el-table-column prop="name" label="报告文件" align="center">
                                        <template slot-scope="scope">
                                            <div v-show="scope.row.sourceType == 0">
                                                {{ scope.row.name }}
                                            </div>
                                            <div v-show="scope.row.sourceType == 1">
                                                人工汇报
                                            </div>
                                            <div v-show="scope.row.sourceType == 2">
                                                上传文件
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="num" label="达标状态" align="center">
                                        <template slot-scope="scope">
                                            <div v-if="scope.row.isStandard == true">已达标</div>
                                            <div v-else-if="scope.row.isStandard == false">未达标</div>
                                            <div v-else>——</div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="address" label="操作" align="center">
                                        <template slot-scope="scope">
                                            <el-button type="text" @click="checkDetails(scope.row.reportId)">
                                                查看详情
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <div class="pageChange">
                                    <el-pagination
                                        @current-change="handleCurrentChange"
                                        :current-page="querlist.num"
                                        :page-size="querlist.pageSize"
                                        background
                                        layout="prev, pager, next"
                                        :total="total"
                                        :pager-count="9"
                                    ></el-pagination>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-dialog>
        </div>
        <div class="meteorologyEMDialog dialogStyle ">
            <el-dialog
                :title="regionaltilte"
                width="31%"
                :visible.sync="waterdialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
                class="dialog"
            >
                <img
                    src="../assets/image/eppoWisdom/close.png"
                    alt=""
                    class="clone"
                    @click="waterdialogVisible = false"
                />

                <div class="wire"></div>
                <div class="content">
                    <div class="tableBox systemTableStyle table table_right" v-if="reportType !== 2">
                        <el-table
                            :data="particularslist"
                            height="850"
                            style="width:100%"
                            :cell-style="cellStyle"
                            :header-cell-style="cellStyle"
                        >
                            <el-table-column prop="date" label="检测项" align="center">
                                <template slot-scope="scope">
                                    <div :class="{ item_color: scope.row.isAlarm }">
                                        {{ scope.row.type | waterQualityDetectionFormat }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="standard" label="检测标准" align="center">
                                <template slot-scope="scope">
                                    <div :class="{ item_color: scope.row.isAlarm }">
                                        {{ scope.row.standard }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="value" label="检测数值" align="center">
                                <template slot-scope="scope">
                                    <div :class="{ item_color: scope.row.isAlarm }">
                                        <span v-if="scope.row.value == 0">{{ scope.row.value }}</span>
                                        <span v-else>{{ scope.row.value || wire }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div class="centre_img_box" v-if="reportType == 2">
                        <img :src="item" alt="" v-for="(item, index) in waterimg" :key="index" />
                    </div>
                    <div class="datasources" v-if="reportType == 2">
                        <div>是否达标：{{ userReportState }}</div>
                        <div style="margin-top:10px">数据来源：{{ source }}填报</div>
                    </div>

                    <div class="datasources" v-if="reportType !== 2">数据来源：{{ source }}填报</div>
                </div>
            </el-dialog>
        </div>
        <div class="meteorologyEMDialog dialogStyle ">
            <el-dialog
                title="汇报检测水质数值"
                width="31%"
                :visible.sync="waternumberdialogVisible"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :modal-append-to-body="false"
                center
                class="dialog"
                v-if="waternumberdialogVisible"
            >
                <img
                    src="../assets/image/eppoWisdom/close.png"
                    alt=""
                    class="clone"
                    @click="waternumberdialogVisible = false"
                />
                <div class="wire"></div>
                <div class="waternumber_content">
                    <el-form ref="form" :model="form" :rules="reportrules">
                        <div class="waternumber_content_box">
                            <div class="status">检测标准：</div>
                            <div class="handleBox">
                                <div
                                    class="handleBox_item formStyle header_max_width"
                                    style="height: 40px;margin-right: 16px;"
                                >
                                    <el-form-item class="formStyle" prop="applyTo">
                                        <el-select
                                            v-model="form.applyTo"
                                            clearable
                                            popper-class="selectStyle_list"
                                            style="height: 40px; width:232px"
                                            @change="chooseCrop"
                                        >
                                            <el-option
                                                v-for="(item, index) in monoculture"
                                                :key="index"
                                                :label="item.key"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                                <div
                                    class="handleBox_item formStyle header_max_width"
                                    style="height: 40px;margin-right: 0px;"
                                >
                                    <el-form-item class="formStyle" prop="solubleType">
                                        <el-select
                                            v-model="form.solubleType"
                                            clearable
                                            style="width:140px;height:40px"
                                            popper-class="selectStyle_list"
                                            @change="selectLand"
                                        >
                                            <el-option
                                                v-for="(item, index) in Soilsalination"
                                                :key="index"
                                                :label="item.label"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                            </div>
                        </div>

                        <div class="tableBox systemTableStyle table table_right">
                            <el-table
                                :data="tableDatass"
                                style="width:100%"
                                :cell-style="cellStyle"
                                :header-cell-style="cellStyle"
                            >
                                <el-table-column prop="date" label="检测项" align="center"></el-table-column>
                                <el-table-column prop="name" label="检测标准" align="center"></el-table-column>
                                <el-table-column label="检测数值" align="center">
                                    <template slot-scope="scope">
                                        <div class="formStyle">
                                            <el-input
                                                v-model="form.ph"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == 'ph'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.temperature"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '水温'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.suspended"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '悬浮物'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.bod"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '五日生化需氧量'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.cod"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '化学需氧量'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.anionicSurfactant"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '阴离子表面活性剂'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.chloride"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '氯化物'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.sulfide"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '硫化物'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.soluble"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '全盐量'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.lead"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '总铅'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.cadmium"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '总镉'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.chromium"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '铬(六价)'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.mercury"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '总汞'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.arsenic"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '总砷'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.fecalColiform"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '粪大肠菌群数'"
                                            ></el-input>
                                            <el-input
                                                v-model="form.roundworm"
                                                style="height: 40px;width:90px"
                                                v-if="scope.row.date == '蛔虫卵数'"
                                            ></el-input>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <el-row>
                            <el-col :span="2">
                                <div style="height:2px"></div>
                            </el-col>
                            <el-col :span="4" style="margin-left: 16px;">
                                <div class="row_text">上报区域：</div>
                            </el-col>
                            <el-col :span="6">
                                <div class="formStyle">
                                    <el-form-item class="formStyle" prop="areaId">
                                        <el-select
                                            v-model="form.areaId"
                                            clearable
                                            popper-class="selectStyle_list"
                                            style="height: 40px;width:189px"
                                        >
                                            <el-option
                                                v-for="(item, index) in cities"
                                                :key="index"
                                                :label="item.areaName"
                                                :value="item.areaId"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="2">
                                <div style="height:2px"></div>
                            </el-col>
                            <el-col :span="4" style="margin-left: 16px;">
                                <div class="row_text">上报时间：</div>
                            </el-col>
                            <el-col :span="6">
                                <div class="formStyle">
                                    <el-form-item class="formStyle" prop="showTime">
                                        <el-date-picker
                                            v-model="form.showTime"
                                            type="date"
                                            placeholder="选择日期"
                                            style="height:40px; width:189px"
                                            format="yyyy 年 MM 月 dd 日"
                                            value-format="yyyy-MM-dd"
                                            popper-class="datePickerStyle"
                                        ></el-date-picker>
                                    </el-form-item>
                                </div>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="waternumber_button">
                    <div class="cancel_button " @click="waternumberdialogVisible = false">取消</div>
                    <div class="confirm_button " @click="submit">提交</div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import znyAMap from '../components/znyAMap.vue'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
import WaterService from '../jaxrs/concrete/com.zny.ia.api.WaterService.js'
import data from '../jaxrs/constants/com.zny.ia.constant.ProdConstant$水质检测适用于的作物种类'
export default {
    components: {
        znyAMap,
    },
    data() {
        return {
            wire: '——',
            reportrules: {
                showTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
                areaId: [{ required: true, message: '请选择相关区域', trigger: 'change' }],
                applyTo: [{ required: true, message: '请选择检测标准', trigger: 'change' }],
                solubleType: [{ required: true, message: '请选择检测标准', trigger: 'change' }],
            },

            classOption: {
                step: 0.5,
                limitMoveNum: 20,
            },
            // 区域下拉
            cities: [],
            // 所有区域基本信息
            allcities: [],
            district: undefined,
            districtlist: {},
            showScroll2: false,
            Scroll: null,
            timer: null, //用来接收setInterval()返回的 ID 值
            dialogVisibles: false,
            areaIsJudge:true,//判断当前用户能否操作该区域
            waterdialogVisible: false,
            waternumberdialogVisible: false,
            title: '玉米种植一区水质检测记录',
            form: {},
            tableDatass: [
                {
                    date: 'ph',
                    name: '5.5~8.5',
                },
                {
                    date: '水温',
                    name: '≤35',
                },
                {
                    date: '悬浮物',
                    name: '≤100',
                },
                {
                    date: '五日生化需氧量',
                    name: '≤100',
                },
                {
                    date: '化学需氧量',
                    name: '≤200',
                },
                {
                    date: '阴离子表面活性剂',
                    name: '≤8',
                },
                {
                    date: '氯化物',
                    name: '≤350',
                },
                {
                    date: '硫化物',
                    name: '≤1',
                },
                {
                    date: '全盐量',
                    name: '≤1000',
                },
                {
                    date: '总铅',
                    name: '≤0.2',
                },
                {
                    date: '总镉',
                    name: '≤0.01',
                },
                {
                    date: '铬(六价)',
                    name: '≤0.001',
                },
                {
                    date: '总汞',
                    name: '≤0.1',
                },
                {
                    date: '总砷',
                    name: '≤0.1',
                },
                {
                    date: '粪大肠菌群数',
                    name: '≤40000',
                },
                {
                    date: '蛔虫卵数',
                    name: '≤20',
                },
            ],
            tableData: [],
            waterQualitytime: [],
            querlist: {
                num: 1,
                pageSize: 10,
                condition: {
                    startTime: '',
                    endTime: '',
                    state: '',
                },
            },
            runningState: [
                {
                    value: 0,
                    label: '未达标',
                },
                {
                    value: 1,
                    label: '正常',
                },
            ],
            payTime: [],
            waterQualityInspectionList: [],
            particularslist: [],
            monoculture: data._toArray(),
            Soilsalination: [
                {
                    value: false,
                    label: '非盐碱土地区',
                },
                {
                    value: true,
                    label: '盐碱土地区',
                },
            ],
            total: 0,
            tablelist: {},
            regionalTimedialogVisible: false,
            waterimg: [],
            reportType: undefined,
            source: '',
            userReportState: '',
            regionaltilte: '',
        }
    },
    mounted() {
        this.adddistrict()

        this.inspectionList()
    },
    methods: {
        adddistrict() {
            CommonService.allAreaOfCurrentUserManage().then(res => {
                this.cities = res
            })
            CommonService.allAreaListInformation().then(res => {
                this.allcities = res
                this.district = res[0].areaId
                this.selectionRegion(res[0].areaId)
            })
            WaterService.listWaterSchedule().then(res => {
                res.forEach(item => {
                    var month = item.dateTime.substr(5, 2)
                    let day = item.dateTime.substr(8, 2)
                    // console.log(month)
                    item.dateTime = `${month}月${day}日`
                })
                this.waterQualitytime = res
            })
        },
        // 选择区域
        selectionRegion(district, value) {
            WaterService.findWaterInfo(district).then(res => {
                if (value == 1) {
                    this.tablelist = res
                    this.title = `${res.areaName}水质检测记录`
                    return
                }
                this.districtlist = res
            })
        },
        // 打开水质检测记录
        inspectionList() {
            this.listen('waterMonitorEMDialogOpen', areaId => {
                this.testReportList()
                this.selectionRegion(areaId, 1)
                this.dialogVisibles = true
                this.areaJudge(areaId)
            })
        },
        // 判断当前用户能否操作该区域
        areaJudge(areaId){
            CommonService.areaJudge(areaId)
            .then(res=>{
                // console.log(res);
                this.areaIsJudge=res
            })
        },
        // 水质检测报告列表
        testReportList() {
            WaterService.listWaterReport(this.querlist).then(res => {
                this.waterQualityInspectionList = res.list
                // this.querlist.num = res.num
                // this.querlist.pageSize = res.pageSize
                this.total = res.count
            })

            // this.dialogVisibles = true
        },
        // 查看详情
        checkDetails(reportId) {
            WaterService.findWaterDetailInfo(reportId).then(res => {
                if (res.source == null) {
                    this.source = '设备上传'
                } else {
                    this.source = res.source
                }
                if (res.reportType == 2) {
                    this.regionaltilte = '检测水质文件'
                } else {
                    this.regionaltilte = '检测水质数值'
                }
                if (res.userReportState) {
                    this.userReportState = '否'
                } else {
                    this.userReportState = '是'
                }
                this.reportType = res.reportType
                this.particularslist = res.waterDataList
                this.waterimg = res.picList.map(res => res.url)
                this.waterdialogVisible = true
            })
        },
        // 提交
        submit() {
            this.$refs.form.validate(valid => {
                if (!valid) return
                WaterService.addWaterReport(this.form).then(() => {
                    this.$message({
                        type: 'success',
                        message: '提交成功！',
                    })
                    this.testReportList()
                    this.waternumberdialogVisible = false
                })
            })
        },
        report() {
            this.form = {}
            this.tableDatass = [
                {
                    date: 'ph',
                    name: '5.5~8.5',
                },
                {
                    date: '水温',
                    name: '≤35',
                },
                {
                    date: '悬浮物',
                    name: '≤100',
                },
                {
                    date: '五日生化需氧量',
                    name: '≤100',
                },
                {
                    date: '化学需氧量',
                    name: '≤200',
                },
                {
                    date: '阴离子表面活性剂',
                    name: '≤8',
                },
                {
                    date: '氯化物',
                    name: '≤350',
                },
                {
                    date: '硫化物',
                    name: '≤1',
                },
                {
                    date: '全盐量',
                    name: '≤1000',
                },
                {
                    date: '总铅',
                    name: '≤0.2',
                },
                {
                    date: '总镉',
                    name: '≤0.01',
                },
                {
                    date: '铬(六价)',
                    name: '≤0.001',
                },
                {
                    date: '总汞',
                    name: '≤0.1',
                },
                {
                    date: '总砷',
                    name: '≤0.1',
                },
                {
                    date: '粪大肠菌群数',
                    name: '≤40000',
                },
                {
                    date: '蛔虫卵数',
                    name: '≤20',
                },
            ]
            this.waternumberdialogVisible = true
        },
        chooseCrop() {
            if (this.form.applyTo == 0) {
                this.tableDatass[2].name = '≤80'
                this.tableDatass[3].name = '≤60'
                this.tableDatass[4].name = '≤150'
                this.tableDatass[5].name = '≤5'
                this.tableDatass[13].name = '≤0.05'
                this.tableDatass[14].name = '≤40000'
            } else if (this.form.applyTo == 1) {
                this.tableDatass[2].name = '≤100'
                this.tableDatass[3].name = '≤100'
                this.tableDatass[4].name = '≤200'
                this.tableDatass[5].name = '≤8'
                this.tableDatass[13].name = '≤0.1'
                this.tableDatass[14].name = '≤40000'
            } else if (this.form.applyTo == 2) {
                this.tableDatass[2].name = '≤60'
                this.tableDatass[3].name = '≤40'
                this.tableDatass[4].name = '≤100'
                this.tableDatass[5].name = '≤5'
                this.tableDatass[13].name = '≤0.05'
                this.tableDatass[14].name = '≤20000'
                this.tableDatass[15].name = '≤20'
            } else if (this.form.applyTo == 3) {
                this.tableDatass[2].name = '≤15'
                this.tableDatass[3].name = '≤15'
                this.tableDatass[4].name = '≤60'
                this.tableDatass[5].name = '≤5'
                this.tableDatass[13].name = '≤0.05'
                this.tableDatass[14].name = '≤10000'
                this.tableDatass[15].name = '≤10'
            }
        },
        selectLand() {
            if (this.form.solubleType) {
                this.tableDatass[8].name = '≤1000'
            } else if (!this.form.solubleType) {
                this.tableDatass[8].name = '≤2000'
            }
        },
        GetzhifuTime() {
            
            if (!this.payTime) {
                this.querlist.condition.startTime = null
                this.querlist.condition.endTime = null
                return
            }
            this.querlist.condition.startTime = this.payTime[0]
            this.querlist.condition.endTime = this.payTime[1]
        },
        handleCurrentChange(val) {
            this.querlist.num = val
            this.testReportList()
        },
        // 打开虫情环境监测弹窗
        InsectSituationEMDialogOpen() {
            this.zEmit('InsectSituationEMDialogOpen')
        },
        // 修改 table cell边框的背景色
        cellStyle() {
            return 'background-color: #003D42; color: #DFEEF3!important; border-color:#FEFFFF'
        },
        // 修改 table header cell的背景色
        headerCellStyle() {
            return 'background: #1a5155;color: #DFEEF3!important; border:0px'
        },
        // 修改 table cell边框的背景色
        cellStyles() {
            return 'background-color: rgba(223,238,243,0.1); color: #DFEEF3!important; border-color:#FEFFFF'
        },
        // 修改 table header cell的背景色
        headerCellStyles() {
            return 'background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(0,245,255,0));color: #DFEEF3!important; border:0px'
        },
    },
    beforeDestroy() {
        const that = this
        that.Scroll && clearInterval(that.Scroll)
        that.timer && clearInterval(that.timer)
        Object.assign(that.$data, that.$options.data())
    },
}
</script>
<style lang="less" scoped>
@import '../assets/css/waterMonitor.less';
</style>
<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/InsectSituation.less';
</style>

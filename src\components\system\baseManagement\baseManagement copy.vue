<template>
  <div class="baseManagement systemDialogStyle">
    <!-- 
    <div class="baseManagement_tabList">
      <div class="tabList_item" v-for="(item,index) in tabList" :key="index">
        <div :class="{active:tabActive==item.areaId}" @click="tabClick(item)">
          {{item.areaName}}
        </div>
      </div>
    </div>
    -->
    <div class="baseManagement_dataList">
      <div class="dataList_item" >
        <div class="dataList_item_text">
          <div>设备总数</div>
          <div>{{areaStatus.devicesNumber}}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../assets/image/managementSystem/icon7.png" alt="">
        </div>
      </div>
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>报警设备数</div>
          <div>{{areaStatus.alarmDevicesNumber}}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../assets/image/managementSystem/icon12.png" alt="">
        </div>
      </div>
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>离线设备数</div>
          <div>{{areaStatus.offlineDevicesNumber}}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../assets/image/managementSystem/icon9.png" alt="">
        </div>
      </div>
      <div class="dataList_item" >
        <div class="dataList_item_text">
          <div>区域总数</div>
          <div>{{areaStatus.areaNumber}}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../assets/image/managementSystem/icon8.png" alt="">
        </div>
      </div>
      <!-- <div class="report_item" @click="reportHistoryDialogOpen" v-if="tabActive==0">
        <div>
          <img src="../../assets/image/managementSystem/icon13.png" alt="">
        </div>     
        <div>
          汇报记录
        </div>   
      </div> -->
      <div class="report_item" @click="reportDialog=true">
        <div>
          <img src="../../assets/image/managementSystem/icon13.png" alt="">
        </div>     
        <div>
          概述信息管理
        </div>   
      </div>
    </div>
    <div class="baseManagement_con">
      <div class="handleBox">
        <div class="handleBox_leftSection">
          <div class="handleBox_item systemFormStyle">
            <el-select v-model="selectedAreaId" clearable placeholder="请选择区域">
              <el-option
                v-for="item in areaList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaId">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item systemFormStyle">
            <el-select v-model="deviceType" clearable placeholder="请选择设备">
              <el-option
                v-for="item in deviceTypeList"
                :key="item.value"
                :label="item.key"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item systemSearchButtonStyle2">
            <el-button @click="search">查询</el-button>
          </div>
        </div>
        <div class="handleBox_rightButtons">
          <div class="handleBox_item systemSearchButtonStyle2">
            <el-button @click="addDialogOpen">添加</el-button>
          </div>
          <div class="handleBox_item systemSearchButtonStyle2">
            <el-button @click="downloadList">下载</el-button>
          </div>
        </div>
      </div>
      <div class="tableBox tableBox1 systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column type="expand">
            <template slot-scope="scope">
              <el-table
                :data="scope.row.equipmentData || []"
                style="width: 100%">
                <el-table-column
                  type="index" 
                  label="序号" 
                  align="center" 
                  width="90">
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="设备名称">
                  <template slot-scope="scoped">
                    <div>
                      {{scoped.row.equipmentSimpleInformationVo.equipmentName}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="设备状态"
                  width="180">
                  <template slot-scope="scoped">
                    <div>
                      {{scoped.row.state | stateFormat}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="是否报警">
                  <template slot-scope="scoped">
                    <div v-if="scoped.row.alarmState">
                      是
                    </div>
                    <div v-else>
                      否
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  width="150"
                  label="操作">
                  <template slot-scope="scoped">
                    <span class="viewData" @click="openHistoryDialog(scoped.row)">
                      查看数据
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="区域"
            width="300">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.areaVo.areaName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="设备名称">
            <template slot-scope="scoped">
              <span v-for="(item,index) in scoped.row.equipmentSimpleInformationVo" :key="index">
                {{item.equipmentName}}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            width="150"
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="editDialogOpen(scoped.row)">编辑</span>
              <span class="viewData" >删除</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    <!-- 
    <div class="baseManagement_con" v-if="tabActive!=0">
      <div class="tableBox tableBox2 systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            prop="date"
            align="center" 
            label="设备名称">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.equipmentSimpleInformationVo.equipmentName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="设备状态"
            width="180">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.state | stateFormat}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center" 
            label="是否报警">
            <template slot-scope="scoped">
              <div v-if="scoped.row.alarmState">
                是
              </div>
              <div v-else>
                否
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="150"
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="openHistoryDialog(scoped.row)">
                查看数据
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    -->
    <!-- 汇报弹窗 -->
    <el-dialog
      class="reportDialog"
      title="大屏展示数据汇报"
      :visible.sync="reportDialog"
      width="23%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="reportClose('reportRuleForm')">
        <img src="../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formList">
        <el-form :model="reportRuleForm" :rules="reportRules" ref="reportRuleForm" label-width="100px" label-position="top">
          <el-form-item label="智慧农业覆盖率" prop="coverage">
            <el-input v-model="reportRuleForm.coverage" clearable class="systemFormStyle" placeholder="请输入智慧农业覆盖率">
              <i
                class=" el-input__icon"
                slot="suffix">
                %
              </i>
            </el-input>
          </el-form-item>
          <el-form-item label="作物年产值" prop="annualValue">
            <el-input v-model="reportRuleForm.annualValue" clearable class="systemFormStyle" placeholder="请输入作物年产值">
              <i
                class=" el-input__icon"
                slot="suffix">
                元
              </i>
            </el-input>
          </el-form-item>
          <el-form-item label="作物总产量" prop="yield">
            <el-input v-model="reportRuleForm.yield" clearable class="systemFormStyle" placeholder="请输入作物总产量">
              <i
                class=" el-input__icon"
                slot="suffix">
                公斤kg
              </i>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="reportCancel('reportRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="reportSubmit('reportRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 汇报记录弹窗 -->
    <el-dialog
      class="reportHistoryDialog"
      title="汇报历史记录"
      :visible.sync="reportHistoryDialog"
      width="60%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="reportHistoryDialog=false">
        <img src="../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="reportHistoryData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            prop="coverage"
            align="center" 
            label="智慧农业覆盖率">
          </el-table-column>
          <el-table-column
            prop="annualValue"
            align="center" 
            label="作物年产值/元">
          </el-table-column>
          <el-table-column
            prop="yield"
            align="center" 
            label="作物总产量/公斤">
          </el-table-column>
          <el-table-column
            prop="createDate"
            align="center" 
            label="提交时间">
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 编辑弹窗 -->
    <el-dialog
      class="editDialog"
      title="编辑"
      :visible.sync="editDialog"
      width="37%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="editClose('editRuleForm')">
        <img src="../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="formList">
        <el-form :model="editRuleForm" :rules="editRules" ref="editRuleForm" label-width="100px" label-position="top">
          <div class="formItem">
            <el-form-item label="区域名称">
              <el-input v-model="editRuleForm.areaName" clearable class="systemFormStyle"></el-input>
            </el-form-item>
            <el-form-item label="当前作物">
              <el-input v-model="editRuleForm.crop" disabled class="systemFormStyle"></el-input>
            </el-form-item>
          </div>
          <div class="formItem1">
            <el-form-item label="包含设备">
              <div class="tips" @click="numIncrease">
                <img src="../../assets/image/managementSystem/add-icon.png" alt="">
              </div>
              <div class="formItem1_con" v-for="(item,index) in editRuleForm.equipmentId" :key="index">
                <el-input v-model="editRuleForm.equipmentId[index].equipmentName" class="systemFormStyle" disabled></el-input>
                <img @click="numReduce(index)" src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
              </div>
              <div class="formItem1_con systemFormStyle formItem1_con2">
                <el-select 
                v-model="editRuleForm.equipment" 
                placeholder="请选择设备">
                  <el-option
                    v-for="item in editRuleForm.equipmentList"
                    :key="item.equipmentId"
                    :label="item.equipmentName"
                    :value="item.equipmentId">
                  </el-option>
                </el-select>
                <!-- <el-input v-model="editRuleForm.equipmentId[index].equipmentName" class="systemFormStyle"></el-input> -->
                <img @click="numReduce1" src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="editCancel('editRuleForm')">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="editSubmit('editRuleForm')">确定</el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- 新增区域弹窗 -->
    <el-dialog
      class="addAreaDialog"
      :visible.sync="addDialog"
      width="1512px"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div slot="title"></div>
      <div class="addAreaDialog_content">
        <!-- 左侧地图区域 -->
        <div class="addAreaDialog_map">
          <div class="map_controls">
            <div class="map_selectButton" @click="toggleAreaSelection">
              {{ isSelectingArea ? '停止选择' : '选择区域' }}
            </div>
            <div class="map_clearButton" @click="clearPlot" v-if="currentPolygon">
              清除地块
            </div>
          </div>
          <zny-map
            ref="addAreaMap"
            style="width: 100%; height: 100%"
            :map-options="addMapOptions"
            :max-zoom="20"
            :roadnet="true"
            @click="handleMapClick">
          </zny-map>
        </div>
        <!-- 右侧表单区域 -->
        <div class="addAreaDialog_form">
          <div class="form_header">
            <div class="form_title">添加</div>
            <div class="close" @click="addClose('addRuleForm')">
              <img src="../../assets/image/managementSystem/close.png" alt="">
            </div>
          </div>
          <el-form :model="addRuleForm" :rules="addRules" ref="addRuleForm" label-width="100px" label-position="top">
            <el-form-item label="区域名称" prop="areaName">
              <el-input v-model="addRuleForm.areaName" clearable class="systemFormStyle" placeholder="请输入区域名称"></el-input>
            </el-form-item>
            <el-form-item label="当前作物" prop="crop">
              <el-input v-model="addRuleForm.crop" clearable class="systemFormStyle" placeholder="请输入作物种类"></el-input>
            </el-form-item>
            <el-form-item label="区域面积">
              <el-input v-model="addRuleForm.areaSize" class="systemFormStyle" placeholder="14574.07" disabled></el-input>
            </el-form-item>
            <el-form-item>
              <div class="equipment_label_container">
                <span class="equipment_label">包含设备</span>
                <div class="equipment_addButton" @click="showEquipmentSelector" v-if="!showSelector">
                  <img src="../../assets/image/managementSystem/add-icon.png" alt="">
                </div>
              </div>
              <!-- 设备选择器 - 独立于滚动容器 -->
              <div class="equipment_selector" v-if="showSelector">
                <el-select
                  v-model="addRuleForm.equipment"
                  placeholder="请选择设备"
                  class="systemFormStyle"
                  @change="handleEquipmentSelect">
                  <el-option
                    v-for="item in availableEquipmentList"
                    :key="item.equipmentId"
                    :label="item.equipmentName"
                    :value="item.equipmentId">
                  </el-option>
                </el-select>
                <img @click="addNumReduce1" src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="equipment_removeBtn">
              </div>
              <!-- 已选设备列表 - 带滚动条 -->
              <div class="equipment_list" v-if="addRuleForm.equipmentId.length > 0">
                <div class="equipment_item" v-for="(item,index) in addRuleForm.equipmentId" :key="index">
                  <el-input v-model="addRuleForm.equipmentId[index].equipmentName" class="systemFormStyle" disabled></el-input>
                  <img @click="addNumReduce(index)" src="../../assets/image/centralControlPlatform/reduce-icon.png" alt="" class="equipment_removeBtn">
                </div>
              </div>
            </el-form-item>
          </el-form>
          <div class="btnBox">
            <div class="btnItem systemResetButtonStyle2">
              <el-button @click="addCancel('addRuleForm')">取消</el-button>
            </div>
            <div class="btnItem systemSearchButtonStyle2">
              <el-button @click="addSubmit('addRuleForm')">确定</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <BaseEquipmentHistoryDialog/>
  </div>
</template>
<script>
import 指标类型 from '../../../jaxrs/constants/com.zny.ia.constant.DeviceConstant$指标类型.js';
import 农作物种类 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
import BaseEquipmentHistoryDialog from './components/baseEquipmentHistoryDialog.vue/index.js';
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
import BaseManagementService from '../../../jaxrs/concrete/com.zny.ia.api.BaseManagementService.js';
export default {
  components:{
    BaseEquipmentHistoryDialog
  },
  data(){
    return{
      administrators:"admin",//管理员：admin->总,subAdmin->分管理员
      tabActive:0, // 默认设置为0，表示基地总览
      tabList:[],
      areaStatus:{
        devicesNumber: 0,        // 设备总数
        alarmDevicesNumber: 0,   // 报警设备数
        offlineDevicesNumber: 0, // 离线设备数
        areaNumber: 0            // 区域总数
      },//获取列表上方数据统计
      deviceType:null,
      deviceTypeList:指标类型._toArray(),//设备列表
      cropList: 农作物种类._toArray(), // 作物种类列表
      tableData: [],
      excelData:[],
      // 分页
      pageSize:20,
      currentPage:1,
      total:0,
      pageCount:1,
      // 汇报弹窗
      reportDialog:false,
      reportRuleForm:{
        coverage:"",
        annualValue:"",
        yield:"",
      },
      reportRules:{
        coverage: [
          { required: true, message: '请输入智慧农业覆盖率', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        annualValue: [
          { required: true, message: '请输入作物年产值', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        yield: [
          { required: true, message: '请输入作物总产量', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
      },
      // 汇报记录弹窗
      reportHistoryDialog:false,
      reportHistoryData:[],
      // 编辑弹窗
      editDialog:false,
      // num:1,//包含设备列表
      editRuleForm:{
        areaId:"",
        areaName:"",
        crop:"",
        // 已经有的设备
        equipmentIds:"",//已有设备id list
        equipmentId:[],//已有设备列表
        // 选择的设备
        equipment:"",//已选择的设备
        equipmentList:[],//所有设备列表
      },
      editRules:{
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
        ],
      },
      // 新增区域弹窗
      addDialog: false,
      addRuleForm: {
        areaName: "",
        crop: "",
        areaSize: "0.00", // 区域面积
        equipmentId: [], // 已添加的设备列表
        equipment: "", // 已选择的设备
        equipmentList: [], // 所有设备列表
      },
      // 地图相关
      addMapOptions: {
        zoom: 15,
        center: [117.12665, 36.6584],
        amapTileUrl: localStorage.getItem('amapTileUrl'),
      },
      isSelectingArea: false, // 是否正在选择区域
      showSelector: false, // 是否显示设备选择器
      currentPolygon: null, // 当前绘制的多边形
      addRules: {
        areaName: [
          { required: true, message: '请输入区域名称', trigger: 'blur' },
        ],
        crop: [
          { required: true, message: '请选择作物种类', trigger: 'change' },
        ],
      },
      // 新增：区域下拉选择
      areaList: [],
      selectedAreaId: null,
    }
  },
  computed: {
    // 可用设备列表（排除已选择的设备）
    availableEquipmentList() {
      if (!this.addRuleForm.equipmentList) return [];
      const selectedIds = this.addRuleForm.equipmentId.map(item => item.equipmentId);
      return this.addRuleForm.equipmentList.filter(item => !selectedIds.includes(item.equipmentId));
    }
  },
  mounted(){
    let role = localStorage.getItem('userRoles')
    if(role.indexOf(0) == -1){
      this.administrators='subAdmin'
    }else{
      this.administrators='admin'
    }
    this.getAllAreaListInformation()

    // 新的初始化方法，直接加载基地总览数据
    // this.tabActive = 0;
    // this.getAreaStatisticsData();
    this.getList();
  },
  methods:{
    /*
    // tab 列表点击
    tabClick(item){
      this.tabActive=item.areaId
      this.getAreaStatisticsData()
      this.getList()
    },
    */
    // 获取所有区域列表
    getAllAreaListInformation(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        // 用于区域下拉选择框
        this.areaList = res;
        // this.tabActive=res[0].areaId
        // this.tabList=res
      })
    },
    // 获取列表上方数据统计
    getAreaStatisticsData(){
      /* 
      if(this.tabActive==0) {  
        BaseManagementService.areaStatistics()
        .then(res=>{
          this.areaStatus=res
        })
      }else{
        BaseManagementService.areaStatistics(this.tabActive)
        .then(res=>{
          this.areaStatus=res
        })
      }
      */
      // 始终加载基地总览数据
      BaseManagementService.areaStatistics()
      .then(res=>{
        console.log(res);
        this.areaStatus=res
      })
    },
    // 查询
    search(){
      this.currentPage=1
      this.getList()
    },
    // 获取列表
    getList(){
      /* 
      if(this.tabActive==0) {
        BaseManagementService.baseAreaOverviewInformation(this.currentPage,this.pageSize,this.deviceType==""?null:this.deviceType)
        .then(res=>{
          this.tableData=res.list
          this.pageCount=res.count
          this.count=res.total
          
          // 为每个区域加载设备详细信息
          if (this.tableData && this.tableData.length > 0) {
            this.loadEquipmentDetailsForAreas();
          }
        })
      }else {
        BaseManagementService.areaOverviewInformation(this.currentPage,this.pageSize,this.tabActive)
        .then(res=>{
          this.tableData=res.list
          this.pageCount=res.count
          this.count=res.total
        })
      }
      */
      // 始终加载基地总览数据
      BaseManagementService.baseAreaOverviewInformation(this.currentPage,this.pageSize,this.deviceType==""?null:this.deviceType)
      .then(res=>{
        this.tableData=res.list
        this.pageCount=res.count
        this.count=res.total
        
        // 为每个区域加载设备详细信息
        if (this.tableData && this.tableData.length > 0) {
          this.loadEquipmentDetailsForAreas();
        }
      })
    },
    // 为每个区域加载设备详细信息
    loadEquipmentDetailsForAreas() {
      let completedRequests = 0;
      const totalRequests = this.tableData.length;
      
      this.tableData.forEach((area, index) => {
        if (area.areaVo && area.areaVo.areaId) {
          BaseManagementService.areaOverviewInformation(1, 100, area.areaVo.areaId)
            .then(res => {
              this.$set(this.tableData[index], 'equipmentData', res.list);
              
              // 当所有区域的设备数据都加载完成后，计算统计信息
              completedRequests++;
              if (completedRequests === totalRequests) {
                this.calculateStatistics();
              }
            })
        } else {
          completedRequests++;
          if (completedRequests === totalRequests) {
            this.calculateStatistics();
          }
        }
      });
    },
    // 计算统计信息
    calculateStatistics() {
      // 初始化统计数据
      let statistics = {
        devicesNumber: 0,        // 设备总数
        alarmDevicesNumber: 0,   // 报警设备数
        offlineDevicesNumber: 0, // 离线设备数
        areaNumber: this.tableData.length // 区域总数
      };
      
      // 遍历所有区域的设备数据
      this.tableData.forEach(area => {
        // 计算设备总数
        if (area.equipmentSimpleInformationVo && area.equipmentSimpleInformationVo.length) {
          statistics.devicesNumber += area.equipmentSimpleInformationVo.length;
        }
        
        // 统计报警和离线设备
        if (area.equipmentData && area.equipmentData.length) {
          area.equipmentData.forEach(device => {
            // 统计报警设备
            if (device.alarmState) {
              statistics.alarmDevicesNumber++;
            }
            
            // 统计离线设备（state值为2表示离线）
            if (device.state === 2) {
              statistics.offlineDevicesNumber++;
            }
          });
        }
      });
      
      // 更新统计数据
      this.areaStatus = statistics;
    },
    handleSizeChange(){

    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.getList()
    },
    // 打开历史记录弹窗
    openHistoryDialog(row){
      /* 
      let obj={
        areaId:this.tabActive,
        deviceType:row.equipmentSimpleInformationVo.deviceType
      }
      */
      // 更新后的传参方式，直接使用行数据中的信息
      let obj={
        areaId: row.areaId || 0, // 如果没有areaId则默认使用0
        deviceType: row.equipmentSimpleInformationVo.deviceType
      }
      this.zEmit('openbaseEquipmentHistoryDialog',obj);
    },
    // 打开汇报记录弹窗
    reportHistoryDialogOpen(){
      this.reportHistoryDialog=true
      BaseManagementService.reportHistoryListInformation(1,100)
      .then(res=>{
        this.reportHistoryData=res.list
      })
    },
    // 数据汇报关闭
    reportClose(formName){
      this.$refs[formName].resetFields();
      this.reportDialog=false
    },
    // 数据汇报取消
    reportCancel(formName){
      this.$refs[formName].resetFields();
      this.reportDialog=false
    },
    // 数据汇报确定
    reportSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let po={
            coverage:this.reportRuleForm.coverage,
            annualValue:this.reportRuleForm.annualValue,
            yield:this.reportRuleForm.yield,
          }
          BaseManagementService.areaReport(po)
          .then(()=>{
            // console.log(res);
            that.$message({
              message: '数据汇报成功',
              type: 'success'
            });
            that.$refs[formName].resetFields();
            that.reportDialog=false
          })
        }
      })
    },
    // 打开编辑弹窗
    editDialogOpen(row){
      this.editDialog=true
      this.editRuleForm.areaId=row.areaVo.areaId
      this.editRuleForm.areaName=row.areaVo.areaName
      if(row.crop){
        this.editRuleForm.crop=农作物种类._lableOf(row.crop)
      }
      this.editRuleForm.equipmentId=row.equipmentSimpleInformationVo
      this.getListEquipment()
    },
    // 获取设备列表
    getListEquipment(){
      CommonService.listEquipment()
      .then(res=>{
        this.editRuleForm.equipmentList=res
      })
    },
    // 点击数量增加
    numIncrease(){
      // this.num+=1
      if(this.editRuleForm.equipment==""){
        this.$message('请选择设备');
      }else{
        this.editRuleForm.equipmentList.forEach(e=>{
          if(this.editRuleForm.equipment==e.equipmentId){
            this.editRuleForm.equipmentId.push(e)
            this.editRuleForm.equipment=''
          }
        })
      }
    },
    // 点击删除某一项
    numReduce(index){
      this.editRuleForm.equipmentId.splice(index,1)
    },
    // 点击清除已选择项
    numReduce1(){
      this.editRuleForm.equipment=''
    },
    // 编辑弹窗关闭
    editClose(formName){
      this.$refs[formName].resetFields();
      this.editDialog=false
    },
    // 编辑弹窗取消
    editCancel(formName){
      this.$refs[formName].resetFields();
      this.editDialog=false
    },
    // 编辑弹窗确定
    editSubmit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let equipmentIds=[]
          that.editRuleForm.equipmentId.forEach(e=>{
            equipmentIds.push(e.equipmentId)
          })
          let po={
            areaId:that.editRuleForm.areaId,
            areaName:that.editRuleForm.areaName,
            equipmentIds:equipmentIds
          }
          BaseManagementService.changeAreaEquipment(po)
          .then(()=>{
            that.$message({
              message: '修改成功',
              type: 'success'
            });
            that.getList()
            that.$refs[formName].resetFields();
            that.editDialog=false
          })
        }
      })
    },
    
    // 打开新增区域弹窗
    addDialogOpen(){
      this.addDialog = true;
      // 清空表单
      this.addRuleForm.areaName = "";
      this.addRuleForm.crop = "";
      this.addRuleForm.areaSize = "0.00";
      this.addRuleForm.equipmentId = [];
      this.addRuleForm.equipment = "";
      this.isSelectingArea = false;
      this.showSelector = false;
      this.currentPolygon = null;
      // 获取设备列表
      this.getAddListEquipment();
    },
    // 获取新增区域所需的设备列表
    getAddListEquipment(){
      // 模拟设备数据
      this.addRuleForm.equipmentList = [
        { equipmentId: 1, equipmentName: "气象监测设备001" },
        { equipmentId: 2, equipmentName: "土壤检测设备002" },
        { equipmentId: 3, equipmentName: "灌溉控制设备003" },
        { equipmentId: 4, equipmentName: "温湿度传感器004" },
        { equipmentId: 5, equipmentName: "光照强度检测器005" },
        { equipmentId: 6, equipmentName: "水质监测设备006" },
        { equipmentId: 7, equipmentName: "虫情监测设备007" },
        { equipmentId: 8, equipmentName: "苗情监测设备008" }
      ];
      // 原来的接口调用（暂时注释）
      // CommonService.listEquipment()
      // .then(res=>{
      //   this.addRuleForm.equipmentList = res;
      // })
    },
    // 显示设备选择器
    showEquipmentSelector(){
      this.showSelector = true;
    },

    // 新增区域设备添加
    addNumIncrease(){
      if(this.addRuleForm.equipment == ""){
        this.$message('请选择设备');
      }else{
        this.addRuleForm.equipmentList.forEach(e => {
          if(this.addRuleForm.equipment == e.equipmentId){
            this.addRuleForm.equipmentId.push(e);
            this.addRuleForm.equipment = '';
            // 如果还有可选设备，保持选择器显示，否则隐藏
            if (this.availableEquipmentList.length === 0) {
              this.showSelector = false;
            }
          }
        })
      }
    },
    // 新增区域删除已选设备
    addNumReduce(index){
      this.addRuleForm.equipmentId.splice(index, 1);
      // 删除设备后，如果选择器未显示且还有可选设备，则显示选择器
      if (!this.showSelector && this.availableEquipmentList.length > 0) {
        this.showSelector = true;
      }
    },
    // 清除新增区域已选择设备
    addNumReduce1(){
      this.addRuleForm.equipment = '';
      this.showSelector = false;
    },
    // 新增区域弹窗关闭
    addClose(formName){
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.clearAllMapData(); // 清除所有地图数据
      this.addDialog = false;
    },
    // 新增区域弹窗取消
    addCancel(formName){
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.clearAllMapData(); // 清除所有地图数据
      this.addDialog = false;
    },
    // 新增区域弹窗确定
    addSubmit(formName){
      const that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let equipmentIds = [];
          that.addRuleForm.equipmentId.forEach(e => {
            equipmentIds.push(e.equipmentId);
          });
          let po = {
            areaName: that.addRuleForm.areaName,
            crop: that.addRuleForm.crop,
            areaSize: that.addRuleForm.areaSize,
            equipmentIds: equipmentIds
          };
          // 接口尚未实现，显示临时提示
          that.$message({
            message: '新增区域功能开发中，接口尚未实现',
            type: 'warning'
          });
          console.log("新增区域数据:", po);
          // TODO: 接口实现后取消注释下方代码
          /*
          BaseManagementService.addArea(po)
          .then(() => {
            that.$message({
              message: '新增区域成功',
              type: 'success'
            });
            that.getList();
            that.$refs[formName].resetFields();
            that.addDialog = false;
          });
          */
          // 临时关闭弹窗
          that.$refs[formName].resetFields();
          that.addDialog = false;
        }
      });
    },

    // 地图相关方法
    // 切换区域选择状态
    toggleAreaSelection(){
      if (this.isSelectingArea) {
        // 如果正在选择，则停止选择
        this.stopAreaSelection();
      } else {
        // 开始选择区域
        this.startAreaSelection();
      }
    },

    // 开始区域选择
    startAreaSelection(){
      // 如果已有地块，先清除
      if (this.currentPolygon) {
        this.clearPlot();
      }

      this.isSelectingArea = true;
      this.$message({
        message: '请在地图上绘制多边形选择区域',
        type: 'info'
      });
      this.drawPolygon();
    },

    // 停止区域选择
    stopAreaSelection(){
      this.isSelectingArea = false;
    },

    // 清除地块
    clearPlot(){
      if (this.currentPolygon) {
        // 先隐藏面积标签
        this.currentPolygon.hideAreaLabel();
        // 移除多边形
        this.$refs.addAreaMap.removePolygon(this.currentPolygon);
        this.currentPolygon = null;
        this.addRuleForm.areaSize = "0.00";

        this.$message({
          message: '已清除地块',
          type: 'info'
        });
      }
    },

    // 清除所有地图数据（包括面积标签）
    clearAllMapData(){
      if (this.currentPolygon) {
        // 先隐藏面积标签
        this.currentPolygon.hideAreaLabel();
        // 移除多边形
        this.$refs.addAreaMap.removePolygon(this.currentPolygon);
        this.currentPolygon = null;
      }
      this.addRuleForm.areaSize = "0.00";
      this.isSelectingArea = false;
    },

    // 绘制多边形
    drawPolygon(){
      if (!this.$refs.addAreaMap) return;

      this.$refs.addAreaMap
        .drawPolygon({
          border: {
            color: "#0093BC",
            weight: 2,
            opacity: 1
          },
          fill: {
            color: "#0093BC",
            opacity: 0.2
          },
          zIndex: 101,
        })
        .then((polygon) => {
          this.currentPolygon = polygon;

          // 显示面积标签
          polygon.showAreaLabel({
            formatter: (area) => {
              return ((area / 2000) * 3).toFixed(1) + " 亩";
            }
          });

          // 计算面积（转换为亩）
          const area = ((polygon.getArea() / 2000) * 3).toFixed(1);
          this.addRuleForm.areaSize = area;

          this.isSelectingArea = false;
          this.$message({
            message: `区域选择完成，面积：${area}亩`,
            type: 'success'
          });
        })
        .catch(() => {
          this.isSelectingArea = false;
        });
    },



    // 处理地图点击事件
    handleMapClick(e){
      // 如果不在选择区域模式，不处理点击事件
      return;
    },

    // 处理设备选择
    handleEquipmentSelect(){
      if (this.addRuleForm.equipment) {
        // 当选择设备后自动添加到列表
        this.addNumIncrease();
      }
    },
    // 下载列表
    downloadList(){
      BaseManagementService.baseAreaOverviewInformation(0,0,this.deviceType)
      .then(res=>{
        
        res.list.forEach(v=>{
          v.areaName=v.areaVo.areaName
          let equipmentName=[]
          v.equipmentSimpleInformationVo.forEach(e=>{
            equipmentName.push(e.equipmentName)
          })
          v.equipmentName=equipmentName.toString()
        })
        this.excelData=res.list
        this.export2Excel()
      })
    },
    //表格数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const {
          export_json_to_excel_productPlan
        } = require("@/excel/Export2Excel.js");
        var tHeader=[]
        var filterVal = []
        tHeader = ["区域","设备名称"]
        filterVal = [
          "areaName",
          "equipmentName",
        ];
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        export_json_to_excel_productPlan(tHeader, data, "基地总览数据表");
      });
    },
    //格式转换，直接复制即可,不需要修改什么
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
  },
}
</script>
<style lang="less">
@import '../../assets/css/system/baseManagement.less';
@media screen and (max-width: 1280px){
  .baseManagement{
    .editDialog, .addDialog{
      .el-dialog{
        width:48.5% !important;
      }
    }     
    .reportDialog{
      .el-dialog{
        width: 30% !important;
        .el-dialog__body{
          .formList{
            .systemFormStyle{
              width: 338px;
            }
          }
        }
      }
    } 
  } 
}
@media screen and (min-width: 1280px) and (max-width: 1680px){
  .baseManagement{
    .editDialog, .addDialog{
      .el-dialog{
        .el-dialog__body{
          .formList{
            .el-form{
              .el-input{
                width: 235px;
              }
              .formItem1{
                .el-form-item{
                  .el-form-item__content{
                    .systemFormStyle{
                      width: 235px;
                    }
                  }
                }
              }   
            }
          }
          
        }
      }
    }     
    .reportDialog{
      .el-dialog{
        .el-dialog__body{
          .formList{
            .systemFormStyle{
              width: 338px;
            }
          }
        }
      }
    } 
  } 
}

/* 展开行样式 */
.baseManagement {
  .el-table__expanded-cell {
    padding: 0 !important;
    
    .el-table {
      margin-bottom: 0;
      background-color: #f6f8fa;
      
      .el-table__header-wrapper {
        th {
          background-color: #eef1f6;
          color: #606266;
        }
      }
      
      .el-table__body-wrapper {
        .viewData {
          cursor: pointer;
          color: #409EFF;
        }
      }
    }
  }
}
</style>
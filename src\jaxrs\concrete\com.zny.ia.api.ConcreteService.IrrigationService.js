/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const IrrigationService = {
    /**
     * 灌溉区域页面-控制水阀
     * @param {*} nodeId 主通道节点Id
     * @param {*} control 0、打开 1、关闭
     * @returns Promise 
     */
    'controlTraceSourceWater': function (nodeId, control) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '810340615c5cae19bbda900ab13c2f07fe4bfe54', {nodeId, control})
            : execute(concreteModuleName, `/IrrigationService/controlTraceSourceWater`, 'json', 'POST', { nodeId, control });
    }, 
    /**
     * 灌溉区域页面-控制施肥阀
     * @param {*} nodeId 主通道节点Id
     * @param {*} control 0、打开 1、关闭
     * @returns Promise 
     */
    'controlTraceSourceFertilization': function (nodeId, control) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '45d6f500dbe3de893c39953d6297a933d0068a2c', {nodeId, control})
            : execute(concreteModuleName, `/IrrigationService/controlTraceSourceFertilization`, 'json', 'POST', { nodeId, control });
    }, 
    /**
     * 灌溉主页面-最近七天土壤墒情变化
     * @param {*} areaId 区域Id, 不传值代表查询所有区域
     * @returns Promise 
     */
    'cumulativeSoilMoistureListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '327fe8135feaf063bee974c0c20c8419aeef3ead', areaId)
            : execute(concreteModuleName, `/IrrigationService/cumulativeSoilMoistureInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉区域页面-区域基本信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'irrigationAreaInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b95d19991991c893136c380ba13e309ff74d9771', areaId)
            : execute(concreteModuleName, `/IrrigationService/irrigationAreaInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉主页面-最近七天累计灌溉统计
     * @param {*} areaId 区域Id, 不传值代表查询所有区域
     * @returns Promise 
     */
    'cumulativeIrrigationListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '65238cb74799bb04f38027f806958c6b78379a4e', areaId)
            : execute(concreteModuleName, `/IrrigationService/cumulativeIrrigationInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取某个区域下所有灌溉节点设备信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'irrigationNodeListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '49de43040fffcd79f6a6342c10e5fd460c2ac229', areaId)
            : execute(concreteModuleName, `/IrrigationService/irrigationNodeListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉区域页面-瞬时数据
     * @param {*} nodeId 节点Id, 不传值代表查询主通道
     * @returns Promise 
     */
    'instantaneousInformation': function (nodeId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e88bdc2fb7642bc151dd0d84da27085f29fb303c', nodeId)
            : execute(concreteModuleName, `/IrrigationService/instantaneousInformation`, 'json', 'POST', { nodeId });
    }, 
    /**
     * 灌溉主页面-正在灌溉地区
     * @returns Promise 
     */
    'irrigationWatering': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '28d171841008d6d00c876afee596c01c25cb20e3')
            : execute(concreteModuleName, `/IrrigationService/irrigationWatering`, 'json', 'GET');
    }, 
    /**
     * 灌溉历史数据（按次）
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'irrigationHistoryList': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'bd8009d13d3ed22a09225209209ffddcb64c2063', pageRequest)
            : execute(concreteModuleName, `/IrrigationService/irrigationHistoryList`, 'json', 'POST', pageRequest);
    }
}

export default IrrigationService

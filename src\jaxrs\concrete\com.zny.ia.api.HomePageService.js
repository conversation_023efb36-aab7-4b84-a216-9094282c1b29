/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const HomePageService = {
    /**
     * 报警汇总
     * @returns Promise 
     */
    'alarmCollect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '68335d020e466da6891bb2f8290b0eed02909b8d')
            : execute(concreteModuleName, `/HomePageService/alarmCollect`, 'json', 'GET');
    }, 
    /**
     * 区域详情
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'areaDetail': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '517e05f9050d3d92d7f0bcec5330a265893cea5d', areaId)
            : execute(concreteModuleName, `/HomePageService/areaDetail`, 'json', 'POST', { areaId });
    }, 
    /**
     * 农作物溯源信息
     * @returns Promise 
     */
    'homeCropSource': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0e069cdf7547104a8232702b6f5df34d681664ca')
            : execute(concreteModuleName, `/HomePageService/homeCropSource`, 'json', 'GET');
    }, 
    /**
     * 农作物实时价格
     * @returns Promise 
     */
    'homeCropPrice': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f4ce5dfc380adba0eda11adf528368ea83321708')
            : execute(concreteModuleName, `/HomePageService/homeCropPrice`, 'json', 'GET');
    }, 
    /**
     * 获取区域信息 （作物，边框，中心位置）
     * @returns Promise 
     */
    'findAreaCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e1307dc19f568b45614607a838b0ffe1834fb477')
            : execute(concreteModuleName, `/HomePageService/findAreaCrop`, 'json', 'GET');
    }, 
    /**
     * 电商销售占比
     * @returns Promise 
     */
    'homeMarketProportion': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0cb77f141f332709ac3e904a8c501f2768d0203e')
            : execute(concreteModuleName, `/HomePageService/homeMarketProportion`, 'json', 'GET');
    }, 
    /**
     * 智能物联数据
     * @returns Promise 
     */
    'homeUnionAIData': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'cd9b61fdad10df7bca4433004c26acc3c3b0f5cf')
            : execute(concreteModuleName, `/HomePageService/homeUnionAIData`, 'json', 'GET');
    }
}

export default HomePageService

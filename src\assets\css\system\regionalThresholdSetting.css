#regionalThresholdSetting {
  height: 100%;
}
#regionalThresholdSetting .regional {
  display: flex;
  width: 100%;
  height: 100%;
}
#regionalThresholdSetting .regional .regional_left {
  width: 232px;
  height: 100%;
  background: #ffffff;
  opacity: 1;
  border-radius: 0px;
}
#regionalThresholdSetting .regional .regional_left .regional_left_table {
  height: 83%;
  overflow-y: scroll;
}
#regionalThresholdSetting .regional .regional_left .regional_left_box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
  background: #ffffff;
  opacity: 1;
  border-radius: 0px;
  text-align: center;
}
#regionalThresholdSetting .regional .regional_left .regional_left_box .regional_left_text {
  width: 98%;
  height: 100%;
  line-height: 60px;
}
#regionalThresholdSetting .regional .regional_left .regional_left_box .regional_left_img {
  width: 8px;
  height: 8px;
  margin-right: 26px;
}
#regionalThresholdSetting .regional .regional_left .regional_left_box .regional_left_img img {
  position: absolute;
}
#regionalThresholdSetting .regional .regional_left .itemActive {
  background: #edf4fb;
  opacity: 1;
}
#regionalThresholdSetting .regional .regional_left .systemSearchButtonStyle2 {
  width: 140px;
  height: 40px;
  background: #0093bc;
  opacity: 1;
  border-radius: 6px;
  margin: auto;
  margin-top: 20px;
}
#regionalThresholdSetting .regional .regional_right {
  width: 1575px;
  height: 95%;
  background: #ffffff;
  opacity: 1;
  border-radius: 8px;
  margin: auto;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box {
  padding-left: 16px;
  box-sizing: border-box;
  padding-right: 36px;
  overflow: auto;
  height: 100%;
  padding-bottom: 34px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .systemFormStyle {
  height: 48px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button {
  display: flex;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button .regional_right_systemSearchButtonStyle2 {
  width: 140px;
  height: 40px;
  background: #0093bc;
  opacity: 1;
  border-radius: 6px;
  margin-top: 20px;
  margin-left: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button1 {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button1 .regional_right_systemSearchButtonStyle2 {
  width: 140px;
  height: 40px;
  background: #0093bc;
  opacity: 1;
  border-radius: 6px;
  margin-top: 20px;
  margin-left: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button1 .regional_right_systemSearchButtonStyle1 {
  width: 140px;
  height: 40px;
  background: #cccccc;
  opacity: 1;
  border-radius: 6px;
  margin-top: 20px;
  margin-left: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_button1 .regional_right_systemSearchButtonStyle1 .el-button {
  background: #cccccc;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_relevance {
  display: flex;
  margin-top: 30px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_relevance .regional_right_relevance_text {
  line-height: 48px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #333333;
  opacity: 1;
  margin-left: 20px;
  margin-right: 15px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_relevance .regional_right_relevance_text1 {
  line-height: 48px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #333333;
  opacity: 1;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_headline {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  opacity: 1;
  margin-top: 26px;
  margin-left: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_headline img {
  margin-left: 10px;
  vertical-align: middle;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_matter {
  display: flex;
  padding-left: 20px;
  margin-top: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_matter .regional_right_matter_box .regional_right_matter_box_header {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  opacity: 1;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_matter .regional_right_matter_box .regional_right_matter_box_row {
  display: flex;
  margin-top: 16px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_matter .regional_right_matter_box .regional_right_matter_box_row .regional_right_matter_box_row_text {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  opacity: 1;
  line-height: 40px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_matter .border {
  width: 1px;
  height: 85px;
  background: #dfdfdf;
  opacity: 1;
  border-radius: 0px;
  margin-left: 16px;
  margin-right: 24px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content {
  display: flex;
  flex-wrap: wrap;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 {
  display: flex;
  margin-top: 16px;
  margin-left: 20px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .text {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  line-height: 40px;
  color: #666666;
  opacity: 1;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .text img {
  margin-left: 10px;
  vertical-align: middle;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .right_input {
  width: 80px;
  height: 40px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .margin_left {
  margin-left: 14px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .margin_right {
  margin-right: 14px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content .regional_right_content_row1 .margin_right_left {
  margin-right: 14px;
  margin-left: 14px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 {
  display: flex;
  margin-left: 20px;
  flex-wrap: wrap;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 {
  display: flex;
  margin-top: 16px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .text {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  line-height: 40px;
  color: #666666;
  opacity: 1;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .text img {
  margin-left: 10px;
  vertical-align: middle;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .right_input {
  width: 80px;
  height: 40px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .margin_left {
  margin-left: 14px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .margin_right {
  margin-right: 14px;
}
#regionalThresholdSetting .regional .regional_right .regional_right_box .regional_right_content2 .regional_right_content_row1 .margin_right_left {
  margin-right: 14px;
  margin-left: 14px;
}
#regionalThresholdSetting .regional .cursor {
  cursor: pointer;
}
#regionalThresholdSetting .regional .thread {
  position: absolute;
  left: 0px;
  width: 6px;
  height: 60px;
  background: linear-gradient(150deg, #12aca7 0%, #0093bc 100%);
  opacity: 1;
  border-radius: 0px 5px 5px 0px;
}
#regionalThresholdSetting .close {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
}
#regionalThresholdSetting .right_input {
  width: 126px;
  height: 40px;
}
#regionalThresholdSetting .right_input .el-input__suffix {
  line-height: 40px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #999999;
  opacity: 1;
  right: 12px;
}
#regionalThresholdSetting .margin_left {
  margin-left: 16px;
}
#regionalThresholdSetting .margin_right {
  margin-right: 24px;
}
#regionalThresholdSetting .margin_right_left {
  margin-right: 24px;
  margin-left: 16px;
}
#regionalThresholdSetting .btnBox {
  display: flex;
  justify-content: center;
}
#regionalThresholdSetting .btnBox .btnItem {
  width: 160px;
  height: 40px;
}
#regionalThresholdSetting .btnBox .systemSearchButtonStyle2 {
  margin-left: 30px;
}

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const BatchService = {
    /**
     * 总览批次信息
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchOverviewListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '21064e5ad0d29c76d2dc96fd22cca7aaa3ed8dee', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchOverviewListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 填报收获批次产量
     * @param {*} harvestId 收获批次号
     * @param {*} production 产量
     * @returns Promise 
     */
    'fillInProduction': function (harvestId, production) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3668a446fb4b82babfd40ce5a669a6deaceb4209', {harvestId, production})
            : execute(concreteModuleName, `/BatchService/fillInProduction`, 'json', 'POST', { harvestId, production });
    }, 
    /**
     * 生成收获批次
     * @param {*} harvestBatchPo 收获批次信息
     * @returns Promise 
     */
    'generateHarvestBatch': function (harvestBatchPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f55c9fbe727034b80ef489f598b7a2d7dad2ab1a', harvestBatchPo)
            : execute(concreteModuleName, `/BatchService/generateHarvestBatch`, 'text', 'POST', harvestBatchPo);
    }, 
    /**
     * 结束种植批次
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'closedBatch': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ef494f1e3b1daff02034851e7efe129b066e8667', batchId)
            : execute(concreteModuleName, `/BatchService/closedBatch`, 'json', 'POST', { batchId });
    }, 
    /**
     * 种植批次是否有收获批次
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'checkHarvestBatch': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a56f9f9905d4ef2dc624bfe1e1a58c3eeba207f0', batchId)
            : execute(concreteModuleName, `/BatchService/checkHarvestBatch`, 'json', 'POST', { batchId });
    }, 
    /**
     * 生成种植批次
     * @param {*} generateBatchPo 生成种植批次信息
     * @returns Promise 
     */
    'generateBatch': function (generateBatchPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a112bd9e42a642b963b6562d5331000aab551830', generateBatchPo)
            : execute(concreteModuleName, `/BatchService/generateBatch`, 'text', 'POST', generateBatchPo);
    }, 
    /**
     * 批次历史记录
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchHistoryListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b1b4520e95d95bfde6610b410f327a6d0b89e9f1', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchHistoryListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 生成种植批次时能够选择的区域
     * @returns Promise 
     */
    'generateBatchChooseArea': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '89c4786a686a2855a9c8910c273d55113e1dbcac')
            : execute(concreteModuleName, `/BatchService/generateBatchChooseArea`, 'json', 'GET');
    }, 
    /**
     * 区域批次信息
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'batchAreaListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0bd3b87aeddb72d12bc24a9f5fe0e4e5da396147', pageRequest)
            : execute(concreteModuleName, `/BatchService/batchAreaListInformation`, 'json', 'POST', pageRequest);
    }, 
    /**
     * 批次详情
     * @param {*} batchId 种植批次号
     * @returns Promise 
     */
    'batchInformationByBatchId': function (batchId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2aa535cfe629009d3f414a13fcdcb7bc5d8996d3', batchId)
            : execute(concreteModuleName, `/BatchService/batchInformationByBatchId`, 'json', 'POST', { batchId });
    }, 
    /**
     * 填报收获批次产值
     * @param {*} harvestId 收获批次号
     * @param {*} outputValue 产值
     * @returns Promise 
     */
    'fillInOutputValue': function (harvestId, outputValue) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7185f959bca9b2620ba7ff4191e597be34b9a4e2', {harvestId, outputValue})
            : execute(concreteModuleName, `/BatchService/fillInOutputValue`, 'json', 'POST', { harvestId, outputValue });
    }, 
    /**
     * 生成结束种植批次-根据作物查询种植该作物的区域以及对应种植批次号
     * @param {*} crop 作物类型
     * @returns Promise 
     */
    'areaAndBatchByCorp': function (crop) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '414ca30c27b62179c90ff6ef3596282b488bd1b2', crop)
            : execute(concreteModuleName, `/BatchService/areasByCorp`, 'json', 'POST', { crop });
    }
}

export default BatchService

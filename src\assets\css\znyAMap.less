.AMapContainer{
  width: 100%;
  height: 100%;
  .infoWindow{
    background: rgba(0,69,61,.8);
    border: 1px solid transparent;
    position: relative;
    font-size: 14px;
    font-weight: 400;
    color: #DFEEF3;
    .top_left{
      position: absolute;
      top: -7px;
      left: -8px;
    }
    .top_right{
      position: absolute;
      top: -8px;
      right: -9px;
    }
    .bottom_left{
      position: absolute;
      bottom: -13px;
      left: -9px;
    }
    .bottom_right{
      position: absolute;
      bottom: -13px;
      right: -9px;
    }
  }
  .areaDetail_infoWindow{
    width: 264px;
    height: 280px;
    .iw_detailsList{
      width: 224px;
      height: 240px;
      margin: 20px;
      .iw_detailsItem{
        .planHomeVoList{
          height: 40px;
          // overflow: scroll;
          .planHomeItem{
            display: flex;
            margin-top: 5px;
            div:first-child{
              margin-right: 25px;
            }
          }
        }
      }
      .iw_detailsItem:nth-child(5){
        height: 38px;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .iw_detailsItem:not(:first-child){
        margin-top: 8px;
      }
    }
  }
  .equipment_infoWindow{
    width: 220px;
    height: 230px;
    font-size: 14px;
    .iw_title{
      width: 220px;
      text-align: center;
      margin: 16px auto 10px;
    }
    .iw_con{
      width: 190px;
      height: 165px;
      margin: auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}
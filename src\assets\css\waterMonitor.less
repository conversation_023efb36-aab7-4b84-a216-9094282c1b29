/* 设置表主体的高度 */
.table_left::v-deep .el-table__body {
    border-collapse: separate;
    border-spacing: 0 10px;
}
.mask_layer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
    margin: 0;
    z-index: 100;
}

.table_header_box {
    display: flex;
    flex-wrap: wrap;
    .table_header_box_row1 {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 16px;
        .row1_text {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #00f4da;
            margin: auto;
            margin-right: 16px;
        }
    }
}
.pageChange {
    position: relative;
    top: 20px;
    .el-pagination {
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0px);
    }
}
.meteorology_left_smartIOTData,
.soil_left_smartIOTData,
.InsectSituation_left_smartIOTData {
    height: 100%;
}
.meteorology_left_smartIOTData_con,
.soil_left_smartIOTData_con,
.InsectSituation_left_smartIOTData_con {
    height: 96%;
    background: rgba(1, 13, 23, 0.2);
}
.table_header {
    width: 461px;
    margin-top: 20px;
    display: flex;
    height: 36px;
    background: linear-gradient(0deg, rgba(0, 245, 255, 0.2), rgba(42, 203, 178, 0));
    .item {
        width: 33%;
        padding: 8px 0;
        text-align: center;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #dfeef3;
    }
}
.item_color {
    color: #ee0101;
}

.farming {
    // padding: 0px 29px;
    width: 100%;
    box-sizing: border-box;
    .farming_position {
        width: 100%;
        position: absolute;
        z-index: 2;
        padding: 0px 29px;
        box-sizing: border-box;
    }
    .eppoWisdom {
        width: 230px;
        background: url('../../assets/image/eppoWisdom/eppoWisdom2.png') no-repeat;
        background-size: 100% 100%;
        font-size: 15px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        // color: #00fcff;
        color: #f3fcff;
        margin-top: 30px;
        height: 28px;
        line-height: 28px;
        padding-left: 15px;
        margin-bottom: 18px;
    }
    .recentEppo {
        display: flex;
        // justify-content: space-between;
        :nth-child(n + 2) {
            margin-left: 10px;
        }
        .recentEppo-box {
            width: 240px;
            height: 111px;
            background: url('../../assets/image/eppoWisdom/recentEppo1.png') no-repeat;
            text-align: center;
            .recentEppo-time {
                font-size: 30px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #00fcff;
                margin-top: 13px;
            }
            .recentEppo-area {
                width: 135px;
                height: 28px;
                background: linear-gradient(90deg, #006ae1, #16b7ff);
                border-radius: 4px 4px 4px 4px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #ffffff;
                line-height: 28px;
                text-align: center;
                margin: auto;
                margin-top: 18px;
                cursor: pointer;
            }
        }
    }
    .meteorology_right {
        margin: 0;
        .eppoWisdom_twoDimensionalMap {
            width: 950px;
            height: 545px;
            background: url('../../assets/image/monitoringCenter/ploat.png') no-repeat;
            background-size: 100%;
            margin: 62px 0px 0px 19px;
        }
    }
}
.waterMonitorDialog ::v-deep .el-dialog {
    height: 866px;
    margin-top: 116px !important;
}
.waterMonitor .meteorologyEMDialog ::v-deep .el-dialog {
    .el-dialog__body {
        height: 0px;
        .handleBox_item {
            display: flex;
            .status {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #feffff;
                position: relative;
                top: 8px;
            }
        }
    }
    .clone {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .header-text {
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #dfeef3;
        position: absolute;
        top: 22px;
        left: 300px;
    }
    .wire {
        height: 2px;
        background: radial-gradient(circle, #00f4fd, rgba(0, 244, 253, 0));
    }
    .waternumber_content {
        overflow-y: scroll;
        height: 880px;
        .waternumber_content_box {
            margin-top: 20px;
            margin-bottom: 10px;
            display: flex;
            box-sizing: border-box;
            padding-left: 43px;
            .status {
                // margin: auto;
                margin-top: 41px;
                margin-right: 24px;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #dfeef3;
            }
        }
    }
    .waternumber_button {
        display: flex;
        margin-top: 20px;
        justify-content: center;
        .cancel_button {
            width: 120px;
            height: 42px;
            background: rgba(1, 18, 21, 0);
            box-shadow: 0px 0px 10px 0px rgba(0, 244, 253, 0.5) inset;
            border-radius: 2px 4px 4px 4px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #ffffff;
            line-height: 42px;
            text-align: center;
            cursor: pointer;
        }
        .confirm_button {
            margin-left: 60px;
            width: 120px;
            height: 42px;
            background: rgba(1, 18, 21, 0);
            box-shadow: 0px 0px 10px 0px rgba(243, 161, 52, 0.8) inset;
            border-radius: 2px 4px 4px 4px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #ffffff;
            line-height: 42px;
            text-align: center;
            cursor: pointer;
        }
    }
    .el-row {
        margin-top: 16px;
        .row_text {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            margin-top: 10px;
        }
    }
    .content {
        box-sizing: border-box;
        padding-top: 23px;
        .centre_img_box {
            height: 830px;
            overflow: auto;
            width: 100%;
            :nth-child(1) {
                margin-top: 0px;
            }
            img {
                width: 100%;
                height: 300px;
                margin-top: 20px;
                object-fit: cover;
            }
        }
        .row1_time {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
        }
        .row_wire {
            width: 1px;
            height: 729px;
            background: radial-gradient(circle, rgba(199, 206, 216, 0.8), rgba(199, 206, 216, 0));
            margin: auto;
        }
        .table {
            margin-top: 30px;
        }
        .datasources {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            float: right;
            margin-top: 20px;
        }
    }
}
// el-table样式
.systemTableStyle {
    ::v-deep .el-table th.el-table__cell > .cell {
        color: #dfeef3 !important;
    }
}

// 表头的样式
::v-deep .el-table th {
    // background: rgba($color: #4ec7e1, $alpha: 0.2);
    text-align: center;
    color: #49c3e3;
    font-size: 16px;
    border-bottom: 0px solid #dfe6ec !important;
}
// 行和列的样式
.table_left::v-deep .el-table tr,
.table_left::v-deep .el-table td {
    // background-color: #003d42 !important;
    border: none;
}
.table_right::v-deep .el-table tr,
.table_right::v-deep .el-table td {
    // background-color: #003d42 !important;
    border: none;
}
.el-table::before {
    height: 0px;
}

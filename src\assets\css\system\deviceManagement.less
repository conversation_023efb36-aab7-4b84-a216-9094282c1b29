.deviceManagement{
  width: 100%;
  height: 96%;
  &_dataList{
    margin: 32px 0 20px 32px;
    display: flex;
    align-items: center;
    .dataList_item{
      width: 210px;
      height: 92px;
      // line-height: 92px;
      background: #FFFFFF;
      box-shadow: 6px 6px 54px rgba(0,0,0,0.05);
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;
      &_text{
        margin-left: 16px;
        div:nth-child(1){
          font-size: 16px;
          font-weight: 400;
          color: #333333;
        }
        div:nth-child(2){
          font-size: 28px;
          font-weight: bold;
          color: #333333;
        }
      }
      &_img{
        vertical-align: middle;
        margin-right: 16px;
      }
    }
  }
  &_con{
    width: 96%;
    height: 84%;
    background: #FFFFFF;
    border-radius: 8px;
    margin: 20px 0 0 32px;
    .handleBox,.handleBox1{
      display: flex;
      align-items: center;
      &_item{
        height: 40px;
        margin-top: 24px;
        margin-right: 24px;
      }
      &_item:nth-child(1){
        margin-left: 24px;
      }
      &_item:nth-child(1),
      &_item:nth-child(2),
      &_item:nth-child(3){
        width: 270px;
        
      }
      &_item:nth-child(4),
      &_item:nth-child(5){
        width: 80px;
      }
      &_item:nth-child(6){
        width: 100px;
      }
    }
    .handleBox1{
      .handleBox_btn{
        margin-top: 24px;
      }
    }
    .tableBox{
      width: 97%;
      height: 79%;
      margin: auto;
      margin-top: 24px;
      .cell{
        .viewData{
          font-size: 15px;
          font-weight: 400;
          color: #007EFF;
          cursor: pointer;
          margin: 0 8px;
        }
      }
    }
    .pageBox{
      width: 97%;
      height: 5%;
      margin-top: 10px;
      text-align: center;
    }
  }
  .dataRecordIntervalDialog{
    .el-dialog{
      height: 770px;
      margin-top: 10vh !important;
      .el-dialog__body{
        height: 675px;
        .formList{
          height: 630px;
          overflow: scroll;
          .systemFormStyle{
            width: 281px;
            height: 48px;
          }
          .el-form-item:nth-child(2),
          .el-form-item:nth-child(3){
            .el-form-item__content{
              position: relative;
              .tips{
                position: absolute;
                top: -38px;
                left: 73px;
                height: 12px;
                font-size: 12px;
                font-weight: 400;
                color: #FF6363;
                img{
                  width: 10px;
                  height: 10px;
                }
              }
              div:nth-child(2){
                display: flex;
                align-items: center;
                font-size: 15px;
                font-weight: 400;
                color: #999999;
                .systemFormStyle{
                  width: 80px;
                  height: 48px;
                  .el-input.el-input--suffix{
                    line-height: 48px;
                  }
                }
              }
            }
    
          }
          .el-form-item:nth-child(4),
          .el-form-item:nth-child(5){
            .el-form-item__content{
              .input_list{
                display: flex;
                font-size: 15px;
                font-weight: 400;
                color: #999999;
                .systemFormStyle{
                  width: 80px;
                  height: 48px;
                }
                .list_item{
                  margin-bottom: 12px;
                  span{
                    margin: 0 5px;
                  }
                  img{
                    vertical-align: middle;
                  }
                }
              }
            }
          }
          .el-form-item:nth-child(4){
            .el-form-item__content{
              position: relative;
              .tips{
                position: absolute;
                top: -34px;
                left: 45px;
                img{
                  width: 18px;
                  height: 18px;
                }
              }
            }
          }
          .el-form-item:nth-child(5){
            .el-form-item__content{
              position: relative;
              .tips{
                position: absolute;
                top: -34px;
                left: 72px;
                img{
                  width: 18px;
                  height: 18px;
                }
              }
            }
          }
        }
        .btnBox{
          margin-top: 12px;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 160px;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }
  .equipmentDialog{
    .el-dialog{
      height: 548px;
      margin-top: 20vh !important;
      .el-dialog__header{
        padding: 0;
      }
      .el-dialog__body{
        height: 550px;
        .form_title{
                font-size: 18px;
                font-weight: bold;
                color: #333333;
                text-align: center;
                margin-bottom: 44px;
              }
        .formList{
          height: 440px;
          .el-form{
            .formItem{
              display: flex;

            }
            .formItem:nth-child(1){
              .el-form-item:nth-child(2),
              .el-form-item:nth-child(3){
                margin-left: 78px;
              }
            }
            .formItem:nth-child(2),
            .formItem:nth-child(3){
              .el-form-item:nth-child(2){
                margin-left: 78px;
                .el-input{
                  width: 638px;
                }
              }
            }
            .formItem:nth-child(4){
              .el-input{
                width: 996px;
              }
            }
            .el-input{
              width: 280px;
              height: 48px;
            }
          }
        }
        .btnBox{
          margin-top: 12px;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 160px;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }

  // 设备动态弹窗样式
  .deviceDynamicDialog{
    .el-dialog{
      height: 900px;
      margin-top: 1vh !important;

      .el-dialog__header{
        padding: 20px 24px 12px 24px;
        background: #ffffff;
        border-bottom: 1px solid #f0f0f0;

        .el-dialog__title{
          font-size: 18px;
          font-weight: 500;
          color: #333333;
        }
      }

      .el-dialog__body{
        height: 700px;
        padding: 20px 24px 30px;

        .deviceDynamicContent{
          height: 100%;
          display: flex;
          flex-direction: column;

          .tableBox{
            flex: 1;
            margin-bottom: 10px;

            .el-table{
              height: 100% !important;

              .el-table__header-wrapper{
                .el-table__header{
                  th{
                    background: #f8f9fa;
                    color: #333333;
                    font-weight: 500;
                  }
                }
              }

              .el-table__body-wrapper{
                max-height: 720px;
                overflow-y: auto;

                .el-table__row{
                  &:hover{
                    background: #f5f7fa;
                  }
                }
              }
            }
          }

          .pageBox{
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
}
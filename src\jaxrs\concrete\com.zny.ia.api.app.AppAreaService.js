/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppAreaService = {
    /**
     * 获取某区域下某种设备信息
     * @param {*} areaId 区域Id
     * @param {*} constantId 设备类型Id
     * @returns Promise 
     */
    'areaEquipmentListInformation': function (areaId, constantId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a2b88211be07314cd0663a312ac58428d3e7dbd7', {areaId, constantId})
            : execute(concreteModuleName, `/App/AreaService/areaEquipmentListInformation`, 'json', 'POST', { areaId, constantId });
    }
}

export default AppAreaService

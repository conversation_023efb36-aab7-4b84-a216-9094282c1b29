/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const EquipmentCommunicationService = {
    /**
     * 设备控制，返回值：订阅号，后续根据此订阅号获取指令执行结果
     * @param {*} equipmentCtrlVO 设备控制参数
     * @returns Promise 
     */
    'equipmentCtrl': function (equipmentCtrlVO) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '556d3cfd7a66c1fbe8ff241fed0a1cc126720034', equipmentCtrlVO)
            : execute(concreteModuleName, `/EquipmentCommunicationService/equipmentCtrl`, 'json', 'POST', equipmentCtrlVO);
    }, 
    /**
     * 根据结果请求id获取结果 4-已发送 1-已超时 2-成功 3-失败 
     * @param {*} id 
     * @returns Promise 
     */
    'gainCMDResult': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1efa67a3a02863392b7e9218f71bac5713cae630', id)
            : execute(concreteModuleName, `/EquipmentCommunicationService/gainCMDResult`, 'json', 'POST', { id });
    }
}

export default EquipmentCommunicationService

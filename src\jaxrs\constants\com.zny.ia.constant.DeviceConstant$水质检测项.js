import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: pH值
     * desc: --
     * value: 0
     */
    pH值: 0,
    /**
     * label: 水温
     * desc: --
     * value: 1
     */
    水温: 1,
    /**
     * label: 悬浮物
     * desc: --
     * value: 2
     */
    悬浮物: 2,
    /**
     * label: 五日生化需氧量
     * desc: --
     * value: 3
     */
    五日生化需氧量: 3,
    /**
     * label: 化学需氧量
     * desc: --
     * value: 4
     */
    化学需氧量: 4,
    /**
     * label: 阴离子表面活性剂
     * desc: --
     * value: 5
     */
    阴离子表面活性剂: 5,
    /**
     * label: 氯化物
     * desc: --
     * value: 6
     */
    氯化物: 6,
    /**
     * label: 硫化物
     * desc: --
     * value: 7
     */
    硫化物: 7,
    /**
     * label: 全盐量
     * desc: --
     * value: 8
     */
    全盐量: 8,
    /**
     * label: 总铅
     * desc: --
     * value: 9
     */
    总铅: 9,
    /**
     * label: 总镉
     * desc: --
     * value: 10
     */
    总镉: 10,
    /**
     * label: 六价铬
     * desc: --
     * value: 11
     */
    六价铬: 11,
    /**
     * label: 总汞
     * desc: --
     * value: 12
     */
    总汞: 12,
    /**
     * label: 总砷
     * desc: --
     * value: 13
     */
    总砷: 13,
    /**
     * label: 粪大肠菌群数
     * desc: --
     * value: 14
     */
    粪大肠菌群数: 14,
    /**
     * label: 蛔虫卵数
     * desc: --
     * value: 15
     */
    蛔虫卵数: 15,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 叶片卷叶
     * desc: --
     * value: 0
     */
    叶片卷叶: 0,
    /**
     * label: 叶片上挺
     * desc: --
     * value: 1
     */
    叶片上挺: 1,
    /**
     * label: 叶片稀薄
     * desc: --
     * value: 2
     */
    叶片稀薄: 2,
    /**
     * label: 叶片皱缩
     * desc: --
     * value: 3
     */
    叶片皱缩: 3,
    /**
     * label: 叶片黄斑
     * desc: --
     * value: 4
     */
    叶片黄斑: 4,
    /**
     * label: 穗粒秃尖
     * desc: --
     * value: 5
     */
    穗粒秃尖: 5,
    /**
     * label: 叶片下垂耷拉
     * desc: --
     * value: 6
     */
    叶片下垂耷拉: 6,
    /**
     * label: 叶片短少
     * desc: --
     * value: 7
     */
    叶片短少: 7,
    /**
     * label: 穗粒缺失半面
     * desc: --
     * value: 8
     */
    穗粒缺失半面: 8,
    /**
     * label: 植株宽大
     * desc: --
     * value: 9
     */
    植株宽大: 9,
    /**
     * label: 叶片稀疏
     * desc: --
     * value: 10
     */
    叶片稀疏: 10,
    /**
     * label: 叶片萎蔫
     * desc: --
     * value: 11
     */
    叶片萎蔫: 11,
    /**
     * label: 结荚少
     * desc: --
     * value: 12
     */
    结荚少: 12,
    /**
     * label: 叶面白斑
     * desc: --
     * value: 13
     */
    叶面白斑: 13,
    /**
     * label: 叶片洞穿成排状
     * desc: --
     * value: 14
     */
    叶片洞穿成排状: 14,
    /**
     * label: 叶尖变红
     * desc: --
     * value: 15
     */
    叶尖变红: 15,
    /**
     * label: 紫鞘黄叶
     * desc: --
     * value: 16
     */
    紫鞘黄叶: 16,
    /**
     * label: 正常
     * desc: --
     * value: 17
     */
    正常: 17,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
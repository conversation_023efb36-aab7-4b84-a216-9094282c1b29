<template>
    <div class="irrigationDrainage">
        <div class="soil_left">
            <!-- 区域概况 -->
            <div class="soil_left_smartIOTData">
                <div class="title soil_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>遥感作物分布</span>
                </div>
                <div class="soil_left_smartIOTData_con">
                    <remoteSensEchart />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 智慧植保预防 -->
            <div class="soil_left_equipmentAlarm" style="height:294px">
                <div class="title soil_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>作物长势</span>
                </div>
                <div class="soil_left_equipmentAlarm_con" style="height:260px">
                    <znyEchart :chartData="cropsGrow" :chartType="'line'"></znyEchart>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <div class="soil_left_equipmentAlarm" style="height:217px">
                <div class="title soil_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>遥感相关历史</span>
                </div>
                <div class="soil_left_equipmentAlarm_con" style="height:176px">
                    <div class="remoteSens_content" id="scroll_box">
                        <vue-seamless-scroll
                            :data="queryssList"
                            :class-option="classOption"
                            class="seamless-warp"
                            style="width: 100%"
                        >
                            <div>
                                <div
                                    class="remoteSens_box"
                                    v-for="item in queryssList"
                                    :key="item.length"
                                    @click="historicalDetails(item.record)"
                                >
                                    <img :src="item.picUrl" alt="" class="box_left" />
                                    <div class="box_right">
                                        <div class="box_right_text box_right_row1">{{ item.dateTime }}拍摄</div>
                                        <div class="box_right_text box_right_row2">相关农作物：</div>

                                        <div class="box_right_text box_right_row3">
                                            <span v-for="(item, index) in item.cropList" :key="index">
                                                <span v-if="index !== 0">,{{ item | cropFormat }}</span>
                                                <span v-if="index == 0">{{ item | cropFormat }}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </vue-seamless-scroll>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="soil_right">
            <div class="title soil_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="soil_right_con">
                <div class="farming">
                    <div class="farming_row1">
                        <div class="farming_row1_left">
                            相关农作物：
                            <span v-for="(item, index) in mainlist.cropList" :key="index">
                                <span v-if="index !== 0">,{{ item | cropFormat }}</span>
                                <span v-if="index == 0">{{ item | cropFormat }}</span>
                            </span>
                        </div>
                        <div class="farming_row1_right" @click="uploadingimg" v-if="areaIsJudge">上传图片</div>
                    </div>
                    <div class="farming_row2">
                        <img :src="mainlist.picUrl" alt="" class="farming_row2_img" />
                        <div class="farming_row2_box">
                            <div class="box_title">颜色说明：</div>
                            <div class="box_row1">
                                <div class="box_row1_color"></div>
                                <div class="box_row1_text">0~10</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row2_color"></div>
                                <div class="box_row1_text">11~20</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row3_color"></div>
                                <div class="box_row1_text">21~30</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row4_color"></div>
                                <div class="box_row1_text">31~40</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row5_color"></div>
                                <div class="box_row1_text">41~50</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row6_color"></div>
                                <div class="box_row1_text">51~60</div>
                            </div>
                            <div class="box_row1">
                                <div class="box_row7_color"></div>
                                <div class="box_row1_text">61~100</div>
                            </div>
                        </div>
                    </div>
                    <div class="farming_row3">{{ mainlist.dateTime }}</div>
                    <div class="farming_row4">
                        <div class="farming_row4_col1">遥感分析结果</div>
                        <div class="farming_row4_col2" @click="openanalysedialogVisible" v-if="areaIsJudge">人工补充</div>
                    </div>
                    <div class="farming_row5">{{ mainlist.description }}</div>
                </div>
            </div>
        </div>
        <!-- 上传图片 -->
        <div class="meteorologyEMDialog dialogStyle waterMonitorDialog">
            <el-dialog
                title="上传遥感图片"
                width="35%"
                :visible.sync="dialogVisibles"
                :modal-append-to-body="false"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                center
                v-if="dialogVisibles"
            >
                <img src="../assets/image/eppoWisdom/close.png" alt="" class="clone" @click="dialogVisibles = false" />
                <div class="wire"></div>
                <div class="content">
                    <div class="content_overflow">
                        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
                            <div class="formList">
                                <el-form-item label="添加时间：" class="form_item2 formStyle" prop="dateTime">
                                    <el-date-picker
                                        v-model="form.dateTime"
                                        type="date"
                                        placeholder="选择日期"
                                        style="height:40px"
                                        format="yyyy 年 MM 月 dd 日"
                                        value-format="yyyy-MM-dd"
                                        popper-class="datePickerStyle"
                                        @change="toChooseTime"
                                    ></el-date-picker>
                                </el-form-item>

                                <el-form-item label="相关区域：" class="formStyle" prop="areaIdList">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-checkbox
                                                :indeterminate="isIndeterminate"
                                                v-model="checkAll"
                                                @change="handleCheckAllChange"
                                            >
                                                全部区域
                                            </el-checkbox>
                                        </el-col>
                                        <el-col :span="14">
                                            <el-checkbox-group
                                                v-model="form.areaIdList"
                                                @change="handleCheckedCitiesChange"
                                            >
                                                <el-checkbox
                                                    v-for="(city, index) in cities"
                                                    :label="city.areaId"
                                                    :key="index"
                                                >
                                                    {{ city.areaName }}
                                                </el-checkbox>
                                            </el-checkbox-group>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                                <el-form-item
                                    label="作物长势："
                                    class="form_item2 formStyle"
                                    prop="verification"
                                ></el-form-item>
                                <!-- <el-form-item label="作物长势:" class="formStyle" prop="verification"></el-form-item> -->
                                <div
                                    class="content_flex formStyle"
                                    v-for="(item, index) in form.heightList"
                                    :key="index"
                                >
                                    <span class="text">{{ item.type | cropFormat }}:</span>
                                    <el-input
                                        v-model="item.height"
                                        style="height:40px;width:80%"
                                        @change="verificationchange(item.height)"
                                        @input="changePricess(index)"
                                    >
                                        <i slot="suffix" style="display: flex;padding: 10px">
                                            cm
                                        </i>
                                    </el-input>
                                </div>

                                <el-form-item label="遥感图片：" class="formStyle" prop="pic">
                                    <el-upload
                                        class="upload-demo"
                                        accept=".jpg,.jpeg,.png,.JPG,.PNG"
                                        action=""
                                        multiple
                                        :limit="1"
                                        :auto-upload="false"
                                        list-type="picture-card"
                                        :on-exceed="handleExceed"
                                        :on-change="handleChange"
                                        :on-remove="handleRemove"
                                    >
                                        <i class="el-icon-plus"></i>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="图片分析：" class="formStyle">
                                    <el-input v-model="form.description" type="textarea" :rows="5"></el-input>
                                </el-form-item>
                            </div>
                        </el-form>
                    </div>

                    <div class="waternumber_button">
                        <div class="cancel_button" @click="dialogVisibles = false">取消</div>
                        <div class="confirm_button" @click="submitToUploadPictures">提交</div>
                    </div>
                </div>
            </el-dialog>
        </div>
        <!-- 遥感分析结果 -->
        <div class="meteorologyEMDialog dialogStyle  analyseDialog ">
            <el-dialog
                title="遥感分析结果"
                width="25%"
                :visible.sync="analysedialogVisible"
                :modal-append-to-body="false"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                center
            >
                <img
                    src="../assets/image/eppoWisdom/close.png"
                    alt=""
                    class="clone"
                    @click="analysedialogVisible = false"
                />
                <div class="wire"></div>
                <div class="content">
                    <div class="formList">
                        <div class="formStyle">
                            <el-input
                                v-model="artificialSupplementaryform.description"
                                type="textarea"
                                :rows="8"
                            ></el-input>
                        </div>
                    </div>

                    <div class="waternumber_button">
                        <div class="cancel_button" @click="analysedialogVisible = false">取消</div>
                        <div class="confirm_button " @click="artificialSupplementary">提交</div>
                    </div>
                </div>
            </el-dialog>
        </div>
        <!-- 查看大图 -->
        <div class="meteorologyEMDialog dialogStyle checkDialog">
            <el-dialog
                :title="historicalDetailstitle"
                width="50%"
                :visible.sync="checkdialogVisible"
                :modal-append-to-body="false"
                :show-close="false"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
            >
                <img
                    src="../assets/image/eppoWisdom/close.png"
                    alt=""
                    class="clone"
                    @click="checkdialogVisible = false"
                />
                <div class="wire"></div>
                <div class="content">
                    <div class="content_imgbox">
                        <img
                            src="../assets/image/eppoWisdom/zuobian.png"
                            alt=""
                            class="zuobian"
                            @click="cutimgbutton(1)"
                        />
                        <img
                            :src="historicalDetailsform.picUrl[cutimgnumber]"
                            alt=""
                            v-if="historicalDetailsform.picUrl"
                            class="content_img"
                        />
                        <img
                            src="../assets/image/eppoWisdom/youbian.png"
                            alt=""
                            class="youbian"
                            @click="cutimgbutton(2)"
                        />
                    </div>
                    <div class="content_row1">
                        <div class="content_row1_col1">相关作物：</div>
                        <div class="content_row1_col2">
                            <span v-for="(item, index) in historicalDetailsform.cropList" :key="index">
                                <span v-if="item == 0 && index !== 0">,大豆</span>
                                <span v-if="item == 1 && index !== 0">,小麦</span>
                                <span v-if="item == 2 && index !== 0">,玉米</span>
                                <span v-if="item == 3 && index !== 0">,水稻</span>
                                <span v-if="item == 0 && index == 0">大豆</span>
                                <span v-if="item == 1 && index == 0">小麦</span>
                                <span v-if="item == 2 && index == 0">玉米</span>
                                <span v-if="item == 3 && index == 0">水稻</span>
                            </span>
                        </div>
                    </div>
                    <div class="content_row1_col1 content_row2_col1">遥感分析结果</div>
                    <div class="content_row1_col2 content_row2_col2">
                        {{ historicalDetailsform.description }}
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import remoteSensEchart from '../components/remoteSensEchart.vue'
import RemoteSensingService from '../jaxrs/concrete/com.zny.ia.api.RemoteSensingService.js'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'

// import $ from 'jquery'
import { chart } from '../js/chart'
export default {
    name: 'irrigationDrainage',
    components: {
        remoteSensEchart,
    },
    data() {
        return {
            areaIsJudge:true,//判断当前用户能否操作该区域
            classOption: {
                step: 0.5,
                limitMoveNum: 3,
            },
            showScroll2: false,
            Scroll: null,
            timer: null, //用来接收setInterval()返回的 ID 值
            cropsGrow: {
                //氮磷钾折线图数据
                show: true,
                option: {},
            },
            optionPie: {
                show: true,
                option: {},
            }, //图表数据
            dialogVisibles: false,
            form: {
                areaIdList: [],
                pic: [],
                heightList: [],
                verification: '',
            },
            heightList: [],
            checkAll: false,
            checkedCities: [],
            cities: [],
            isIndeterminate: false,
            analysedialogVisible: false,
            checkdialogVisible: false,
            queryssList: [],
            historicalDetailsform: {},
            historicalDetailstitle: '',
            cutimgnumber: 0,
            mainlist: {},
            rules: {
                dateTime: [{ required: true, message: '请选择时间', trigger: 'blur' }],
                areaIdList: [{ required: true, message: '请选择相关区域', trigger: 'change' }],
                pic: [{ required: true, message: '请上传遥感图片', trigger: 'change' }],
                verification: [{ required: true, message: '请输入作物长势', trigger: 'blur' }],
            },
            areaId: [],
            artificialSupplementaryform: {
                record: undefined,
                description: '',
            },
        }
    },
    created() {
        this.relatedHistory()
        this.accessToPublicAreas()
        this.areaJudge()
    },
    mounted() {
        chart.cropsGrow().then(data => {
            this.cropsGrow = data
        })
    },
    // computed: {
    //     listenChange() {
    //         const { areaIdList, dateTime } = this.form
    //         return { areaIdList, dateTime }
    //     },
    // },
    methods: {
        // 判断当前用户能否操作该区域
        areaJudge(){
            let role = localStorage.getItem('userRoles')
            if(role==2){
                this.areaIsJudge=false
            }else{
                this.areaIsJudge=true
            }
        },
        changePricess(name) {
            this.form.heightList[name].height = this.form.heightList[name].height.replace(/[^0-9.]/g, '')

            this.form.heightList[name].height = this.form.heightList[name].height.replace(/\.{2,}/g, '.') //清除第二个小数点
        },
        verificationchange(value) {
            this.form.verification = value
        },
        toChooseTime() {
            if (this.form.areaIdList.length !== 0 && this.form.dateTime) {
                this.forCropSpecies()
            }
        },
        // 根据时间和区域获取作物种类
        forCropSpecies() {
            RemoteSensingService.listCropType(this.form.dateTime, this.form.areaIdList).then(res => {
                let heightList = res.map(item => {
                    return {
                        type: item,
                        height: '',
                    }
                })
                this.form.heightList = heightList
            })
        },
        // 遥感相关历史
        relatedHistory() {
            RemoteSensingService.listRemoteHistory(1, 100).then(res => {
                this.queryssList = res.list
                this.historicalDetails(res.list[0].record, 1)
            })
        },
        accessToPublicAreas() {
            CommonService.allAreaOfCurrentUserManage().then(res => {
                this.cities = res
                this.areaId = res.map(item => item.areaId)
            })
        },
        // 遥感相关历史详情
        historicalDetails(record, value) {
            RemoteSensingService.detailRemote(record).then(res => {
                if (value == 1) {
                    this.mainlist = res
                    this.mainlist.picUrl = this.mainlist.picUrl[0]
                    return
                }
                this.cutimgnumber = 0
                this.historicalDetailsform = res
                this.historicalDetailstitle = `${res.dateTime} 遥感图片`
                this.checkdialogVisible = true
            })
        },
        // 遥感相关历史详情切换图片
        cutimgbutton(value) {
            if (value == 1) {
                if (this.cutimgnumber == 0) return
                this.cutimgnumber -= 1
            } else {
                // this.cutimgnumber = this.cutimgnumber + 1
                this.cutimgnumber += 1

                if (this.historicalDetailsform.picUrl.length <= this.cutimgnumber) {
                    this.cutimgnumber = this.historicalDetailsform.picUrl.length - 1
                }
            }
        },
        // 打开上传图片
        uploadingimg() {
            this.form = {
                areaIdList: [],
                pic: [],
                heightList: [],
                verification: '',
            }
            this.heightList = []
            this.checkAll = false
            this.isIndeterminate = false
            this.dialogVisibles = true
        },
        // 打开人工补充
        openanalysedialogVisible() {
            this.artificialSupplementaryform.description = ''
            this.artificialSupplementaryform.record = this.mainlist.record
            this.analysedialogVisible = true
        },
        // 保存上传图片
        submitToUploadPictures() {
            this.$refs.form.validate(valid => {
                if (!valid) return
                // this.form.heightList = this.heightList
                delete this.form.verification
                RemoteSensingService.addRemote(this.form).then(() => {
                    this.$message({
                        type: 'success',
                        message: '提交成功！',
                    })

                    this.relatedHistory()
                    this.dialogVisibles = false
                })
            })
        },
        // 人工补充
        artificialSupplementary() {
            RemoteSensingService.addRemoteDescription(this.artificialSupplementaryform).then(() => {
                this.$message({
                    type: 'success',
                    message: '提交成功！',
                })

                this.relatedHistory()
                this.analysedialogVisible = false
            })
        },
        handleCheckAllChange(val) {
            this.form.areaIdList = val ? this.areaId : []
            this.isIndeterminate = false
            if (this.form.areaIdList.length !== 0 && this.form.dateTime) {
                this.forCropSpecies()
            }
        },
        handleCheckedCitiesChange(value) {
            if (this.form.areaIdList.length !== 0 && this.form.dateTime) {
                this.forCropSpecies()
            }
            // this.form.areaIdList = value
            let checkedCount = value.length
            this.checkAll = checkedCount === this.cities.length
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
        },

        updatePicProperties(fileList) {
            this.form.pic = fileList
                .filter(f => f._type)
                .map(f => {
                    return { type: f._type, content: f._content }
                })
        },
        handleRemove(file, fileList) {
            this.updatePicProperties(fileList)
        },

        handleExceed() {
            this.$message.warning(`当前限制选择 1 个文件`)
        },

        handleChange(file, fileList) {
            file._type = file.name.slice(file.name.lastIndexOf('.') + 1)
            this.getBase64(file.raw).then(res => {
                file._content = res.slice(res.indexOf(',') + 1)
                this.updatePicProperties(fileList)
            })
        },
        getBase64(file) {
            return new Promise(function(resolve, reject) {
                let reader = new FileReader()
                let imgResult = ''
                if (file) {
                    reader.readAsDataURL(file)
                }
                reader.onload = function() {
                    imgResult = reader.result
                }
                reader.onerror = function(error) {
                    reject(error)
                }
                reader.onloadend = function() {
                    resolve(imgResult)
                }
            })
        },
    },
}
</script>
<style lang="less" scoped>
@import '../assets/css/remoteSensNumber.less';
</style>
<style lang="less">
@import '../assets/css/irrigationDrainage.less';
@import '../assets/css/meteorology.less';
</style>

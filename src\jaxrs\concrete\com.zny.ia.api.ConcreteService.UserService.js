/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const UserService = {
    /**
     * 权限详情
     * @param {*} id 权限id
     * @returns Promise 
     */
    'detailPermission': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3d009879fa9f4c4b1fc185778672cd1a9279bc5b', id)
            : execute(concreteModuleName, `/UserService/detailPermission`, 'json', 'POST', { id });
    }, 
    /**
     * 查看用户详情
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'findUserInfo': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c9c67b300ba623582102c20a040e7749f7c896ba', userId)
            : execute(concreteModuleName, `/UserService/findUserInfo`, 'json', 'POST', { userId });
    }, 
    /**
     * 删除角色
     * @param {*} roleId 角色Id
     * @returns Promise 
     */
    'removeRole': function (roleId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3b59b2e47dd404a9034a387ecf69b91af4ea740c', roleId)
            : execute(concreteModuleName, `/UserService/removeRole`, 'json', 'POST', { roleId });
    }, 
    /**
     * 删除用户
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'userDelete': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '602b1b9d52648c41c40cfb149c29eaedab92c577', userId)
            : execute(concreteModuleName, `/UserService/userDelete`, 'json', 'POST', { userId });
    }, 
    /**
     * 编辑用户
     * @param {*} userId 用户id
     * @param {*} po 
     * @returns Promise 
     */
    'userUpdate': function (userId, po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '97f7caf74d283fbcd6c03d3614acb4e685c0dc56', {userId, po})
            : execute(concreteModuleName, `/UserService/userUpdate`, 'json', 'POST', { userId, po });
    }, 
    /**
     * 删除权限
     * @param {*} id 权限id
     * @returns Promise 
     */
    'deletePermission': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '78bb33e70af7ef369ee5e07056763242b4d6fa66', id)
            : execute(concreteModuleName, `/UserService/permission`, 'json', 'DELETE', { id });
    }, 
    /**
     * 添加权限
     * @param {*} po 
     * @returns Promise 
     */
    'addPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f2b5c395dce8415fbc31b02977ecbe7781d0610e', po)
            : execute(concreteModuleName, `/UserService/addPermission`, 'json', 'POST', po);
    }, 
    /**
     * 修改角色信息
     * @param {*} updateRoleInfoPo 
     * @returns Promise 
     */
    'changeRoleInfoByRoleId': function (updateRoleInfoPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a683ec2e5d8a79fa9c29bae15631d6758f2d9f55', updateRoleInfoPo)
            : execute(concreteModuleName, `/UserService/changeRoleInfoByRoleId`, 'json', 'POST', updateRoleInfoPo);
    }, 
    /**
     * 添加用户
     * @param {*} po 
     * @returns Promise 
     */
    'userAdd': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5b257456f9adaff533070bcebccfc6457921dbdf', po)
            : execute(concreteModuleName, `/UserService/userAdd`, 'json', 'POST', po);
    }, 
    /**
     * 查询角色的信息列表
     * @param {*} pageNum 第几页 - 从1开始
     * @param {*} pageSize 每页多少条数据
     * @returns Promise 
     */
    'roleInfoList': function (pageNum, pageSize) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '032f6dadd62a9632860f13573e1d80dc7a81f997', {pageNum, pageSize})
            : execute(concreteModuleName, `/UserService/roleInfoList`, 'json', 'POST', { pageNum, pageSize });
    }, 
    /**
     * 查看用户列表
     * @param {*} po 
     * @returns Promise 
     */
    'userList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ce34b8d1886fd75bb231d74eee4c7f50885ff223', po)
            : execute(concreteModuleName, `/UserService/userList`, 'json', 'POST', po);
    }, 
    /**
     * 重置密码
     * @param {*} userId 用户id
     * @returns Promise 
     */
    'passwordReset': function (userId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6c3403dc96e1413e1a8f0e405f7fd5b98956501c', userId)
            : execute(concreteModuleName, `/UserService/passwordReset`, 'json', 'POST', { userId });
    }, 
    /**
     * 权限列表
     * @param {*} po 
     * @returns Promise 
     */
    'listPermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd464c9d8d3f359d66bb53faadbc7f24f7dec635b', po)
            : execute(concreteModuleName, `/UserService/listPermission`, 'json', 'POST', po);
    }, 
    /**
     * 编辑权限
     * @param {*} po 
     * @returns Promise 
     */
    'updatePermission': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '679dd30f8ff35297e13d880f589909f716128f35', po)
            : execute(concreteModuleName, `/UserService/permission`, 'json', 'PUT', po);
    }, 
    /**
     * 添加角色
     * @param {*} addRoleInfoPo 
     * @returns Promise 
     */
    'addRole': function (addRoleInfoPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '960dbc2e9d96027c0196c11aed0727602a1f2bec', addRoleInfoPo)
            : execute(concreteModuleName, `/UserService/addRole`, 'json', 'POST', addRoleInfoPo);
    }, 
    /**
     * 根据角色ID查询角色的信息
     * @param {*} roleId 角色Id
     * @returns Promise 
     */
    'roleInfoByRoleId': function (roleId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '21a5187e8ee572e61cda1cecd4aa78132e4afe75', roleId)
            : execute(concreteModuleName, `/UserService/roleInfoByRoleId`, 'json', 'POST', { roleId });
    }
}

export default UserService

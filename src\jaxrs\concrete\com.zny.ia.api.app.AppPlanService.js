/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppPlanService = {
    /**
     * 生产计划列表(按天)
     * @param {*} date 时间 yyyy-MM-dd
     * @returns Promise 
     */
    'findPlanByDay': function (date) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9b28c1c426a5b0f353933f093e18be792726e981', date)
            : execute(concreteModuleName, `/App/PlanService/findPlanByDay`, 'json', 'POST', { date });
    }, 
    /**
     * 生产计划详情
     * @param {*} planId 生产计划id
     * @returns Promise 
     */
    'detailPlan': function (planId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a51b33f4ef02c41dd6fc9a0fe21ecca81a955faf', planId)
            : execute(concreteModuleName, `/App/PlanService/detailPlan`, 'json', 'POST', { planId });
    }, 
    /**
     * 生产计划列表
     * @param {*} po 
     * @returns Promise 
     */
    'listPlan': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b3495fbc87ca16e57fc34f6bbe49f790ab6e1619', po)
            : execute(concreteModuleName, `/App/PlanService/listPlan`, 'json', 'POST', po);
    }, 
    /**
     * 判断一段时间内有无生产计划 返回有生产计划的日期
     * @param {*} startTime 开始时间 yyyy-MM-dd
     * @param {*} endTime 结束时间 yyyy-MM-dd
     * @returns Promise 
     */
    'havePlan': function (startTime, endTime) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8580915ede043370fbaea7a54b17fa4abe201fe1', {startTime, endTime})
            : execute(concreteModuleName, `/App/PlanService/havePlan`, 'json', 'POST', { startTime, endTime });
    }
}

export default AppPlanService

import Vue from 'vue'
import VueRouter from 'vue-router'
import { saveTokenId } from '@/jaxrs/concrete'

Vue.use(VueRouter)
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}

const routes = [
    {
        path: '/',
        name: 'Login',
        component: () => import(/* webpackChunkName: "about" */ '../views/login.vue'),
    },
    {
        path: '/home',
        name: 'Home',
        component: () => import(/* webpackChunkName: "about" */ '../views/Home.vue'),
        meta: {
            isLogin: true,
        },
        redirect: '/monitoringCenter',
        children: [
            {
                path: '/monitoringCenter',
                name: 'monitoringCenter',
                component: () => import(/* webpackChunkName: "about" */ '../components/monitoringCenter.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/centralControlPlatform',
                name: 'centralControlPlatform',
                component: () => import(/* webpackChunkName: "about" */ '../components/centralControlPlatform.vue'),
                meta: {
                    isLogin: true,
                },
                redirect: '/meteorology',
                children: [
                    {
                        path: '/meteorology',
                        name: 'meteorology',
                        component: () => import(/* webpackChunkName: "about" */ '../components/meteorology.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/soil',
                        name: 'soil',
                        component: () => import(/* webpackChunkName: "about" */ '../components/soil.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/InsectSituation',
                        name: 'InsectSituation',
                        component: () => import(/* webpackChunkName: "about" */ '../components/InsectSituation.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/seedlingGrowth',
                        name: 'seedlingGrowth',
                        component: () => import(/* webpackChunkName: "about" */ '../components/seedlingGrowth.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/irrigationDrainage',
                        name: 'irrigationDrainage',
                        component: () => import(/* webpackChunkName: "about" */ '../components/irrigationDrainage.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/traceSystem',
                        name: 'traceSystem',
                        component: () => import(/* webpackChunkName: "about" */ '../components/traceSystem.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/eppoWisdom',
                        name: 'eppoWisdom',
                        component: () => import(/* webpackChunkName: "about" */ '../components/eppoWisdom.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/waterMonitor',
                        name: 'waterMonitor',
                        component: () => import(/* webpackChunkName: "about" */ '../components/waterMonitor.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/remoteSensNumber',
                        name: 'remoteSensNumber',
                        component: () => import(/* webpackChunkName: "about" */ '../components/remoteSensNumber.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                    {
                        path: '/floatingWindow',
                        name: 'floatingWindow',
                        component: () => import(/* webpackChunkName: "about" */ '../components/floatingWindow.vue'),
                        meta: {
                            isLogin: true,
                        },
                    },
                ],
            },
        ],
    },
    {
        path: '/managementSystem',
        name: 'managementSystem',
        component: () => import(/* webpackChunkName: "about" */ '../views/managementSystem.vue'),
        meta: {
            isLogin: true,
        },
        redirect: '/systemSettings',
        children: [
            {
                path: '/systemSettings',
                name: 'systemSettings',
                component: () => import(/* webpackChunkName: "about" */ '../components/system/systemSettings/systemSettings.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/baseManagement',
                name: 'baseManagement',
                component: () => import(/* webpackChunkName: "about" */ '../components/system/baseManagement/baseManagement.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/deviceManagement',
                name: 'deviceManagement',
                component: () => import(/* webpackChunkName: "about" */ '../components/system/deviceManagement/deviceManagement.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/videoManagement',
                name: 'videoManagement',
                component: () => import(/* webpackChunkName: "about" */ '../components/system/videoManagement/videoManagement.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/productionPlan',
                name: 'productionPlan',
                component: () => import(/* webpackChunkName: "about" */ '../components/system/productionPlan/productionPlan.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/regionalThresholdSetting',
                name: 'regionalThresholdSetting',
                component: () =>
                    import(/* webpackChunkName: "about" */ '../components/system/regionalThresholdSetting/regionalThresholdSetting.vue'),
                meta: {
                    isLogin: true,
                },
            },
            {
                path: '/batchInforManagement',
                name: 'batchInforManagement',
                component: () =>
                    import(/* webpackChunkName: "about" */ '../components/system/batchInforManagement/batchInforManagement.vue'),
                meta: {
                    isLogin: true,
                },
            },
        ],
    },
    {
        path: '/tracePage',
        name: 'tracePage',
        component: () => import(/* webpackChunkName: "about" */ '../views/tracePage.vue'),
        meta: {
            isLogin: true,
        },
    },
]

const router = new VueRouter({
    routes,
})
router.beforeEach(async (to, from, next) => {
    let urlParams = new URLSearchParams(window.location.search);
    let params_info = urlParams.get('info');
    if (params_info) {
        let info = JSON.parse(params_info)
        if (info.token) {
            localStorage.setItem('nickname', info.nickname)
            localStorage.setItem('userRoles', info.userRoles)
            localStorage.setItem('frontPermissionVoList', JSON.stringify(info.frontPermissionVoList))
            localStorage.setItem('permissionVoList', JSON.stringify(info.permissionVoList))
            saveTokenId(info.token)
            let url = new URL(window.location.href);
            url.searchParams.delete('info');
            window.history.replaceState({}, '', url.toString()); // 移除 info 参数后更新 URL
            next({ name: 'Home' });
        }
    } else {
        next()
    }
})
export default router

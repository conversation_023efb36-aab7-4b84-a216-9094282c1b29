import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 系统管理员
     * desc: --
     * value: 0
     */
    系统管理员: 0,
    /**
     * label: 区域管理员
     * desc: --
     * value: 1
     */
    区域管理员: 1,
    /**
     * label: 游客
     * desc: --
     * value: 2
     */
    游客: 2,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
.meteorologyEMDialog{
  .el-dialog{
    height: 1045px;
    margin-top: 2vh !important;
    .el-dialog__body{
      height: 950px;
      .line{
        width: 1370px;
        margin: auto;
      }
      .time{
        position: absolute;
        top: 20px;
        left: 260px;
        font-size: 18px;
        font-weight: 400;
        color: #DFEEF3;
      }
      .handleBox{
        display: flex;
        margin-top: 30px;
        &_item{
          margin-right: 30px;
          .el-button{
            border-radius: 4px !important;
          }
        }
      }
      .dataList{
        margin-top: 30px;
        .list1,.list2{
          display: flex;
          .list_item{
            width: 16%;
            display: flex;
            .item_img{
              margin-right: 40px;
              img{
                width: 42px;
                height: 55px;
              }
            }
            .item_text{
              width: 130px;
              font-weight: 400;
              text-align: center;
              div:first-child {
                font-size: 20px;
                color: #70A8FF;
              }
              div:last-child {
                margin-top: 10px;
                font-size: 14px;
                color: #B8D4FF;
              }
            }
          }
        }
        .list2{
          margin-top: 30px;
        }
      }
      .chartList{
        height: 675px;
        margin-top: 30px;
        display: flex;
        &_left,&_right{
          width: 680px;
          height: 675px;
          .chartList_item{
            background: rgba(255,255,255,0);
            border: 1px solid #00F4FD;
            box-shadow:inset 0px 0px 28px 0px rgba(0,244,253,0.3);
          }
        }
        &_left,&_right{
          .chartList_item{
            width: 680px;
            position: relative;
            text-align: center;
            &_title{
              margin-top: 10px;
              font-size: 16px;
              font-weight: 400;
              color: #DFEEF3;
            }
            &_checkBtn{
              position: absolute;
              top: 10px;
              right: 10px;
              display: flex;
              .checkBtn1{
                width: 42px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di1.png) no-repeat 100% center;
              }
              .checkBtn2{
                width: 63px;
                height: 20px;
                background: url(../../image/centralControlPlatform/di3.png) no-repeat 100% center;
              }
              .checkBtn{
                margin-left: 10px;
                font-size: 14px;
                font-weight: 400;
                color: #DFEEF3;
                cursor: pointer;
              }
              .active1{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di2.png) no-repeat 100% center;
              }
              .active2{
                color: #FFA72A;
                background: url(../../image/centralControlPlatform/di4.png) no-repeat 100% center;
              }
              
            }
            &_temperatureList{
              display: flex;
              justify-content: center;
              .temperatureItem{
                width: 130px;
                height: 32px;
                line-height: 32px;
                margin-top: 25px;
                font-size: 14px;
                font-weight: 400;
                color: #FFFFFF;
                background: url(../../image/centralControlPlatform/border.png) no-repeat 100% center;
              }
              .temperatureItem:not(:first-child){
                margin-left: 20px;
              }
              .temperatureItem:nth-child(1){
                span{
                  color: #52F9BC;
                }
              }
              .temperatureItem:nth-child(2){
                span{
                  color: #FF46A1;
                }
              }
              .temperatureItem:nth-child(3){
                span{
                  color: #4CCAEE;
                }
              }
            }
            &_IlluminanceList{
              .IlluminanceItem{
                width: 150px;
                height: 32px;
                margin: auto;
                line-height: 32px;
                margin-top: 25px;
                font-size: 14px;
                font-weight: 400;
                color: #FFFFFF;
                background: url(../../image/centralControlPlatform/border4.png) no-repeat 100% center;
              }
            }
            &_chart{
              width: 680px;
              // height: 218px;
              // height: 280px;
              margin-top: 20px;
            }
          }
          .chartList_item:first-child{
            margin-bottom:10px ;
          }
        }
        &_left{
          .chartList_item{
            height: 332px;
          }
        }
        &_right{
          margin-left: 13px;
          .chartList_item{
            height: 675px;
            &_title{
              text-align: left;
              margin-left: 24px;
            }
            &_chart{
              width: 680px;
              height: 623px;
            }
          }
        }
      }
    }
  }
}
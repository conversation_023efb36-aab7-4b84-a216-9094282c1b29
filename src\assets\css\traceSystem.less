.traceSystem{
  width: 1532px;
  height: 922px;
  .title{
    height: 39px;
    line-height: 39px;
    position: absolute;
    top: 0;
    img{
      vertical-align: middle;
      margin: 0 7px 0 16px;
    }
    span{
      font-size: 16px;
      font-weight: bold;
      color: #DFEEF3;
    }
  }
  .bottom_left{
    position: absolute;
    left: -8px;
    bottom: -11px;
  }
  .bottom_right{
    position: absolute;
    right: -8px;
    bottom: -11px;
  }
  &_top{
    width: 100%;
    height: 415px;
    &_data{
      width: 100%;
      height: 100%;
      position: relative;
      &_title{
        width: 100%;
        background: url(../image/monitoringCenter/title5.png) no-repeat 100% center;
      }
      &_con{
        width: 1529px;
        height: 386px;
        position: absolute;
        bottom: 0;
        background: url(../image/centralControlPlatform/bg6.png) no-repeat 100% center;
        &_image{
          width: 1469px;
          height: 230px;
          margin: 40px 30px 0 30px;
        }
        .handleBox{
          width: 100%;
          margin-top: 30px;
          display: flex;
          &_input{
            width: 850px;
            height: 48px;
            margin-left: 30px;
            .el-input__inner{
              border-radius: 10px!important;
            }
          }
          &_item{
            width: 124px;
            height: 48px;
            margin-left: 30px;
            .el-button{
              border-radius: 10px!important;
            }
          }
        }
      }
    }
  }
  &_bottom{
    width: 100%;;
    height: 485px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    &_left{
      width: 752px;
      height: 485px;
      position: relative;
      &_title{
        width: 752px;
        background: url(../image/monitoringCenter/title4.png) no-repeat 100% center;
      }
      &_con{
        width: 752px;
        height: 456px;
        position: absolute;
        bottom: 0;
        background: url(../image/centralControlPlatform/bg7.png) no-repeat 100% center;
        .containerBox{
          width: 100%;
          height: 100%;
          position: relative;
          &_halfBg{
            width: 427px;
            height: 215px;
            position: absolute;
            bottom: 25px;
            left: 0;
            right: 0;
            margin: auto;
          }
          &_item{
            width: 110px;
            height: 110px;
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            background: url(../image/centralControlPlatform/trace-circle.png) no-repeat 100% center;
            &_text{
              width: 64px;
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #D5E6EB;
              text-align: center;
            }
          }
          &_item1{
            top: 236px;
            left: 31px;
          }
          &_item2{
            top: 107px;
            left: 137px;
          }
          &_item3{
            top: 52px;
            left: 321px;
          }
          &_item4{
            top: 106px;
            left: 505px;
          }
          &_item5{
            top: 236px;
            left: 611px;
          }
        }
      }
    }
    &_right{
      width: 752px;
      height: 485px;
      position: relative;
      &_title{
        width: 752px;
        background: url(../image/monitoringCenter/title4.png) no-repeat 100% center;
      }
      &_con{
        width: 752px;
        height: 446px;
        overflow-y: scroll;
        position: absolute;
        bottom: 0;
        background: url(../image/centralControlPlatform/bg7.png) no-repeat 100% center;
        .dataList1{
          width: 100%;
          &_header{
            height: 36px;
            margin-top: 20px;
            padding-left: 21px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FEFFFF;
            line-height: 30px;
            background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));
          }
          &_imageList{
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            padding: 0 12px;
            &_item{
              width: 220px;
              height: 169px;
              margin: 16px 12px;
              img{
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
        .datalist2{
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          &_con:nth-child(even){
            margin-left: 16px;
            .container{
              margin-right: 21px;
              .video-js{
                width: 100%;
                height: 100%;
              }
            }
          }
          &_con:nth-child(odd){
            margin-right: 16px;
            .container{
              margin-left: 21px;
              .video-js{
                width: 100%;
                height: 100%;
              }
            }
          }
          &_con{
            width: 360px;
            margin-top: 20px;
            &_header{
              width: 339px;
              height: 36px;
              background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #FEFFFF;
              line-height: 30px;
              padding-left: 21px;
            }
            .container{
              width: 339px;
              height: 264px;
              margin: 16px 0px 0 0px;
              img{
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

          }
        }
        .dataList3{
          width: 100%;
          &_header{
            height: 36px;
            margin-top: 20px;
            padding-left: 21px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FEFFFF;
            line-height: 30px;
            background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));
          }
          &_fileList{
            width: 100%;
            padding: 30px 0 9px 0;
            display: flex;
            // justify-content: flex-start;
            // justify-content: space-around;
            &_item{
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #007EFF;
              line-height: 20px;
              margin-left: 20px;
            }
            &_item:nth-child(1){
              //margin-left: 21px;
            }
          }
        }
        .datalist4{
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          &_con:nth-child(even){
            margin-left: 16px;
          }
          &_con:nth-child(odd){
            margin-right: 16px;
          }
          &_con{
            width: 360px;
            margin-top: 20px;
            &_header{
              width: 339px;
              height: 36px;
              background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));;
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #FEFFFF;
              line-height: 30px;
              padding-left: 21px;
            }
            &_container{
              margin: 28px 44px;
              table{
                width: 272px;
                border-collapse: collapse;
                color: #DFEEF3;
              }
              table th{
                padding: 10px;
              }
              table td{
                padding: 10px;
              }
              table tr{
                border: 1px solid #DFEEF3;
                text-align: center;
              }
            }
          }
        }
        .dataList5{
          width: 100%;
          &_header{
            height: 36px;
            margin-top: 20px;
            padding-left: 21px;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FEFFFF;
            line-height: 30px;
            background: linear-gradient(0deg, rgba(0,245,255,0.2), rgba(42,203,178,0));
          }
          &_fileList{
            width: 100%;
            padding: 10px 0 9px 0;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            &_item{
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #007EFF;
              line-height: 40px;
              margin-left: 30px;
            }
            &_item:nth-child(1){
              //margin-left: 21px;
            }
          }
        }
      }
    }
  }
}
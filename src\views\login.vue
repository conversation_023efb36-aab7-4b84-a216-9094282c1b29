<template>
    <div class="login">
        <div class="login_con">
            <div class="login_con_bg"></div>
            <div class="login_con_loginBox">
                <div class="login_con_loginBox_title">
                    <img src="../assets/image/monitoringCenter/title7.png" alt="" />
                </div>
                <div class="login_con_loginBox_formList">
                    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
                        <el-form-item prop="name">
                            <el-input v-model="ruleForm.name" placeholder="请输入账号">
                                <template #prefix>
                                    <div class="prefix">
                                        <img src="../assets/image/monitoringCenter/name_icon.png" alt="" />
                                    </div>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="pass">
                            <el-input v-model="ruleForm.pass" placeholder="请输入密码" type="password" show-password>
                                <template #prefix>
                                    <div class="prefix">
                                        <img src="../assets/image/monitoringCenter/pass_icon.png" alt="" />
                                    </div>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <div class="btnBox">
                        <el-button @click="login('ruleForm')">登录</el-button>
                    </div>
                </div>
            </div>
        </div>
        <!-- <el-button @click="$router.push({path:'/home'})">登录</el-button> -->
    </div>
</template>
<script>
import { saveTokenId } from '@/jaxrs/concrete'
import LoginService from '../jaxrs/concrete/com.zny.ia.api.LoginService.js'
import { getAmapTileUrl } from '../utils/mapTileUtils.js'
export default {
    data() {
        return {
            ruleForm: {
                name: '',
                pass: '',
            },
            rules: {
                name: [{ required: true, message: '请输入账号', trigger: 'blur' }],
                pass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
            },
        }
    },
    created() {
        window.addEventListener("keydown",this.eventHandler);
    },
    mounted() {},
    methods: {
        eventHandler(){
            // console.log('登录');
            var key = window.event.keyCode;
            if ( key == 13 ) {
                this.login('ruleForm');
            }
        },
        // 登录
        login(formName) {
            const that = this
            that.$refs[formName].validate(valid => {
                if (valid) {
                    LoginService.login(this.ruleForm.name, this.ruleForm.pass).then(res => {
                        localStorage.setItem('nickname', res.nickname)
                        localStorage.setItem('userRoles', res.userRoles)
                        localStorage.setItem('frontPermissionVoList',JSON.stringify(res.frontPermissionVoList))
                        localStorage.setItem('permissionVoList',JSON.stringify(res.permissionVoList))
                        saveTokenId(res.token)

                        // 登录成功后先获取地图瓦片地址，然后再跳转页面
                        getAmapTileUrl().then(() => {
                            that.$router.push({ path: '/home' })
                        }).catch(error => {
                            console.error('获取地图瓦片地址失败:', error)
                            that.$router.push({ path: '/home' })
                        })
                    })
                }
            })
        },
    },
    beforeDestroy(){
        window.removeEventListener("keydown",this.eventHandler);
    },
}
</script>
<style lang="less">
@import '../assets/css/login.less';

</style>

<template>
    <div class="irrigationDrainage">
        <div class="irrigationDrainage_left">
            <div class="irrigationDrainage_left_info">
                <div class="title irrigationDrainage_left_info_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>灌溉区简介</span>
                </div>
                <div class="irrigationDrainage_left_info_con">
                    <div class="irrigationDrainage_left_info_con_text">
                        {{ info }}
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <div class="irrigationDrainage_left_data1">
                <div class="title irrigationDrainage_left_data1_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>累计灌溉</span>
                </div>
                <div class="irrigationDrainage_left_data1_con">
                    <div class="irrigationDrainage_left_data1_con_search formStyle">
                        <el-select
                            v-model="areaId1"
                            clearable
                            popper-class="selectStyle_list"
                            placeholder="请选择区域"
                            @change="areaChange1"
                        >
                            <el-option
                                v-for="item in areaData"
                                :key="item.areaId"
                                :label="item.areaName"
                                :value="item.areaId"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="irrigationDrainage_left_data1_con_dataChart">
                        <znyEchart :chartData="cumulativeIrrigationOption" :chartType="'bar'"></znyEchart>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <div class="irrigationDrainage_left_data2">
                <div class="title irrigationDrainage_left_data2_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>土壤墒情变化趋势</span>
                </div>
                <div class="irrigationDrainage_left_data2_con">
                    <div class="irrigationDrainage_left_data2_con_search formStyle">
                        <el-select v-model="areaId2" clearable popper-class="selectStyle_list" placeholder="请选择区域" @change="areaChange2">
                            <el-option
                                v-for="item in areaData"
                                :key="item.areaId"
                                :label="item.areaName"
                                :value="item.areaId"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="irrigationDrainage_left_data2_con_dataChart">
                        <znyEchart :chartData="flowVelocityOption" :chartType="'line'"></znyEchart>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="irrigationDrainage_right">
            <!-- <div class="irrigationDrainage_right_con"  @click="irrigationDrainageDialogOpen"> -->
            <div class="irrigationDrainage_right_con">
                <znyAMap :type="3"></znyAMap>
                <!--                <div class="irrigationDrainage_twoDimensionalMap" @click="irrigationDrainageDialogOpen"></div>-->
            </div>
            <div class="title irrigationDrainage_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="irrigationDrainage_right_data">
                <div class="irrigationDrainage_right_data_total">正在灌溉：{{ AreaIrrigatingData.length }}</div>
                <div
                    class="irrigationDrainage_right_data_list"
                    v-for="(item, index) in AreaIrrigatingData"
                    :key="index"
                >
                    {{ item.areaVo.areaName }} {{ item.nodeName }}
                </div>
            </div>
            <div class="irrigationDrainage_right_btn" @click="floatingWindow">
                <EquipmentButton />
            </div>
        </div>
    </div>
</template>

<script>
import EquipmentButton from '../components/equipmentButton.vue'
import znyAMap from '../components/znyAMap.vue'
import { chart } from '../js/chart'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
import IrrigationService from '../jaxrs/concrete/com.zny.ia.api.IrrigationService'
export default {
    name: 'irrigationDrainage',
    components: {
        EquipmentButton,
        znyAMap,
    },
    data() {
        return {
            info:
                '灌溉区项目规划设计灌溉面积1.41万亩,有效灌斑面积1.05万亩，现实际灌克面积0.77万亩,年用水量约为700万立米,插种作将全第为水稻。灌区现有:—一干渠道:长度约为10公里，节制机、排洪闸11座,波信2座,涵河2个,支渠口97个.控制设备32个节点，管理员可对所有设备节点进行相应管理，保证灌溉区土壤水源充足。',
            cumulativeIrrigationOption: {
                //累计灌溉柱状图数据
                show: true,
                option: {},
            },
            flowVelocityOption: {
                //通道24小时灌溉流速折线图数据
                show: true,
                option: {},
            },
            areaId1: '',
            areaId2: '',
            areaData: [],
            AreaIrrigatingData: [],
        }
    },
    mounted() {
        this.getAreaData()
        this.getAreaIrrigating()
        chart.cumulativeIrrigationBar(this.areaId1).then(data => {
            this.cumulativeIrrigationOption = data
        })
        chart.flowVelocityLine(this.areaId2).then(data => {
            this.flowVelocityOption = data
        })
    },
    methods: {
        floatingWindow() {
            this.$router.push('/floatingWindow')
        },
        //获取区域信息
        getAreaData() {
            CommonService.allAreaListInformation().then(res => {
                this.areaData = res
            })
        },
        //累计灌溉区域选择
        areaChange1(val) {
            chart.cumulativeIrrigationBar(val).then(data => {
                this.cumulativeIrrigationOption = data
            })
        },
        //土壤墒情变化区域选择
        areaChange2(val){
            chart.flowVelocityLine(val).then(data=>{
                this.flowVelocityOption=data
            })
        },
        //获取正在灌溉地区
        getAreaIrrigating() {
            IrrigationService.irrigationWatering().then(res => {
                this.AreaIrrigatingData = res
            })
        },
        // //灌排种植区弹窗打开
        // irrigationDrainageDialogOpen(){
        //     this.zEmit('irrigationDrainageDialogOpen',this.areaId)
        // },
        beforeDestroy() {
            const that = this
            Object.assign(that.$data, that.$options.data())
        },
    },
}
</script>

<style lang="less">
@import '../assets/css/irrigationDrainage.less';
</style>

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppWeatherService = {
    /**
     * 气象基础数据
     * @param {*} areaId 区域id,查看设备时此项为null
     * @param {*} equipmentId 设备id,查看区域时此项为null
     * @returns Promise 
     */
    'weatherBasicData': function (areaId, equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2d26350bba6d163dd661c222285d287ee12503bc', {areaId, equipmentId})
            : execute(concreteModuleName, `/App/WeatherService/weatherBasicData`, 'json', 'POST', { areaId, equipmentId });
    }, 
    /**
     * 报警信息查看
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'selectAlarmInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ec28b06f3dbb5f917d8de901d023674aa7af8f96', areaId)
            : execute(concreteModuleName, `/App/WeatherService/selectAlarmInfo`, 'json', 'POST', { areaId });
    }
}

export default AppWeatherService

.tracePage{
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  background: #fff;
  &_header{
    //width: 100%;
    padding: 0 10%;
    height: 80px;
    background: #009985;
    font-size: 24px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #FEFFFF;
    line-height: 80px;
  }
  &_code{
    padding: 1% 10% 0 10%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    &_name{
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 20px;
    }
    &_item{
      width: 80px;
      height: 80px;
      .qrcode{
        .tracePage_code_item img{
          width: 100%;
          height: 100%;
        }
      }
      &_text{
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        line-height: 20px;
        text-align: center;
      }
    }
  }
  &_part{
    padding: 1% 10%;
    &_title{
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      &_line{
        width: 20px;
        height: 4px;
        background: #009985;
        line-height: 20px;
      }
      &_text{
        padding: 0 1%;
        display: inline-block;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
      }
    }
    &_con{
      width: 100%;
      padding: 2% 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      table{
        width: 100%;
        border-collapse: collapse;
      }
      table th{
        width: 16%;
        padding: 10px;
      }
      table td{
        width: 34%;
        padding: 10px;
      }
      table tr{
        border: 1px solid #999999;
        text-align: center;
      }
      &_left{
        width: 48%;
        img{
          width: 100%;
          height: 354px;
          object-fit: cover;
        }
      }
      &_right{
        width: 48%;
      }
    }
    &_con3{
      width: 100%;
      padding: 2% 0;
      &_list{
        width: 100%;
        display: flex;
        margin-bottom: 1%;
        &_name{
          width: 10%;
          height: 18px;
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333333;
          line-height: 20px;
        }
        &_item{
          width: 90%;
          display: flex;
          flex-wrap: wrap;
          .el-progress{
            width: 100%;
          }
          .el-progress-bar__outer{
            background-color: #C4FFF7;
          }
          .itemImage{
            width: 31%;
            margin-right: 1%;
            margin-bottom: 1%;
            height: 319px;
            img{
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .itemVideo{
            width: 100%;
            height: 380px;
            .video-js{
              width: 100%;
              height: 100%;
            }
            img{
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .itemTable1{
            width: 45%;
            display: flex;
            &_name{
              width: 20%;
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #333333;
              line-height: 20px;
            }
            &_item{
              width: 78%;
              table{
                width: 100%;
                border-collapse: collapse;
              }
              table th{
                padding: 10px;
                background: #E4F2FF;
              }
              table td{
                padding: 10px;
              }
              table tr{
                border: 1px solid #999999;
                text-align: center;
              }
              &_expand{
                text-align: center;
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #007EFF;
                line-height: 20px;
                margin-top: 1%;
                cursor: pointer;
              }
            }
          }
          .itemTable2{
            &_name{
              width: 30%;
            }
            &_item{
              width: 68%;
            }
            table tr{
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #007EFF;
              line-height: 20px;
              cursor: pointer;
            }
          }
        }
      }
      &_list2{
        margin-top: 4%;
      }
    }
  }
  &_part2{
    background: #E4F2FF;
  }
  &_part4{
    //padding: 2% 0;
    background: #E4F2FF;
    &_link{
      margin: 4% 0 2% 0;
      display: flex;
      justify-content: center;
      .linkItem{
        padding: 0 4%;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #007EFF;
        line-height: 20px;
      }
    }
  }
}
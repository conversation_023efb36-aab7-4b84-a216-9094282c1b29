/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WaterService = {
    /**
     * 汇报水质检测报告 pc app 共用
     * @param {*} po 
     * @returns Promise 
     */
    'addWaterReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6ee10516720f1722f956060f52f963fa7c02f40a', po)
            : execute(concreteModuleName, `/WaterService/addWaterReport`, 'json', 'POST', po);
    }, 
    /**
     * 近期检测水质 pc app 共用
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findWaterInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6e27d9b2bdc217b1e2eb1c9146a926332bbd2122', areaId)
            : execute(concreteModuleName, `/WaterService/findWaterInfo`, 'json', 'POST', { areaId });
    }, 
    /**
     * 人工汇报文件（图片）查看
     * @param {*} reportId 水质报告id
     * @returns Promise 
     */
    'detailWaterPic': function (reportId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '229d8194654131d935ebf87ae94829c074d050f6', reportId)
            : execute(concreteModuleName, `/WaterService/detailWaterPic`, 'json', 'POST', { reportId });
    }, 
    /**
     * 近期水质检测时间计划(已检测的)
     * @returns Promise 
     */
    'listWaterSchedule': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '57afc220aae3794edbb5d7a4764b826c7ae671c4')
            : execute(concreteModuleName, `/WaterService/listWaterSchedule`, 'json', 'GET');
    }, 
    /**
     * 报告详情 pc app 共用
     * @param {*} reportId 水质报告id
     * @returns Promise 
     */
    'findWaterDetailInfo': function (reportId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e8befbbe919f1fc6c4a046e1261f01cda54721cc', reportId)
            : execute(concreteModuleName, `/WaterService/findWaterDetailInfo`, 'json', 'POST', { reportId });
    }, 
    /**
     * 水质检测报告列表
     * @param {*} po 
     * @returns Promise 
     */
    'listWaterReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9808fa9c8cfdee501c2c6ad1b50ca42b0424b408', po)
            : execute(concreteModuleName, `/WaterService/listWaterReport`, 'json', 'POST', po);
    }
}

export default WaterService

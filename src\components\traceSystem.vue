<template>
  <div>
    <div class="traceSystem" v-if="!isShowTraceRecord">
      <div class="traceSystem_top">
        <div class="traceSystem_top_data">
          <div class="title traceSystem_top_data_title">
            <img src="../assets/image/monitoringCenter/title_icon.png" alt="">
            <span>质量安全溯源系统</span>
          </div>
          <div class="traceSystem_top_data_con">
            <img src="../assets/image/centralControlPlatform/trace-bg.png" alt="" class="traceSystem_top_data_con_image">
            <div class="handleBox">
              <div class="handleBox_input formStyle">
                <el-input v-model="traceId" placeholder="请输入溯源码"></el-input>
              </div>
              <div class="handleBox_item searchButtonStyle2" @click="toTracePage()">
                <el-button>追溯查询</el-button>
              </div>
              <div class="handleBox_item searchButtonStyle2" v-if="areaIsJudge" @click="traceRecordAddDialogOpen()">
                <el-button>溯源建档</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="traceSystem_bottom">
        <div class="traceSystem_bottom_left">
          <div class="title traceSystem_bottom_left_title">
            <img src="../assets/image/monitoringCenter/title_icon.png" alt="">
            <span style="color: #007EFF;cursor: pointer" @click="isShowTraceRecord=true">档案馆>></span>
          </div>
          <div class="traceSystem_bottom_left_con">
            <div class="containerBox">
              <img src="../assets/image/centralControlPlatform/trace-half.png" alt="" class="containerBox_halfBg">
              <div class="containerBox_item containerBox_item1">
                <div class="containerBox_item_text" @click="traceRecordOtherInfo('生长环境图片')">生长环境图片</div>
              </div>
              <div class="containerBox_item containerBox_item2" @click="traceRecordOtherInfo('相关视频')">
                <div class="containerBox_item_text">相关视频</div>
              </div>
              <div class="containerBox_item containerBox_item3" @click="traceRecordOtherInfo('认证文件')">
                <div class="containerBox_item_text">认证文件</div>
              </div>
              <div class="containerBox_item containerBox_item4" @click="traceRecordOtherInfo('灌溉数据')">
                <div class="containerBox_item_text">灌溉数据</div>
              </div>
              <div class="containerBox_item containerBox_item5" @click="traceRecordOtherInfo('水质环境记录')">
                <div class="containerBox_item_text">水质环境记录</div>
              </div>
            </div>
          </div>
          <div class="bottom_left">
            <img src="../assets/image/monitoringCenter/bottom_left.png" alt="">
          </div>
          <div class="bottom_right">
            <img src="../assets/image/monitoringCenter/bottom_right.png" alt="">
          </div>
        </div>
        <div class="traceSystem_bottom_right">
          <div class="title traceSystem_bottom_right_title">
            <img src="../assets/image/monitoringCenter/title_icon.png" alt="">
            <span>{{title}}</span>
          </div>
          <div class="traceSystem_bottom_right_con">
            <div class="dataList1" v-for="(item,index) in data1" :key="index" v-if="title=='生长环境图片'">
              <div class="dataList1_header">溯源档案编号：{{item.serialNumber}}</div>
              <div class="dataList1_imageList">
                <div class="dataList1_imageList_item" v-for="(Item,Index) in item.url" :key="Index">
                  <img :src=Item alt="">
                </div>
              </div>
            </div>
            <div class="datalist2" v-if="title=='相关视频'&&showVideo">
              <div class="datalist2_con" v-for="(item,index) in data2" :key="index">
                <div class="datalist2_con_header">
                  溯源档案编号：{{item.serialNumber}}
                </div>
                <div class="container">
                  <video :id="'myVideo_'+index"
                         class="video-js vjs-big-play-centered"
                  >
                    <source :src=item.url type="video/mp4">
                  </video>
                </div>
              </div>
            </div>
            <div class="dataList3" v-for="(item,index) in data3" :key="index" v-if="title=='认证文件'">
              <div class="dataList3_header">溯源档案编号：{{item.serialNumber}}</div>
              <div class="dataList3_fileList">
                <div class="dataList3_fileList_item" v-if="item.traceSourceCertificationVo.examiningReportName" @click="openFile(item.traceSourceCertificationVo.examiningReport)">{{item.traceSourceCertificationVo.examiningReportName}}</div>
                <div class="dataList3_fileList_item" v-if="item.traceSourceCertificationVo.organicCropReportName" @click="openFile(item.traceSourceCertificationVo.organicCropReport)">{{item.traceSourceCertificationVo.organicCropReportName}}</div>
                <div class="dataList3_fileList_item" v-if="item.traceSourceCertificationVo.greenCropReportName" @click="openFile(item.traceSourceCertificationVo.greenCropReport)">{{item.traceSourceCertificationVo.greenCropReportName}}</div>
              </div>
            </div>
            <div class="datalist4" v-if="title=='灌溉数据'">
              <div class="datalist4_con" v-for="(item,index) in data4" :key="index">
                <div class="datalist4_con_header">
                  溯源档案编号：{{item.serialNumber}}
                </div>
                <div class="datalist4_con_container">
                  <table border="1">
                    <tr>
                      <th>灌溉时间点</th>
                      <th>灌溉量m³</th>
                    </tr>
                    <tr v-for="(Item,Index) in item.irrigationList" :key="Index">
                      <td>{{Item.showTime}}</td>
                      <td>{{Item.value}}</td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
            <div class="dataList5" v-for="(item,index) in data5" :key="index" v-if="title=='水质环境记录'">
              <div class="dataList5_header">溯源档案编号：{{item.serialNumber}}</div>
              <div class="dataList5_fileList">
                <div class="dataList5_fileList_item" v-for="(Item,Index) in item.traceSourceWaterReportVoList" :key="Index" @click="openFile(Item.reportUrl)">
                  {{Item.reportName}}
                </div>
              </div>
            </div>
          </div>
          <div class="bottom_left">
            <img src="../assets/image/monitoringCenter/bottom_left.png" alt="">
          </div>
          <div class="bottom_right">
            <img src="../assets/image/monitoringCenter/bottom_right.png" alt="">
          </div>
        </div>
      </div>
    </div>
    <div class="traceRecord" v-else>
      <traceRecord @trackRecordIsBack="backData"></traceRecord>
    </div>
  </div>
</template>
<script>
  import traceRecord from '../components/traceRecord.vue';
  import TraceSourceService from '../jaxrs/concrete/com.zny.ia.api.TraceSourceService'
  import TraceSourceCommonService from '../jaxrs/concrete/com.zny.ia.api.TraceSourceCommonService'
  export default {
    name:'traceSystem',
    components:{
      traceRecord,
    },
    data(){
      return{
        areaIsJudge:true,//判断当前用户能否操作该区域
        title:'',
        isShowTraceRecord:false,
        traceId:"",
        data1:[],
        data2:[],
        data3:[],
        data4:[],
        data5:[],
        videoOptions: {
          autoplay: false, //自动播放
          controls: true, //是否显示底部控制栏
          loop: true, //是否循环播放
          muted: true, //设置默认播放音频
          //是否自适应布局,播放器将会有流体体积。换句话说，它将缩放以适应容器。
          // 如果<video>标签有“vjs-fluid”样式时，这个选项会自动设置为true。
          fluid: false,
        },
        playlist:[],
        showVideo:true,
      }
    },
    mounted(){
      this.title='生长环境图片'
      this.traceRecordOtherInfo(this.title)
      this.areaJudge()
    },
    methods: {
      // 判断当前用户能否操作该区域
      areaJudge(){
        let role = localStorage.getItem('userRoles')
        if(role==2){
          this.areaIsJudge=false
        }else{
          this.areaIsJudge=true
        }
      },
      //档案馆返回
      backData(data){
        this.isShowTraceRecord=data
      },
      //左侧信息点开
      traceRecordOtherInfo(title){
        this.title=title
        this.clearVideo()
        if(title=='生长环境图片'){
          TraceSourceService.traceSourceHighQualityGrowingEnvironmentPicList()
          .then((res)=>{
            this.data1=res
          })
        }else if(title=='相关视频'){
          this.showVideo=true
          TraceSourceService.traceSourceHighQualityVideoPicList()
                  .then((res)=>{
                    this.data2=res
                    var that = this
                    that.$nextTick(()=> {
                      that.videoPlayer()
                    })
                  })

        }else if(title=='认证文件'){
          TraceSourceService.traceSourceHighQualityCertificationPicList()
                  .then((res)=>{
                    this.data3=res
                  })

        }else if(title=='灌溉数据'){
          TraceSourceService.traceSourceHighQualityIrrigationPicList()
                  .then((res)=>{
                    this.data4=res
                  })

        }else if(title=='水质环境记录'){
          TraceSourceService.traceSourceHighQualityWaterPicList()
                  .then((res)=>{
                    this.data5=res
                  })

        }
      },
      //视频初始化
      videoPlayer(){
        var that = this
        setTimeout(() => {
          let lang=this.data2.length   //（第一步）这是多个视频地址数组的长度
          for(let i=0;i<lang;i++){
            var player=  that.$video(
                    "myVideo_"+i,
                    that.videoOptions,
                    function () {
                      // this.play()   //取消自动播放
                    })
            that.playlist.push(player)  //（第二步）palylist是定义的空数组，存放多个视频实例
          }
        }, 500);
      },
      //视频清除
      clearVideo(){
        var that = this
        if(that.playlist.length>0){
          for(let i=0;i<that.playlist.length;i++){
            that.playlist[i].dispose(); //（第四步）dispose()是官方的销毁函数
          }
        }
        that.showVideo=false
        that.playlist=[]
      },
      traceRecordUploadDialogOpen(title){
        this.zEmit('traceRecordUploadDialogOpen',title)
      },
      //溯源建档弹窗
      traceRecordAddDialogOpen(){
        this.zEmit('traceRecordAddDialogOpen')
      },
      //打开溯源网页
      toTracePage(){
        if(this.traceId==""){
          return
        }else{
          TraceSourceCommonService.traceSourceDetail(this.traceId)
                  .then(()=>{
                    let routerData = this.$router.resolve({
                      path: `/tracePage`,
                      query:{id:this.traceId}
                    });
                    window.open(routerData.href,'_blank')
                  })
        }
      },
      //打开文件
      openFile(url){
        if(url!=""){
          window.open(url,'_blank')
        }

      }
    },
    beforeDestroy() {
      this.clearVideo()
    },
  }
</script>
<style lang="less">
  @import '../assets/css/traceSystem.less';
</style>
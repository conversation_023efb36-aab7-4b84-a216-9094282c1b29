/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WeatherService = {
    /**
     * 区域光照度图表
     * @param {*} po 
     * @returns Promise 
     */
    'illuminanceChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5d7826786e6d146f776c185112d542625f85bd3a', po)
            : execute(concreteModuleName, `/WeatherService/illuminanceChart`, 'json', 'POST', po);
    }, 
    /**
     * 气象数据汇报
     * @param {*} po 
     * @returns Promise 
     */
    'weatherReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8ad343214e307cf94a352f617450a13ca79e91c3', po)
            : execute(concreteModuleName, `/WeatherService/weatherReport`, 'json', 'POST', po);
    }, 
    /**
     * 区域温湿度图表,本日调用
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'temperatureChartDay': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1dc0b0e02d217aca606ca5cc91604b31b96560ca', areaId)
            : execute(concreteModuleName, `/WeatherService/temperatureChartDay`, 'json', 'POST', { areaId });
    }, 
    /**
     * 气象基础数据
     * @returns Promise 
     */
    'weatherBasicData': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'cf14f80f781423d79ed68c2c6d8b9ad74509f4b4')
            : execute(concreteModuleName, `/WeatherService/weatherBasicData`, 'json', 'GET');
    }, 
    /**
     * 区域温湿度图表,近七天,近三十天调用
     * @param {*} po 
     * @returns Promise 
     */
    'temperatureChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd7d6582807d5fc4cbaab6988e11131070bbef11c', po)
            : execute(concreteModuleName, `/WeatherService/temperatureChart`, 'json', 'POST', po);
    }, 
    /**
     * 区域气象数据
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'weatherAreaData': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a9f43942ea7584ff5cd5e91be6893a7af03af391', areaId)
            : execute(concreteModuleName, `/WeatherService/weatherAreaData`, 'json', 'POST', { areaId });
    }, 
    /**
     * 气象报警汇总
     * @returns Promise 
     */
    'weatherAlarmCollect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '52d608b6107ef87c4e979d923e6ad9f5f4991b13')
            : execute(concreteModuleName, `/WeatherService/weatherAlarmCollect`, 'json', 'GET');
    }, 
    /**
     * 区域风向图表
     * @param {*} po 
     * @returns Promise 
     */
    'windChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1febb7508132f14fbf887c60239993d4f01e5746', po)
            : execute(concreteModuleName, `/WeatherService/windChart`, 'json', 'POST', po);
    }, 
    /**
     * 气象报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'weatherAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5908d368536e9f5ec8ba9b75297d5dd14efefafc', po)
            : execute(concreteModuleName, `/WeatherService/weatherAlarmRecord`, 'json', 'POST', po);
    }, 
    /**
     * 气象数据历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'weatherHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '937b7d679b0839bb6682dfa3e93d9270809fcff7', po)
            : execute(concreteModuleName, `/WeatherService/weatherHistory`, 'json', 'POST', po);
    }
}

export default WeatherService

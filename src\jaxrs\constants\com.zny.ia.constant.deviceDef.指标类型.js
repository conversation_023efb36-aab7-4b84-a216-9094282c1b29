import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 气象
     * desc: --
     * value: 1
     */
    气象: 1,
    /**
     * label: 土壤
     * desc: --
     * value: 2
     */
    土壤: 2,
    /**
     * label: 灌排
     * desc: --
     * value: 3
     */
    灌排: 3,
    /**
     * label: 虫情
     * desc: --
     * value: 4
     */
    虫情: 4,
    /**
     * label: 水质
     * desc: --
     * value: 5
     */
    水质: 5,
    /**
     * label: 苗情
     * desc: --
     * value: 6
     */
    苗情: 6,
    /**
     * label: 控制
     * desc: --
     * value: 7
     */
    控制: 7,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
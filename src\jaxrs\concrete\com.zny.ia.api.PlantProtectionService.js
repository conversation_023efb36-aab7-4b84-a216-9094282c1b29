/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const PlantProtectionService = {
    /**
     * 智慧植保主页面-植保记录
     * @returns Promise 
     */
    'plantProtectionRecordVoInformation': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '549300e85f0a3e127966e028f12365046d52c776')
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionRecordVoInformation`, 'json', 'GET');
    }, 
    /**
     * 智慧植保主页面-近期植保计划
     * @returns Promise 
     */
    'recentPlantProtectionProgram': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '924198ef7b2e253acca2d048f3c6ba93e9ce4e51')
            : execute(concreteModuleName, `/PlantProtectionService/recentPlantProtectionProgram`, 'json', 'GET');
    }, 
    /**
     * 智慧植保主页面-区域详情
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'plantProtectionAreaInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ac17a4a11872236c1124a7c6b18c53aba63a2edb', areaId)
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionAreaInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 智慧植保主页面-智慧植保预防
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'plantProtectionPreventionListInformation': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '044529dca97ba139280c84c2877544afd9a3c225', pageRequest)
            : execute(concreteModuleName, `/PlantProtectionService/plantProtectionPreventionListInformation`, 'json', 'POST', pageRequest);
    }
}

export default PlantProtectionService

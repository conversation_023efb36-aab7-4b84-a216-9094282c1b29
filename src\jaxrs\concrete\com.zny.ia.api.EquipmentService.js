/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const EquipmentService = {
    /**
     * 下载设备列表
     * @returns Promise 
     */
    'downLoadEpList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3e7e3eb847ea0b7083fd32cf88ffbb1aef13c99a')
            : execute(concreteModuleName, `/EquipmentService/downLoadEpList`, 'json', 'GET');
    }, 
    /**
     * 其他设备添加,除灌排设备
     * @param {*} po 
     * @returns Promise 
     */
    'insertEquipment': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a2b9f881547e315c02acd047faaf4fb0d8ac7b92', po)
            : execute(concreteModuleName, `/EquipmentService/insertEquipment`, 'json', 'POST', po);
    }, 
    /**
     * 查看设备列表
     * @param {*} po 
     * @returns Promise 
     */
    'eqSelectList': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ab26aae0a1c0cd3070edfa84b6bd12be76bd88ba', po)
            : execute(concreteModuleName, `/EquipmentService/eqSelectList`, 'json', 'POST', po);
    }, 
    /**
     * 获取平台支持的设备型号列表
     * @returns Promise 
     */
    'findEquipmentTypeList': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e8e872a1b42fcb5ca4750286f6c055e707b46b12')
            : execute(concreteModuleName, `/EquipmentService/findEquipmentTypeList`, 'json', 'GET');
    }, 
    /**
     * 修改设备名称
     * @param {*} equipmentId 设备id
     * @param {*} equipmentName 设备名称
     * @returns Promise 
     */
    'updateEqInfo': function (equipmentId, equipmentName) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7c0008ae38a66a9f146719c2623dc85d5690694a', {equipmentId, equipmentName})
            : execute(concreteModuleName, `/EquipmentService/eqInfo`, 'json', 'PUT', { equipmentId, equipmentName });
    }, 
    /**
     * 灌排设备添加
     * @param {*} po 
     * @returns Promise 
     */
    'insertIrrigationEquipment': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8cfedbdd712323facdc51f9f40e12c21cb4eed3f', po)
            : execute(concreteModuleName, `/EquipmentService/insertIrrigationEquipment`, 'json', 'POST', po);
    }, 
    /**
     * 设置记录间隔
     * @param {*} po 
     * @returns Promise 
     */
    'interRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '278c3f46a61e750e3e131f716f5b75771080732c', po)
            : execute(concreteModuleName, `/EquipmentService/interRecord`, 'json', 'POST', po);
    }, 
    /**
     * 设备管理信息查看
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findEqInfo': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '06a0f63baa33a63fa64968a15f2d6d9c599b6d8e', equipmentId)
            : execute(concreteModuleName, `/EquipmentService/findEqInfo`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 获取此设备型号支持的指标项列表
     * @param {*} equipmentType 设备型号
     * @returns Promise 
     */
    'findTargetList': function (equipmentType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8dfe20f4edc3c8a781c4226af3cb03812c67df3f', equipmentType)
            : execute(concreteModuleName, `/EquipmentService/findTargetList`, 'json', 'POST', { equipmentType });
    }, 
    /**
     * 获取此设备型号支持的指标类型列表
     * @param {*} equipmentType 设备型号
     * @returns Promise 
     */
    'findTargetTypeList': function (equipmentType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7e3e106e840b820029ac2a373e0625d648c04fe7', equipmentType)
            : execute(concreteModuleName, `/EquipmentService/findTargetTypeList`, 'json', 'POST', { equipmentType });
    }
}

export default EquipmentService

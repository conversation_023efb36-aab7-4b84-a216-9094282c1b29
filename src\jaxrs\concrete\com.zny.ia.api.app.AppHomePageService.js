/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppHomePageService = {
    /**
     * 首页报警
     * @returns Promise 
     */
    'homeAlarm': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2a879365b44be9146582a643fa58dea400974fab')
            : execute(concreteModuleName, `/App/HomePageService/homeAlarm`, 'json', 'GET');
    }, 
    /**
     * 首页生产计划
     * @returns Promise 
     */
    'homePlan': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ebbd66cc93dc329abf9c8ff72ffae2ac2080f2d4')
            : execute(concreteModuleName, `/App/HomePageService/homePlan`, 'json', 'GET');
    }
}

export default AppHomePageService

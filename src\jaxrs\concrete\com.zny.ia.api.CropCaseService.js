/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const CropCaseService = {
    /**
     * 上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'cropCasePicImport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1ee24f874414e76c02d0953011e0a25b0e0e8f27', po)
            : execute(concreteModuleName, `/CropCaseService/cropCasePicImport`, 'json', 'POST', po);
    }, 
    /**
     * 苗情实时拍摄
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'realTimeShoot': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3af1013158bcb61a32c20daaa8ba0006f48f3121', equipmentId)
            : execute(concreteModuleName, `/CropCaseService/realTimeShoot`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 苗情实时监控
     * @param {*} equipmentIds 设备id
     * @returns Promise 
     */
    'cropCaseMonitoring': function (equipmentIds) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '05f09d21737376888ba0752a8c6ba833cf1125ab', equipmentIds)
            : execute(concreteModuleName, `/CropCaseService/cropCaseMonitoring`, 'json', 'POST', { equipmentIds });
    }, 
    /**
     * 苗情历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'cropCaseHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '93899ecad0e91fc180db119c0fcf8585e71d3336', po)
            : execute(concreteModuleName, `/CropCaseService/cropCaseHistory`, 'json', 'POST', po);
    }, 
    /**
     * 苗情首页参考分析添加
     * @param {*} po 
     * @returns Promise 
     */
    'saveCropCasePicAnalyse': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd4550cd2855d904e06adfa1bff08c5be09a36701', po)
            : execute(concreteModuleName, `/CropCaseService/cropCasePicAnalyse`, 'json', 'POST', po);
    }, 
    /**
     * 展开全部监控
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findCropCaseMonitorList': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'fb4c889b10519e8d2589f09335f622386400c716', areaId)
            : execute(concreteModuleName, `/CropCaseService/findCropCaseMonitorList`, 'json', 'POST', { areaId });
    }, 
    /**
     * 苗情分析
     * @param {*} areaId 区域id
     * @param {*} shootTime 拍摄时间
     * @returns Promise 
     */
    'findCropCasePicAnalyse': function (areaId, shootTime) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1ee49b9b848c08ce53e3a85de17a49591fec07d1', {areaId, shootTime})
            : execute(concreteModuleName, `/CropCaseService/findCropCasePicAnalyse`, 'json', 'POST', { areaId, shootTime });
    }, 
    /**
     * 根据作物种类获取对应生长期
     * @param {*} crop 作物类型
     * @returns Promise 
     */
    'findCropPeriod': function (crop) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2c3eeef5e66f94a69f482307e7075f6902c0f4eb', crop)
            : execute(concreteModuleName, `/CropCaseService/findCropPeriod`, 'json', 'POST', { crop });
    }, 
    /**
     * 苗情修改说明
     * @param {*} id 记录id
     * @param {*} annotation 说明
     * @returns Promise 
     */
    'updateCropCaseAnnotation': function (id, annotation) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '148d964266b4872a7f0c4a59ad411a5c23d35d58', {id, annotation})
            : execute(concreteModuleName, `/CropCaseService/cropCaseAnnotation`, 'json', 'PUT', { id, annotation });
    }, 
    /**
     * 苗情首页显示图片
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findCropCasePic': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f2fd57c9f26d611d9abda62911bfa2a3a2dbebf6', areaId)
            : execute(concreteModuleName, `/CropCaseService/findCropCasePic`, 'json', 'POST', { areaId });
    }
}

export default CropCaseService

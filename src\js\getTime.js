import { log } from "ezuikit-js";

let gettime={
  // 获取日期
  getWeekDate() {
    let now = new Date();
    let day = now.getDay();
    let weeks = new Array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
    let week = weeks[day];
    return week
  },
  // 获取时间
  getReallTime(){
    let myDate = new Date(); 
    let hours = myDate.getHours(); 
    let minutes = myDate.getMinutes(); 
    if (hours >= 1 && hours <= 9) {
      hours = "0" + hours;
    }
    if (minutes >= 1 && minutes <= 9) {
      minutes = "0" + minutes;
    }
    let time=hours+":"+minutes
    return time
  },
  // 获取当前日期
  currentdate(){
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate
},
  // 获取当前日期--月-日
  currentMonthDay(){
    var date = new Date();
    var seperator1 = "-";
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
      month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = "0" + strDate;
    }
    var currentdate =  month + seperator1 + strDate;
    return currentdate
  },
  //2、得到本周、上周、下周的起始、结束日期
  //type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，dates为数字类型，不传或0代表本周，-1代表上周，1代表下周
  getMonday(type, dates) {
    var now = new Date();
    var nowTime = now.getTime();
    var day = now.getDay();
    var longTime = 24 * 60 * 60 * 1000;
    var n = longTime * 7 * (dates || 0);
    if (type == "s") {
      var dd = nowTime - (day - 1) * longTime + n;
    };
    if (type == "e") {
      var dd = nowTime + (7 - day) * longTime + n;
    };
    dd = new Date(dd);
    var y = dd.getFullYear();
    var m = dd.getMonth() + 1;
    var d = dd.getDate();
    m = m < 10 ? "0" + m: m;
    d = d < 10 ? "0" + d: d;
    var day = y + "-" + m + "-" + d;
    console.log(day);
    return day;
  },
  // 获取前七天
  getSevenAaysAgo(){
    let time=(new Date).getTime()-6*24*60*60*1000;
    let sevenAay=new Date(time);
    sevenAay=sevenAay.getFullYear() + "-" + (sevenAay.getMonth()>= 9 ? (sevenAay.getMonth() + 1) : "0" + (sevenAay.getMonth() + 1)) + "-" +(sevenAay.getDate()> 9 ? (sevenAay.getDate()) : "0" + (sevenAay.getDate()));
    return sevenAay
  },
  // 获取当前日期前半月
  getHalfMonthBefore(){
    let time=(new Date).getTime()-15*24*60*60*1000;
    let date=new Date(time);
    date=date.getFullYear() + "-" + (date.getMonth()>= 9 ? (date.getMonth() + 1) : "0" + (date.getMonth() + 1)) + "-" +(date.getDate()> 9 ? (date.getDate()) : "0" + (date.getDate()));
    return date
  },
  // 获取当前日期后半月
  getHalfMonthAfter(){
    let time=(new Date).getTime()+15*24*60*60*1000;
    let date=new Date(time);
    date=date.getFullYear() + "-" + (date.getMonth()>= 9 ? (date.getMonth() + 1) : "0" + (date.getMonth() + 1)) + "-" +(date.getDate()> 9 ? (date.getDate()) : "0" + (date.getDate()));
    return date
  },
  // 获取当前日期前三十天日期
  getPreMonthDay(date){
    var arr = date.split('-');
    var year = arr[0];     //当前年
    var month = arr[1];      //当前月
    var day = arr[2];        //当前日
    //验证日期格式为YYYY-MM-DD
    var reg = date.match(/^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$/);
    if ((!reg) || (month > 12) || (day > 31)) {
        console.log('日期或格式有误！请输入正确的日期格式（年-月-日）');
        return;
    }
    var pre_year = year;     //前一个月的年
    var pre_month = parseInt(month) - 1;      //前一个月的月，以下几行是上月数值特殊处理
    if (pre_month === 0) {
        pre_year = parseInt(pre_year) - 1;
        pre_month = 12;
    }
    var pre_day = parseInt(day);       //前一个月的日，以下几行是特殊处理前一个月总天数
    var pre_month_alldays = new Date(pre_year, pre_month, 0).getDate();    //巧妙处理，返回某个月的总天数
    if (pre_day > pre_month_alldays) {
        pre_day = pre_month_alldays;
    }
    if (pre_month < 10) {   //补0
        pre_month = '0' + pre_month;
    }
    if (pre_day < 10) {   //补0
        pre_day = '0' + pre_day;
    }
    var pre_month_day = pre_year + '-' + pre_month + '-' + pre_day;
    return pre_month_day;
  },
}
export {gettime}
<template>
  <div class="soilEMDialog dialogStyle">
    <el-dialog
      :title="title"
      width="74.6%"
      :visible.sync="dialogVisible"
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open='handleOpen'>
      <div class="line"></div>
      <div class="close" @click="dialogVisible=false">
        <img src="../../assets/image/centralControlPlatform/close.png" alt="">
      </div>
      <div class="time">
        数据采集时间：{{soilCollectTime || '暂无数据'}}
      </div>
      <div class="handleBox">
        <div class="handleBox_item handleBox_select formStyle">
          <el-select v-model="layer" placeholder="请选择土壤层" popper-class="selectStyle_list" @change="layerChange">
            <el-option
              v-for="item in soilLayerData"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <!-- <div class="handleBox_item searchButtonStyle2">
          <el-button>查看</el-button>
        </div> -->
        <div class="handleBox_item searchButtonStyle2" v-if="areaIsJudge">
          <el-button @click="soilReportUpdateDialogOpen">数据汇报</el-button>
        </div>
        <div class="handleBox_item searchButtonStyle">
          <el-button @click="soilOfHistoryDialogOpen">土壤历史记录</el-button>
        </div>
      </div>
      <div class="dataList">
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/monitoringCenter/icon1.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.soilTemperature==null">--</div>
            <div v-else>{{newInfo.soilTemperature}}℃</div>
            <div>温度</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/monitoringCenter/icon4.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.soilHumidity==null">--</div>
            <div v-else>{{newInfo.soilHumidity}}%</div>
            <div>墒情</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/monitoringCenter/icon5.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.conductivity==null">--</div>
            <div v-else>{{newInfo.conductivity}}μS/cm</div>
            <div>电导率</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/monitoringCenter/icon6.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.ph==null">--</div>
            <div v-else>{{newInfo.ph}}</div>
            <div>pH值</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/centralControlPlatform/icon12.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.nitrogen==null">--</div>
            <div v-else>{{newInfo.nitrogen}}mg/kg</div>
            <div>氮浓度</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/centralControlPlatform/icon14.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.phosphorus==null">--</div>
            <div v-else>{{newInfo.phosphorus}}mg/kg</div>
            <div>磷浓度</div>
          </div>
        </div>
        <div class="list_item">
          <div class="item_img">
            <img src="../../assets/image/centralControlPlatform/icon13.png" alt="">
          </div>
          <div class="item_text">
            <div v-if="newInfo.potassium==null">--</div>
            <div v-else>{{newInfo.potassium}}mg/kg</div>
            <div>钾浓度</div>
          </div>
        </div>
      </div>
      <div class="chartList_item_checkBtn">
        <div class="checkBtn2 checkBtn" @click="timeCheck(1)" :class="{active2:active==1}">
          近七天
        </div>
        <div class="checkBtn2 checkBtn" @click="timeCheck(2)" :class="{active2:active==2}">
          近30天
        </div>
      </div>
      <div class="chartList">
        <div class="chartList_left">
          <div class="chartList_item">
            <div class="chartList_item_title">土壤温湿度变化表</div>
            <div class="chartList_item_chart">
              <znyEchart :chartData="soilTHLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
          <div class="chartList_item">
            <div class="chartList_item_title">PH值变化表</div>
            <div class="chartList_item_chart">
              <znyEchart :chartData="phLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
        </div>
        <div class="chartList_right">
          <div class="chartList_item">
            <div class="chartList_item_title">电导率变化表</div>
            <div class="chartList_item_chart">
              <znyEchart :chartData="conductivityLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
          <div class="chartList_item">
            <div class="chartList_item_title">氮磷钾变化表</div>
            <div class="chartList_item_chart">
              <znyEchart :chartData="NpkLineOption" :chartType="'line'"></znyEchart>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {chart} from '../../js/chart.js';
import data from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$土壤层.js';
import SoilService from '../../jaxrs/concrete/com.zny.ia.api.SoilService.js';
import {gettime} from '../../js/getTime.js';
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService.js';

export default {
  data(){
    return{
      title:"",
      soilCollectTime:'',
      areaId:"",
      areaIsJudge:true,//判断当前用户能否操作该区域
      dialogVisible:false,
      newInfo:{},//土壤实时数据
      active:1,//选择按钮1—>近七天 2->近30天
      layer:0,
      soilLayerData:data._toArray(),//土壤层数据
      soilTHLineOption:{//土壤温湿度折线图数据
        show:true,
        option:{},
      },
      phLineOption:{//PH值折线图数据
        show:true,
        option:{},
      },
      conductivityLineOption:{//电导率折线图数据
        show:true,
        option:{},
      },
      NpkLineOption:{//氮磷钾折线图数据
        show:true,
        option:{},
      },
    }
  },
  mounted(){
    // 弹窗打开
    this.listen('soilEMDialogOpen', (block) => {
      this.areaId=block.areaId
      this.title=block.areaName
      this.dialogVisible=true
      this.areaJudge()
    })
    // 数据上报成功，刷新数据
    this.listen('soilUpdateSuccess', () => {
      this.getListChartData()
      this.findNewInfo()
    })
  },
  methods:{
    // 判断当前用户能否操作该区域
    areaJudge(){
      CommonService.areaJudge(this.areaId)
      .then(res=>{
        // console.log(res);
        this.areaIsJudge=res
      })
    },
    // 土壤层数据改变
    layerChange(){
      this.getListChartData()
      this.findNewInfo()
    },
    // 选择近七天或者近30天
    timeCheck(num){
      this.active=num
      this.getListChartData()
    },
    // 弹窗打开时调用
    handleOpen(){
      this.$nextTick(()=>{
        this.getListChartData()
        this.findNewInfo()
      })
    },
    // 获取土壤实时数据
    findNewInfo(){
      SoilService.findNewInfo(this.layer,this.areaId)
      .then(res=>{
        this.newInfo=res
        this.soilCollectTime=res.createTime
      })
    },
    // 获取图表数据
    getListChartData(){
      let now=gettime.currentdate()
      let start=this.active==1?gettime.getSevenAaysAgo():gettime.getPreMonthDay(now)
      let po={
        areaId:this.areaId,	
        layer:this.layer,
        startTime:start,
        endTime:now
      }
      SoilService.listChart(po)
      .then(res=>{
        if(res==null||res.length==0){
          this.soilTHLineOption={//土壤温湿度折线图数据
            show:false,
            option:{},
          }
          this.phLineOption={//PH值折线图数据
            show:false,
            option:{},
          }
          this.conductivityLineOption={//电导率折线图数据
            show:false,
            option:{},
          }
          this.NpkLineOption={//氮磷钾折线图数据
            show:false,
            option:{},
          }
          return 
        }
        let name=[],soilT=[],soilH=[],ph=[],conductivity=[],N=[],P=[],K=[]
        res.forEach(e => {
          name.push(e.showTime)
          soilT.push(e.value.temperatureAvg)
          soilH.push(e.value.humidityAvg)
          ph.push(e.value.phAvg)
          conductivity.push(e.value.conductivityAvg)
          N.push(e.value.nitrogenAvg)
          P.push(e.value.phosphorusAvg)
          K.push(e.value.potassiumAvg)
        });
        chart.soilTHLine(name,soilT,soilH).then(data=>{
          this.soilTHLineOption=data
        })
        chart.phLine(name,ph).then(data=>{
          this.phLineOption=data
        })
        chart.conductivityLine(name,conductivity).then(data=>{
          this.conductivityLineOption=data
        })
        chart.NpkLine(name,N,P,K).then(data=>{
          this.NpkLineOption=data
        })
      })
    },
    // 打开数据汇报弹窗
    soilReportUpdateDialogOpen(){
      this.zEmit('soilReportUpdateDialogOpen',this.areaId);
    },
    // 打开历史记录弹窗
    soilOfHistoryDialogOpen(){
      this.zEmit('soilOfHistoryDialogOpen');
    },
  },
  beforeDestroy() {
    this.removeAllListener();
  },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/soilEMDialog.less';
</style>
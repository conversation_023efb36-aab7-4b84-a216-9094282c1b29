.smartIOTDataSwiper{
  width: 461px;
  height: 130px;
  .swiper-container{
    width: 461px;
    height: 130px;
    .swiper-wrapper{
      width: 461px;
      height: 130px;
      .swiper-slide {
        width: 30%;
        text-align: center;
        font-size: 18px;
        // background: #fff;
        /* Center slide text vertically */
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        .swiper_item_img{
          img{
            width: 34px;
            height: 50px;
          }
        }
        .swiper_item_text{
          // width: 100px;
          font-weight: 400;
          div:first-child {
            font-size: 16px;
            color: #70A8FF;
          }
          div:last-child {
            margin-top: 10px;
            font-size: 12px;
            color: #B8D4FF;
          }
        }
      }
    }
  }
}
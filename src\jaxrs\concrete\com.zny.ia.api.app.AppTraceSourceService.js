/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppTraceSourceService = {
    /**
     * 溯源档案统计
     * @returns Promise 
     */
    'traceSourceStatistic': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2524ea1f0d74a51c61befdcd8fd8743b7946d8a1')
            : execute(concreteModuleName, `/App/TraceSourceService/traceSourceStatistic`, 'json', 'GET');
    }, 
    /**
     * 我的溯源档案
     * @param {*} traceSourceRecordPo 查询条件
     * @returns Promise 
     */
    'listTraceSourceRecordOfCurrentUser': function (traceSourceRecordPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c5ffe6f27ddb987e44e31ee568ee2085d196d862', traceSourceRecordPo)
            : execute(concreteModuleName, `/App/TraceSourceService/listTraceSourceRecordOfCurrentUser`, 'json', 'POST', traceSourceRecordPo);
    }
}

export default AppTraceSourceService

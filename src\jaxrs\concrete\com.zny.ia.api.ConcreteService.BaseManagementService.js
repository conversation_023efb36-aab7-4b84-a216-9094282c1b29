/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const BaseManagementService = {
    /**
     * 汇报
     * @param {*} reportInformationPo 汇报信息
     * @returns Promise 
     */
    'areaReport': function (reportInformationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'daae2da0626be792988c820a6f6748785f5f61a5', reportInformationPo)
            : execute(concreteModuleName, `/BaseManagementService/areaReport`, 'json', 'POST', reportInformationPo);
    }, 
    /**
     * 删除区域
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'deleteAreaByAreaId': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'ca64372720fc7e266808e9a0f62097d041b16946', areaId)
            : execute(concreteModuleName, `/BaseManagementService/deleteAreaByAreaId`, 'json', 'DELETE', { areaId });
    }, 
    /**
     * 修改区域下设备
     * @param {*} changeAreaEquipmentPo 修改区域下设备
     * @returns Promise 
     */
    'changeAreaEquipment': function (changeAreaEquipmentPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f6c219ea3d4ec8392d69c958ec240ec0978939de', changeAreaEquipmentPo)
            : execute(concreteModuleName, `/BaseManagementService/changeAreaEquipment`, 'json', 'POST', changeAreaEquipmentPo);
    }, 
    /**
     * 添加区域及区域下设备
     * @param {*} saveAreaEquipmentPo 新增区域
     * @returns Promise 
     */
    'saveAreaEquipment': function (saveAreaEquipmentPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '89258526b739264ab260b5afd3e6c940d2952db5', saveAreaEquipmentPo)
            : execute(concreteModuleName, `/BaseManagementService/saveAreaEquipment`, 'json', 'POST', saveAreaEquipmentPo);
    }, 
    /**
     * 区域总览列表信息
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'areaOverviewInformation': function (num, pageSize, areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9b9df7dd21761a6537855892811af5a8d38f810f', {num, pageSize, areaId})
            : execute(concreteModuleName, `/BaseManagementService/areaOverviewInformation`, 'json', 'POST', { num, pageSize, areaId });
    }, 
    /**
     * 基地总览区域列表信息
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @param {*} deviceType 设备类型
     * @param {*} areaName 区域名称
     * @returns Promise 
     */
    'baseAreaOverviewInformation': function (num, pageSize, deviceType, areaName) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'da2e56c99cd9aa2c0aa9a4e1600d557fa4354cc2', {num, pageSize, deviceType, areaName})
            : execute(concreteModuleName, `/BaseManagementService/baseAreaOverviewInformation`, 'json', 'POST', { num, pageSize, deviceType, areaName });
    }, 
    /**
     * 区域下设备信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'areaEquipmentListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '60c713125e5df97e578c24ccbb5f01a3623ce427', areaId)
            : execute(concreteModuleName, `/BaseManagementService/areaEquipmentListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 地区统计
     * @param {*} areaId 区域Id, 不传值代表总览
     * @returns Promise 
     */
    'areaStatistics': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e62ab95c31f63a6689eeecf02764e1a4c18e5cde', areaId)
            : execute(concreteModuleName, `/BaseManagementService/areaStatistics`, 'json', 'POST', { areaId });
    }, 
    /**
     * 当前用户下所有区域的汇报历史记录
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @returns Promise 
     */
    'reportHistoryListInformation': function (num, pageSize) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8f723fcde8b47435c8c522d4f6f3b4194a700513', {num, pageSize})
            : execute(concreteModuleName, `/BaseManagementService/reportHistoryListInformation`, 'json', 'POST', { num, pageSize });
    }
}

export default BaseManagementService

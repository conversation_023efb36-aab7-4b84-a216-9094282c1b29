/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const RemoteSensingService = {
    /**
     * 作物长势分析
     * @returns Promise 
     */
    'findRemoteCropHeight': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '55f44caf458236dc5287c0f930dbfd73452903d6')
            : execute(concreteModuleName, `/RemoteSensingService/findRemoteCropHeight`, 'json', 'GET');
    }, 
    /**
     * 上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'addRemote': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'aa382f0bf362b2291c5483be37c7e4f4024b9209', po)
            : execute(concreteModuleName, `/RemoteSensingService/addRemote`, 'json', 'POST', po);
    }, 
    /**
     * 遥感相关历史
     * @param {*} page 第几页
     * @param {*} num 页大小
     * @returns Promise 
     */
    'listRemoteHistory': function (page, num) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '15d386444ec359478d296051ae3088c88ea48a37', {page, num})
            : execute(concreteModuleName, `/RemoteSensingService/listRemoteHistory`, 'json', 'POST', { page, num });
    }, 
    /**
     * 遥感详情
     * @param {*} id 遥感记录id
     * @returns Promise 
     */
    'detailRemote': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '12acaccdf8599500e492266d8d739224ab250708', id)
            : execute(concreteModuleName, `/RemoteSensingService/detailRemote`, 'json', 'POST', { id });
    }, 
    /**
     * 补充遥感分析结果
     * @param {*} po 
     * @returns Promise 
     */
    'addRemoteDescription': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2b2da13b463fe779e7f039d292ffff6b88f58c44', po)
            : execute(concreteModuleName, `/RemoteSensingService/addRemoteDescription`, 'json', 'POST', po);
    }, 
    /**
     * 根据时间和区域获取作物种类
     * @param {*} dateTime 日期时间 yyyy-MM-dd HH:mm:ss
     * @param {*} areaIdList 区域id列表
     * @returns Promise 
     */
    'listCropType': function (dateTime, areaIdList) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '99af8876e500eedce83c99901ed37aa19557d9bb', {dateTime, areaIdList})
            : execute(concreteModuleName, `/RemoteSensingService/listCropType`, 'json', 'POST', { dateTime, areaIdList });
    }, 
    /**
     * 遥感作物占比
     * @returns Promise 
     */
    'findRemoteCrop': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2f05ceb31747d91267fe71147cda0ead213ae9b9')
            : execute(concreteModuleName, `/RemoteSensingService/findRemoteCrop`, 'json', 'GET');
    }
}

export default RemoteSensingService

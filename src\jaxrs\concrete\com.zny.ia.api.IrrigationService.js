/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const IrrigationService = {
    /**
     * 灌溉区域页面-控制水阀
     * @param {*} nodeId 主通道节点Id
     * @param {*} control 0、打开 1、关闭
     * @returns Promise 
     */
    'controlTraceSourceWater': function (nodeId, control) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b6c320fd3d9ce72dcbc9d8906c4d2483fca114f2', {nodeId, control})
            : execute(concreteModuleName, `/IrrigationService/controlTraceSourceWater`, 'json', 'POST', { nodeId, control });
    }, 
    /**
     * 灌溉区域页面-控制施肥阀
     * @param {*} nodeId 主通道节点Id
     * @param {*} control 0、打开 1、关闭
     * @returns Promise 
     */
    'controlTraceSourceFertilization': function (nodeId, control) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'dcd5a22600e9fba7ab2aec1c5f2d942aa79ba69a', {nodeId, control})
            : execute(concreteModuleName, `/IrrigationService/controlTraceSourceFertilization`, 'json', 'POST', { nodeId, control });
    }, 
    /**
     * 灌溉主页面-最近七天土壤墒情变化
     * @param {*} areaId 区域Id, 不传值代表查询所有区域
     * @returns Promise 
     */
    'cumulativeSoilMoistureListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1b7c48c2174545106bfed4f654da361c2d6a7e9c', areaId)
            : execute(concreteModuleName, `/IrrigationService/cumulativeSoilMoistureInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉区域页面-区域基本信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'irrigationAreaInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '20d2a7f6d42ac8130d4bc7561cb76ca93679b910', areaId)
            : execute(concreteModuleName, `/IrrigationService/irrigationAreaInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉主页面-最近七天累计灌溉统计
     * @param {*} areaId 区域Id, 不传值代表查询所有区域
     * @returns Promise 
     */
    'cumulativeIrrigationListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f0c9bd12a741412860109d87a1181d0f85d43bc3', areaId)
            : execute(concreteModuleName, `/IrrigationService/cumulativeIrrigationInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 获取某个区域下所有灌溉节点设备信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'irrigationNodeListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '706642d2bcf48032ec9f49cf1ce7fb0f787ae33f', areaId)
            : execute(concreteModuleName, `/IrrigationService/irrigationNodeListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 灌溉区域页面-瞬时数据
     * @param {*} nodeId 节点Id, 不传值代表查询主通道
     * @returns Promise 
     */
    'instantaneousInformation': function (nodeId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b43240cb68c44cf4f337eac398a2dedb53d72fd0', nodeId)
            : execute(concreteModuleName, `/IrrigationService/instantaneousInformation`, 'json', 'POST', { nodeId });
    }, 
    /**
     * 灌溉主页面-正在灌溉地区
     * @returns Promise 
     */
    'irrigationWatering': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '7c3c4a62f40eadc01ac1b1ad45b6bed83fdc369d')
            : execute(concreteModuleName, `/IrrigationService/irrigationWatering`, 'json', 'GET');
    }, 
    /**
     * 灌溉历史数据（按次）
     * @param {*} pageRequest 查询条件
     * @returns Promise 
     */
    'irrigationHistoryList': function (pageRequest) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1fbc8a9ecea334310a765db7e84808f57c3fdca5', pageRequest)
            : execute(concreteModuleName, `/IrrigationService/irrigationHistoryList`, 'json', 'POST', pageRequest);
    }
}

export default IrrigationService

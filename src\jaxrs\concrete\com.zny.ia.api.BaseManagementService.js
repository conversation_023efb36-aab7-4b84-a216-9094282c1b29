/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const BaseManagementService = {
    /**
     * 汇报
     * @param {*} reportInformationPo 汇报信息
     * @returns Promise 
     */
    'areaReport': function (reportInformationPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '798e9d5d1af9b5f364726935ae8102935e0c6341', reportInformationPo)
            : execute(concreteModuleName, `/BaseManagementService/areaReport`, 'json', 'POST', reportInformationPo);
    }, 
    /**
     * 修改区域下设备
     * @param {*} changeAreaEquipmentPo 修改区域下设备
     * @returns Promise 
     */
    'changeAreaEquipment': function (changeAreaEquipmentPo) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '633f76d72aef8e6f35ba1a0845ca3380c2956dab', changeAreaEquipmentPo)
            : execute(concreteModuleName, `/BaseManagementService/changeAreaEquipment`, 'json', 'POST', changeAreaEquipmentPo);
    }, 
    /**
     * 区域总览列表信息
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'areaOverviewInformation': function (num, pageSize, areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'bde87a02acc8281a049630adaf71a301b47551c3', {num, pageSize, areaId})
            : execute(concreteModuleName, `/BaseManagementService/areaOverviewInformation`, 'json', 'POST', { num, pageSize, areaId });
    }, 
    /**
     * 基地总览区域列表信息
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @param {*} deviceType 设备类型
     * @returns Promise 
     */
    'baseAreaOverviewInformation': function (num, pageSize, deviceType) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '5c2a150c2fb5e2ed8d3a5dd171707bef40c01d58', {num, pageSize, deviceType})
            : execute(concreteModuleName, `/BaseManagementService/baseAreaOverviewInformation`, 'json', 'POST', { num, pageSize, deviceType });
    }, 
    /**
     * 区域下设备信息
     * @param {*} areaId 区域Id
     * @returns Promise 
     */
    'areaEquipmentListInformation': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '95b51dadc1b773530bb140362d608c1dbf348b83', areaId)
            : execute(concreteModuleName, `/BaseManagementService/areaEquipmentListInformation`, 'json', 'POST', { areaId });
    }, 
    /**
     * 地区统计
     * @param {*} areaId 区域Id, 不传值代表总览
     * @returns Promise 
     */
    'areaStatistics': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a4716c37f14ccf14b8bd114ef74debf8c857fc6d', areaId)
            : execute(concreteModuleName, `/BaseManagementService/areaStatistics`, 'json', 'POST', { areaId });
    }, 
    /**
     * 当前用户下所有区域的汇报历史记录
     * @param {*} num 第几页
     * @param {*} pageSize 每页多少条数据
     * @returns Promise 
     */
    'reportHistoryListInformation': function (num, pageSize) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '964fd6584e774496233014700a271c6947e81c04', {num, pageSize})
            : execute(concreteModuleName, `/BaseManagementService/reportHistoryListInformation`, 'json', 'POST', { num, pageSize });
    }
}

export default BaseManagementService

<template>
    <div class="meteorology AMapContainer">
        <div class="meteorology_left">
            <div class="InsectSituation_left_smartIOTData">
                <div class="title InsectSituation_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧物联数据</span>
                </div>
                <div class="InsectSituation_left_smartIOTData_con">
                    <div class="list_con2" style="height:98%">
                        <div class="table_header_box">
                            <div class="table_header_box_row1">
                                <div class="handleBox_item formStyle">
                                    <el-select
                                        v-model="district"
                                        clearable
                                        popper-class="selectStyle_list"
                                        @change="selectionRegion(district)"
                                    >
                                        <el-option
                                            v-for="(item, index) in allcities"
                                            :key="index"
                                            :label="item.areaName"
                                            :value="item.areaId"
                                        ></el-option>
                                    </el-select>
                                </div>
                                <img src="../assets/image/eppoWisdom/return.png" alt="" @click="stepBackward" />
                            </div>
                        </div>
                        <div class="conter_tree">
                            <el-tree :data="treedata" :props="defaultProps" @node-click="handleNodeClick" accordion>
                                <template slot-scope="{ node, data }">
                                    <span class="span-ellipsis" :class="{ color: data.alarmStatus }">
                                        {{ node.label }}
                                    </span>
                                </template>
                            </el-tree>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="meteorology_right">
            <div class="title meteorology_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="meteorology_right_btn">
                <EquipmentButton />
            </div>
            <!-- <div class="meteorology_right_con" @click="meteorologyEMDialogOpen"> -->
            <div class="meteorology_right_con">
                <zny-map
                    ref="aMap"
                    style="width: 100%; height: 100%"
                    :map-options="amapOptions"
                    :max-zoom="20"
                    :roadnet="true"
                ></zny-map>
                <!-- <div class="twoDimensionalMap" @click="meteorologyEMDialogOpen"></div> -->
            </div>
        </div>
        <div class="platDetailsInfoWindow" v-if="selectedBlock">
            <img src="../assets/image/eppoWisdom/guanbi.png" alt="" @click="selectedBlock = null" />
            <div class="conter">
                <div class="conter_title">{{ selectedBlock.equipmentName }}</div>
                <div class="row1">
                    <span>报警状态：</span>
                    <span class="color_red" v-if="selectedBlock.alarmStatus">报警</span>
                    <span v-else>正常</span>
                </div>
                <div class="row1">
                    <span>归属区域：</span>

                    <span v-for="(item, index) in selectedBlock.areaName" :key="index">
                        <span v-if="index == 0">{{ item }}</span>
                        <span v-if="index !== 0">,{{ item }}</span>
                    </span>
                </div>
                <div class="row1">
                    <span>运行状态：</span>
                    <span class="color_2" v-if="selectedBlock.online">在线</span>
                    <span class="color_3" v-else>离线</span>
                </div>
                <div class="row1">
                    <span>设备ID：</span>
                    <span>{{ selectedBlock.equipmentId }}</span>
                </div>
                <div class="row1">
                    <span>设备型号：</span>
                    <span>{{ selectedBlock.model }}</span>
                </div>

                <div class="row1">
                    <span>维护电话：</span>
                    <span>{{ selectedBlock.telephone }}</span>
                </div>
                <div class="row1">
                    <span>设备厂商：{{ selectedBlock.manufacturer }}</span>
                </div>
                <div class="row1">
                    <span>地理位置：{{ selectedBlock.position }}</span>
                </div>
                <div class="row1">
                    <span>经纬度：{{ selectedBlock.longitude }}, {{ selectedBlock.latitude }}</span>
                </div>
                <div class="row1">
                    <span>设备说明：{{ selectedBlock.instruction }}</span>
                </div>
            </div>
        </div>
        <!-- 气象设备弹窗 -->
        <div style="display: none">
            <div class="equipment_infoWindow infoWindow" ref="equipmentIW" v-if="equipmentDetail">
                <div class="iw_title">
                    {{ equipmentDetail.equipmentName }}
                </div>
                <div class="iw_con">
                    <div class="iw_detailsItem">设备ID：{{ equipmentDetail.equipmentId }}</div>
                    <div class="iw_detailsItem">
                        状态：
                        <span v-if="equipmentDetail.onlineType == 1" style="color:#00FFAE">在线</span>
                        <span v-if="equipmentDetail.onlineType == 2" style="color:#999999">离线</span>
                        <span v-if="equipmentDetail.onlineType == 3" style="color:#EE0101">报警</span>
                    </div>
                    <!-- <div class="iw_detailsItem">
                    维护：{{equipmentDetail.telephone}}
                </div>
                <div class="iw_detailsItem">
                    厂商：{{equipmentDetail.manufacturer}}
                </div> -->
                    <div class="iw_detailsItem">位置：{{ equipmentDetail.position }}</div>
                    <div class="iw_detailsItem">说明：{{ equipmentDetail.instruction }}</div>
                    <div class="iw_detailsItem">({{ equipmentDetail.longitude }},{{ equipmentDetail.latitude }})</div>
                </div>
                <div class="top_left">
                    <img src="../assets/image/monitoringCenter/top_left.png" alt="" />
                </div>
                <div class="top_right">
                    <img src="../assets/image/monitoringCenter/top_right.png" alt="" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// import znyAMap from '../components/znyAMap.vue'
// import EquipmentAlarm from '../components/equipmentAlarm.vue'
// import SmartIOTDataSwiper from '../components/smartIOTDataSwiper.vue'
import EquipmentButton from '../components/equipmentButton.vue'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
import NetMonitorService from '../jaxrs/concrete/com.zny.ia.api.NetMonitorService.js'
import { mapHandle } from '../js/mapHandle.js'
export default {
    components: {
        // EquipmentAlarm,
        // SmartIOTDataSwiper,
        EquipmentButton,
        // znyAMap,
    },
    data() {
        return {
            district: undefined,
            // 所有区域基本信息
            allcities: [],
            treedata: [],
            defaultProps: {
                children: 'children',
                label: 'equipmentName',
            },
            amapOptions: {
                zoom: 15,
                center: [117.12665, 36.6584],
                amapTileUrl: localStorage.getItem('amapTileUrl'),
            },
            equipmentList: [],
            selectedBlock: null,
            equipmentDetail: {}, //设备详情(弹窗)
        }
    },
    created() {
        this.adddistrict()
        this.selectionRegion()
    },
    mounted() {
    },
    methods: {
        adddistrict() {
            CommonService.allAreaListInformation().then(res => {
                this.allcities = res
            })
        },
        handleNodeClick(data) {

            if (data.equipmentId) {
                NetMonitorService.netMonitorEqDetail(data.equipmentId).then(res => {
                    this.selectedBlock = res
                    let map = this.$refs['aMap']
                    map.center([res.longitude, res.latitude])

                })
            }
        },
        selectionRegion(district) {
            this.treedata = []
            let map = this.$refs['aMap']
            if (this.equipmentList.length !== 0) {
                map.removeMarker()
            }
            NetMonitorService.netMonitorList(district).then(res => {
                // // 气象
                let meteorological = []
                let meteorologicalonline = []
                let meteorologicalalarmStatus = []
                let plantlsitonline = []
                let plantlsitalarmStatus = []
                let plantlsit = []
                let miaosFolksonline = []
                let miaosFolksalarmStatus = []
                let miaosFolks = []
                let irrigationDrainageonline = []
                let irrigationDrainagealarmStatus = []
                let irrigationDrainage = []
                let soillistonline = []
                let soillistalarmStatus = []
                let soillist = []
                let waterQualityonline = []
                let waterQualityalarmStatus = []
                let waterQuality = []
                res.forEach(item => {
                    if (item.equipmentTypeId == 1) {
                        if (item.online == true) {
                            meteorologicalonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            meteorologicalalarmStatus.push(item)
                        }
                        meteorological.push(item)
                    } else if (item.equipmentTypeId == 4) {
                        if (item.online == true) {
                            plantlsitonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            plantlsitalarmStatus.push(item)
                        }
                        plantlsit.push(item)
                    } else if (item.equipmentTypeId == 6) {
                        if (item.online == true) {
                            miaosFolksonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            miaosFolksalarmStatus.push(item)
                        }
                        miaosFolks.push(item)
                    } else if (item.equipmentTypeId == 3) {
                        if (item.online == true) {
                            irrigationDrainageonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            irrigationDrainagealarmStatus.push(item)
                        }
                        irrigationDrainage.push(item)
                    } else if (item.equipmentTypeId == 2) {
                        if (item.online == true) {
                            soillistonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            soillistalarmStatus.push(item)
                        }
                        soillist.push(item)
                    } else if (item.equipmentTypeId == 5) {
                        if (item.online == true) {
                            waterQualityonline.push(item)
                        }
                        if (item.alarmStatus == true) {
                            waterQualityalarmStatus.push(item)
                        }
                        waterQuality.push(item)
                    }
                })

                if (meteorological.length !== 0) {
                    this.treedata.push({
                        equipmentName: `气象站(${meteorologicalonline.length}/${meteorological.length})`,
                        children: meteorological,
                        alarmStatus: meteorologicalalarmStatus.length == 0 ? false : true,
                    })
                }
                // // 虫情
                if (plantlsit.length !== 0) {
                    this.treedata.push({
                        equipmentName: `虫情监测仪(${plantlsitonline.length}/${plantlsit.length})`,
                        children: plantlsit,
                        alarmStatus: plantlsitalarmStatus.length == 0 ? false : true,
                    })
                }
                // // 苗情
                if (miaosFolks.length !== 0) {
                    this.treedata.push({
                        equipmentName: `苗情监控探头(${miaosFolksonline.length}/${miaosFolks.length})`,
                        children: miaosFolks,
                        alarmStatus: miaosFolksalarmStatus.length == 0 ? false : true,
                    })
                }
                // // 灌排
                if (irrigationDrainage.length !== 0) {
                    this.treedata.push({
                        equipmentName: `灌溉系统节点(${irrigationDrainageonline.length}/${irrigationDrainage.length})`,
                        children: irrigationDrainage,
                        alarmStatus: irrigationDrainagealarmStatus.length == 0 ? false : true,
                    })
                }
                // // 土壤
                if (soillist.length !== 0) {
                    this.treedata.push({
                        equipmentName: `墒情监控设备(${soillistonline.length}/${soillist.length})`,
                        children: soillist,
                        alarmStatus: soillistalarmStatus.length == 0 ? false : true,
                    })
                }
                // // 水质
                if (waterQuality.length !== 0) {
                    this.treedata.push({
                        equipmentName: `水质检测仪(${waterQualityonline.length}/${waterQuality.length})`,
                        children: waterQuality,
                        alarmStatus: waterQualityalarmStatus.length == 0 ? false : true,
                    })
                }

                // this.treedata = res
                // console.log(this.treedata)
            })
            NetMonitorService.findEqInfo(district).then(res => {

                this.equipmentList = res
                this.equipmentList.forEach(e => {
                    this.showMarker(e)
                })
            })
        },

        // 在地图放marker
        showMarker(block) {
            let showImg, position, bgImg
            switch (block.equipmentType) {
                case 0:
                case 7:
                    position = block.polygonPath[0].border[0]
                    showImg = true
                    bgImg = require('@/assets/image/monitoringCenter/bg.png')
                    break
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    position = [block.longitude, block.latitude]
                    showImg = false
                    var onlineType = 0
                    if (block.online) {
                        if (block.alarmStatus) {
                            onlineType = 3
                        } else {
                            onlineType = 1
                        }
                    } else {
                        onlineType = 2
                    }
                    block.onlineType = onlineType

                    bgImg = require('@/assets/image/icon/' + block.equipmentType + '_' + onlineType + '.png')
                    break
            }
            let map = this.$refs['aMap']
            mapHandle.addMarker(map, block, {
                bgImg: bgImg,
                showImg: showImg,
                position: position,
                onClick: e => {
                    // this.handleNodeClick(e.marker.extData)
                    this.equipmentDetail = e.marker.extData
                    var iw2 = e.marker.popup({
                        content: this.$refs['equipmentIW'],
                        offset: [0, -70],
                        autoClose: false,
                    })
                    this.$refs['equipmentIW'].onclick = () => {
                        iw2.close()
                    }
                    this.$refs['equipmentIW'].onmousewheel = e => {
                        e.stopPropagation()
                    }
                },
            })
            map.setFitView()
        },
        stepBackward() {
            this.$router.back()
        },
    },
}
</script>

<style scoped lang="less">
.meteorology_left_smartIOTData,
.soil_left_smartIOTData,
.InsectSituation_left_smartIOTData {
    height: 100%;
}
.meteorology_left_smartIOTData_con,
.soil_left_smartIOTData_con,
.InsectSituation_left_smartIOTData_con {
    height: 96%;
    background: rgba(1, 13, 23, 0.2);
}
.list_con2 {
    padding-left: 23px;
    padding-right: 10px;
    .table_header_box {
        display: flex;
        flex-wrap: wrap;
        .table_header_box_row1 {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-top: 16px;
            .formStyle {
                height: 32px;
            }
            .row1_text {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #00f4da;
                margin: auto;
                margin-right: 16px;
            }
        }
    }
    .conter_tree {
        padding-top: 24px;
        height: 91%;
        overflow-y: auto;
        .el-tree {
            background: rgba(1, 13, 23, 0);
            color: #dfeef3;
        }
        ::v-deep .el-tree-node__content:hover {
            background: rgba(1, 13, 23, 0);
        }
        ::v-deep .el-upload-list__item:hover {
            background: rgba(1, 13, 23, 0);
        }
        ::v-deep .el-tree-node:focus > .el-tree-node__content {
            background: rgba(1, 13, 23, 0);
        }
        ::v-deep.el-tree-node.is-current > .el-tree-node__content {
            background: rgba(1, 13, 23, 0) !important;
        }
        ::v-deep .el-tree-node {
            font-size: 18px;
        }
    }
}
.color {
    color: #ee0101;
}
.platDetailsInfoWindow {
    position: fixed;
    right: 37px;
    top: 340px;
    width: 306px;
    height: 545px;
    background: rgba(0, 61, 66, 0.96);
    box-shadow: 0px 0px 14px 0px rgba(0, 244, 253, 0.6) inset;
    z-index: 103;
    img {
        position: relative;
        top: 11px;
        left: 281px;
        cursor: pointer;
    }
    .conter {
        height: 96%;
        overflow: auto;
        padding-left: 19px;
        padding-right: 28px;
        .conter_title {
            width: 100%;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #dfeef3;
            text-align: center;
            margin-top: 8px;
            margin-bottom: 28px;
        }
        .row1 {
            margin-top: 18px;
            width: 100%;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #dfeef3;
            .color_red {
                color: #ee0101;
            }
            .color_2 {
                color: #01c7b3;
            }
            .color_3 {
                color: #b5b7b7;
            }
        }
    }
}
</style>

<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/znyAMap.less';
</style>

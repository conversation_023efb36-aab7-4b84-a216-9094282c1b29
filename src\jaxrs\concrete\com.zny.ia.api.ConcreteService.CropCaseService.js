/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const CropCaseService = {
    /**
     * 上传图片
     * @param {*} po 
     * @returns Promise 
     */
    'cropCasePicImport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '3264cf3c64736d18c898353deff17e7252ae12c5', po)
            : execute(concreteModuleName, `/CropCaseService/cropCasePicImport`, 'json', 'POST', po);
    }, 
    /**
     * 苗情实时拍摄
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'realTimeShoot': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a2d71768e8705557583804ea1bccbf8991eaeffb', equipmentId)
            : execute(concreteModuleName, `/CropCaseService/realTimeShoot`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 苗情实时监控
     * @param {*} equipmentIds 设备id
     * @returns Promise 
     */
    'cropCaseMonitoring': function (equipmentIds) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '771612977febeca5ccd8de82925737daae06c2a1', equipmentIds)
            : execute(concreteModuleName, `/CropCaseService/cropCaseMonitoring`, 'json', 'POST', { equipmentIds });
    }, 
    /**
     * 苗情历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'cropCaseHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2f71321f06cd9e48758f4b540738dca34600ec6c', po)
            : execute(concreteModuleName, `/CropCaseService/cropCaseHistory`, 'json', 'POST', po);
    }, 
    /**
     * 苗情首页参考分析添加
     * @param {*} po 
     * @returns Promise 
     */
    'saveCropCasePicAnalyse': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a6875c2016a5699caf831332dc240be9bcac7294', po)
            : execute(concreteModuleName, `/CropCaseService/cropCasePicAnalyse`, 'json', 'POST', po);
    }, 
    /**
     * 展开全部监控
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findCropCaseMonitorList': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e94e54811ff5c533107e82f176857e9fab5445d7', areaId)
            : execute(concreteModuleName, `/CropCaseService/findCropCaseMonitorList`, 'json', 'POST', { areaId });
    }, 
    /**
     * 苗情分析
     * @param {*} areaId 区域id
     * @param {*} shootTime 拍摄时间
     * @returns Promise 
     */
    'findCropCasePicAnalyse': function (areaId, shootTime) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '2f79ace14d1d5f56a34a8f424715a32a03ad5548', {areaId, shootTime})
            : execute(concreteModuleName, `/CropCaseService/findCropCasePicAnalyse`, 'json', 'POST', { areaId, shootTime });
    }, 
    /**
     * 根据作物种类获取对应生长期
     * @param {*} crop 作物类型
     * @returns Promise 
     */
    'findCropPeriod': function (crop) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6d500e1ef5521da04618c5b8e57dfe9fdca4dce6', crop)
            : execute(concreteModuleName, `/CropCaseService/findCropPeriod`, 'json', 'POST', { crop });
    }, 
    /**
     * 苗情修改说明
     * @param {*} id 记录id
     * @param {*} annotation 说明
     * @returns Promise 
     */
    'updateCropCaseAnnotation': function (id, annotation) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b402d5a49d4b89a5df58299c54dbd0099b7f5162', {id, annotation})
            : execute(concreteModuleName, `/CropCaseService/cropCaseAnnotation`, 'json', 'PUT', { id, annotation });
    }, 
    /**
     * 苗情首页显示图片
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findCropCasePic': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '99ae9e044311eac5d9be6ec128a13466788d4369', areaId)
            : execute(concreteModuleName, `/CropCaseService/findCropCasePic`, 'json', 'POST', { areaId });
    }
}

export default CropCaseService

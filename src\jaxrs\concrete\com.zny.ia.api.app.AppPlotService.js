/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppPlotService = {
    /**
     * 区域详情
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'detailArea': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '97cab133af4d07a0f5177a815af66e48754cefcd', areaId)
            : execute(concreteModuleName, `/App/PlotService/detailArea`, 'json', 'POST', { areaId });
    }, 
    /**
     * 地块区域列表
     * @returns Promise 
     */
    'listPlot': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8bd2914d359f9f5024b1d28ef2262b7f357d6665')
            : execute(concreteModuleName, `/App/PlotService/listPlot`, 'json', 'GET');
    }
}

export default AppPlotService

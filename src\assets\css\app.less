#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  // width: 1920px;
  // height: 1080px;
  font-family: Microsoft YaHei;
  font-weight: 400; 
  // *****  大屏  *****
  // 弹窗样式
  .dialogStyle{
    .v-modal {
      background: rgba(208,243,255,.3) !important;
    }
    .el-dialog{
      margin: 0px auto 0px;
      background: #003D42;
      box-shadow:inset 0px -1px 5px 0px rgba(0,0,0,0.35);
      .el-dialog__header{
        .el-dialog__title{
          font-size: 18px;
          font-weight: bold;
          color: #DFEEF3;
        }
      }
      .el-dialog__body{
        padding: 13px 30px 30px 30px;
        .line{
          height: 1px;
          background: radial-gradient(circle, #00F4FD, rgba(0,244,253,0));
        }
        .close{
          position: absolute;
          top: 20px;
          right: 30px;
          cursor: pointer;
        }
      }
    }
  }
  // el-select,el-input,.el-date-editor 输入框样式
  .formStyle{
    .el-input{
      width: 100%;
      height: 100%;
      .el-select__caret{
        line-height: 100%;
      }
    }
    .el-select{
      width: 100%;
      height: 100%;
      .el-tag.el-tag--info {
        background-color: #008F9E;
        border-color: transparent;
        color: #DFEEF3;
      }
      .el-tag.el-tag--info .el-tag__close {
        color: #00f4fd;
      }
    }
    .el-select .el-tag__close.el-icon-close {
      background-color: transparent !important;
    }

    .el-input__inner{
      width: 100%;
      height: 100%;
      background: rgba(1,18,21,0);
      box-shadow: inset 0px 0px 10px 0px rgba(0,244,253,0.5);
      border-radius: 2px;
      border: 0;
      outline: 0;
      color: #FEFFFF;
    }
    input::-webkit-input-placeholder{
      font-size: 14px;
      color: rgba(254,255,255,.6);
    }
    //输入框拓展区域
    .el-input-group__append, .el-input-group__prepend{
      background-color: transparent;
      border: 0;
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
    }
    // 时间选择器
    .el-range-input{
      background: transparent;
    }
    .el-range-separator{
      color: rgba(254,255,255,.6);
    }
    .el-date-editor .el-range-input{
      color: #FEFFFF;
    }
    //文本域
    .el-textarea{
      width: 100%;
      height: 100%;
      .el-textarea__inner{
        width: 100%;
        height: 100%;
        background: rgba(1,18,21,0);
        color: #FEFFFF;
        box-shadow: inset 0px 0px 10px 0px rgba(0,244,253,0.5);
        border-radius: 2px;
        border: 0;
      }
    }
    //上传
    .el-upload--picture-card{
      background: rgba(1,18,21,0);
      box-shadow: inset 0px 0px 10px 0px rgba(0,244,253,0.5);
      border-radius: 2px;
      border: 0;
    }
    //上传按钮隐藏
    .hide{
      .el-upload--picture-card{
        display: none!important;
      }
    }
    //宽高128px
    .uploadCard128{
      .el-upload--picture-card{
        width: 128px;
        height: 128px;
        line-height: 13;
      }
      .el-upload-list--picture-card{
        .el-upload-list__item{
          width: 128px;
          height: 128px;
        }
      }
      .el-upload-list--picture-card .el-upload-list__item-actions{
        width: 128px;
        height: 128px;
      }
    }
    //宽高180px
    .uploadCard180{
      .el-upload--picture-card{
        width: 180px;
        height: 180px;
        line-height: 16;
      }
      .el-upload-list--picture-card{
        .el-upload-list__item{
          width: 180px;
          height: 180px;
        }
      }
      .el-upload-list--picture-card .el-upload-list__item-actions{
        width: 180px;
        height: 180px;
      }
    }
  }
  //上传按钮隐藏
  .hide{
    .el-upload--picture-card{
      display: none!important;
    }
  }
  // 搜索、取消按钮样式
  .searchButtonStyle{
    .el-button{
      background: rgba(1,18,21,0);
      box-shadow:inset 0px 0px 10px 0px rgba(0,244,253,0.5);
      border-radius: 2px;
      border: 0;
      outline: 0;
      color: #DFEEF3;
    }
  }
  // 搜索按钮样式(固定宽高)
  .searchButtonStyle2{
    .el-button{
      width: 100%;
      height: 100%;
      padding: 0;
      background: rgba(1,18,21,0);
      box-shadow:inset 0px 0px 10px 0px rgba(0,244,253,0.5);
      border-radius: 2px;
      border: 0;
      outline: 0;
      color: #DFEEF3;
    }
  }
  // 确定、提交按钮样式
  .submitButtonStyle{
    .el-button{
      background: rgba(1,18,21,0);
      box-shadow:inset 0px 0px 10px 0px rgba(243,161,52,0.8);
      border-radius: 2px;
      border: 0;
      outline: 0;
      color: #DFEEF3;
    }
  }
  // 搜索按钮样式(固定宽高)
  .submitButtonStyle2{
    .el-button{
      width: 100%;
      height: 100%;
      padding: 0;
      box-shadow:inset 0px 0px 10px 0px rgba(243,161,52,0.8);
      border-radius: 2px;
      border: 0;
      outline: 0;
      line-height: 100%;
      color: #DFEEF3;
      background: transparent!important;
    }
  }
  // el-table样式
  .tableStyle{
    .el-table,
    .el-table tr,
    .el-table th.el-table__cell{
      background: transparent;
      color: #DFEEF3;
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
    .el-table th.el-table__cell>.cell{
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #DFEEF3;
      line-height: 20px;
    }
    .el-table tr.el-table__cell>.cell{
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #DFEEF3;
      line-height: 20px;
    }
    .el-table__body tr:hover>td{
      background: rgba(0,200,202,0.2) !important;
    }
  }
  // *****  大屏end  *****

  // *****  管理系统样式  *****
  // 单选框样式
  .systemRadio{
    .el-radio__inner {
      border: 1px solid #12ACA7;
    }
    .el-radio__input.is-checked .el-radio__inner {
      border-color: #12ACA7;
      background: #12ACA7;
    }
    .el-radio__input.is-checked+.el-radio__label {
      color: #999999;
    }
  }
  .systemCheckBox{
    .el-checkbox__inner{
      border: 1px solid #12ACA7;
    }
    .el-checkbox__inner:hover {
      border-color: #12ACA7;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #12ACA7;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #999999;
    }
  }
  // 弹窗样式
  .systemDialogStyle{
    .v-modal {
      opacity: 0.4;
    }
    .el-dialog{
      border-radius: 8px;
      margin: 0px auto 0px;
      .el-dialog__header{
        .el-dialog__title{
          font-size: 18px;
          font-weight: bold;
          color: #333333;
        }
      }
      .el-dialog__body{
        padding: 20px 24px;
        .close{
          position: absolute;
          top: 16PX;
          right: 16px;
          cursor: pointer;
        }
        .el-form-item__label{
          line-height: 15px;
        }
      }
    }
  }
  .systemFormStyle{
    .el-input{
      width: 100%;
      height: 100%;
      .el-select__caret{
        line-height: 100%;
      }
    }
    //文本域
    .el-textarea{
      width: 100%;
      height: 100%;
      .el-textarea__inner{
        width: 100%;
        height: 100%;
        background: #F7F8FA;
        border-radius: 4px;
      }
    }
    //上传
    .el-upload{
      width: 100%;
      height: 100%;
    }
    .el-upload-dragger{
      width: 100%;
      height: 100%;
      background: #F7F8FA;
      border: 1px solid #D2D4D9;
    }
    //上传
    .el-upload--picture-card{
      border-radius: 2px;
    }
    //上传按钮隐藏
    .hide{
      .el-upload--picture-card{
        display: none!important;
      }
    }
    //宽高128px
    .uploadCard125{
      .el-upload--picture-card{
        width: 125px;
        height: 97px;
        border: 1px dashed #B4B4B4;
        line-height: 10;
      }
      .el-upload-list--picture-card{
        .el-upload-list__item{
          width: 125px;
          height: 97px;
          margin: 0 5px 8px 0;
        }
      }
      .el-upload-list--picture-card .el-upload-list__item-actions{
        width: 125px;
        height: 97px;
      }
    }
    .el-select{
      width: 100%;
      height: 100%;
    }
    .el-input__inner{
      width: 100%;
      height: 100%;
      background: #F7F8FA;
      border: 1px solid #D2D4D9;
      border-radius: 4px;
      outline: 0;
      color: #666666;
    }
    input::-webkit-input-placeholder{
      font-size: 14px;
      color: #999999;
    }
    // 时间选择器
    .el-range-input{
      background: transparent;
    }
    .el-range-separator{
      //color: rgba(254,255,255,.6);
    }
    .el-date-editor .el-range-input{
      color: #666666;
    }
  }
  .systemFormStyle.is-disabled .el-input__inner{
    background: #DFDFDF !important;
  }
  // 搜索、取消按钮样式
  .systemSearchButtonStyle{
    .el-button{
      background: #0093BC;
      border: 0;
      outline: 0;
      border-radius: 4px;
      color: #FFFFFF;
    }
  }
  // 搜索按钮样式(固定宽高)
  .systemSearchButtonStyle2{
    .el-button{
      width: 100%;
      height: 100%;
      padding: 0;
      background: #0093BC;
      border: 0;
      outline: 0;
      border-radius: 4px;
      color: #FFFFFF;
    }
  }
  // 取消按钮样式
  .systemResetButtonStyle{
    .el-button{
      background: #CCCCCC;
      border: 0;
      outline: 0;
      border-radius: 4px;
      color: #FFFFFF;
    }
  }
  // 取消按钮样式(固定宽高)
  .systemResetButtonStyle2{
    .el-button{
      width: 100%;
      height: 100%;
      padding: 0;
      background: #CCCCCC;
      border: 0;
      outline: 0;
      border-radius: 4px;
      color: #FFFFFF;
    }
  }
  // el-table样式
  .systemTableStyle{
    .el-table,
    .el-table tr{
      background: transparent;
      color: #333333;
    }
    .el-table th.el-table__cell{
      background: #F2F4F7;
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
    .el-table th.el-table__cell>.cell{
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #666666;
      line-height: 20px;
    }
    .el-table tr.el-table__cell>.cell{
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 20px;
    }
    .el-tooltip{
      cursor: pointer;
    }
  }
  // 无边框 无标题背景颜色
  .systemTableStyle1{
    .el-table,
    .el-table tr{
      background: transparent;
      color: #333333;
    }
    .el-table th.el-table__cell{
      background:transparent;
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: transparent;
    }
    .el-table th.el-table__cell>.cell{
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #666666;
      line-height: 20px;
    }
    .el-table tr.el-table__cell>.cell{
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 20px;
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: 1px solid transparent;
    }
    .el-table--border::after, .el-table--group::after, .el-table::before {
      content: '';
      position: absolute;
      background-color: transparent;
      z-index: 1;
    }
    .el-tooltip{
      cursor: pointer;
    }
  }
  // *****  管理系统end  *****
}
// *****  大屏  *****
// tooltip文字提示
.tooltipStyle.el-tooltip__popper.is-dark {
  text-align: center;
  // background: rgba(0,0,0,.5) !important;
  background: #002729 !important;
  color: #DFEEF3 !important;
  .item{
    margin: 1px 0;
  }
}
.tooltipWidthStyle.el-tooltip__popper.is-dark {
  width: 350px;
  // background: rgba(0,0,0,.5) !important;
  background: #002729 !important;
  color: #DFEEF3 !important;
  .item{
    margin: 0;
  }
}

// 时间选择弹窗样式
.datePickerStyle{
  background: #00464C !important;
  box-shadow: inset 0px 0px 10px 0px rgba(0,195,220,0.6) !important;
  border-radius: 2px !important;
  border: 0 !important;
  outline: 0 !important;
  color: #FEFFFF !important;
  .el-date-table th{
    color: #FEFFFF !important;
    border-bottom: solid 1px rgba(254,255,255,0.6) !important;
  }
  .el-date-range-picker__content.is-left {
    border-right: 1px solid rgba(254,255,255,0.6) !important;
  }
  .el-picker-panel__icon-btn{
    color: rgba(254,255,255,0.6) !important;
  }
  .el-date-table td.today span,
  .el-date-table td.available:hover {
    color: #00FFB4;
  }
  .el-date-table td.end-date span, .el-date-table td.start-date span {
    background-color: #00BA83 !important;
  }
  // 选中时间范围背景色
  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: rgba(69,253,255,.4) !important;
  }
  .el-date-picker__header-label{
    color: #FEFFFF !important;
  }
  .el-date-picker__editor-wrap .el-input__inner{
    background: #006B76 ;
    border: 0;
    outline: 0;
    color:#FEFFFF ;
  }
  .el-date-table td.current:not(.disabled) span {
    color: #FFF;
    background-color: #00FFB4;
  }
  .el-date-picker__time-header{
    border-bottom: 1px solid rgba(254,255,255,.6);
  }
  .el-picker-panel__footer{
    border:0;
    background-color: #006B76;
  }
  .el-button--text{
    color: #72C8FF !important;
  }
  .el-button{
    background: transparent;
    color: #FEFFFF;
    border: 0;
    outline: 0;
  }
  .el-button.is-plain:hover {
    background: transparent;
    color: #FEFFFF;
  }
  .el-time-panel{
    background:#006B76;
    box-shadow: inset 0px 3px 10px 0px rgba(0,0,0,0.3);
    border: 0;
    outline: 0;
  }
  .el-time-spinner__item{
    color: rgba(254,255,255,.5);
  }
  .el-time-spinner__item:hover:not(.disabled):not(.active) {
    background: transparent;
  }
  .el-time-spinner__item.active:not(.disabled){
    color: #FEFFFF;
  }
  .el-time-panel__content::after, .el-time-panel__content::before{
    border-top: 1px solid rgba(254,255,255,.4);
    border-bottom: 1px solid rgba(254,255,255,.4);
  }
  .el-time-panel__footer {
    border:0;
  }
  .el-time-panel__btn.cancel{
    color: rgba(254,255,255,.6);
  }
  .el-time-panel__btn.confirm{
    color: #72C8FF;
  }
}
.datePickerStyle.el-popper[x-placement^=top] {
  margin-bottom: 5px !important;
}
.datePickerStyle.el-popper[x-placement^=bottom] {
  margin-top: 5px !important;
}
.datePickerStyle.el-popper[x-placement^=top] .popper__arrow,
.datePickerStyle.el-popper[x-placement^=top] .popper__arrow::after{
  border-top-color: transparent !important;
}
.datePickerStyle.el-popper[x-placement^=bottom] .popper__arrow,
.datePickerStyle.el-popper[x-placement^=bottom] .popper__arrow::after{
  border-bottom-color: transparent !important;
}

// select样式
.selectStyle_list{
  background: #064249 !important;
  box-shadow: inset 0px 0px 10px 0px rgba(0,195,220,.6) !important;
  border-radius: 2px !important;
  border: 0 !important;
  outline: 0 !important;
  .el-select-dropdown__item{
    color: rgba(254,255,255,.4) !important;
  }
  .el-select-dropdown__item.hover, .el-select-dropdown__item:hover,
  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
    background-color:rgba(0,255,252,.12);
  }
  .el-select-dropdown__item.selected {
    color: #FEFFFF !important;
  }
  
}
.selectStyle_list.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  background-color: transparent !important;
}
.selectStyle_list.el-popper[x-placement^=bottom] {
  margin-top: 5px !important;
}
.selectStyle_list.el-popper[x-placement^=bottom] .popper__arrow,
.selectStyle_list.el-popper[x-placement^=bottom] .popper__arrow::after{
  border-bottom-color: transparent !important;
}
//页码样式
.pageChange{
  .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
    background: rgba(0,245,255,0);
    box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
    border-radius: 2px;
    color: rgba(223,238,243,0.7);
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    background-color: rgba(0,245,255,0);
  }
  .el-pagination.is-background .btn-next.disabled, .el-pagination.is-background .btn-next:disabled, .el-pagination.is-background .btn-prev.disabled, .el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .el-pager li.disabled{
    color: rgba(223,238,243,0.2);
  }
  .el-pagination__jump{
    color: rgba(223,238,243,1);
    .el-input__inner{
      background: rgba(0,245,255,0);
      box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
      border-radius: 2px;
      color: rgba(223,238,243);
      border: 0;
    }
  }
}
// *****  大屏end  *****
// *****  管理系统样式  *****
// tooltip文字提示
.systemTooltipStyle.el-tooltip__popper.is-light {
  color: #333333 !important;
  text-align: center;
  background: #FFFFFF !important;
  box-shadow: 0px 3px 6px rgba(0,0,0,0.16) !important;
  border-color: #FFFFFF !important;
  border: 0 !important;
  .item{
    margin: 1px 0;
  }
  
}
.systemTooltipStyle.el-tooltip__popper .popper__arrow, .el-tooltip__popper .popper__arrow::after{
  border: 0 !important;
}
.systemTooltipWidthStyle.el-tooltip__popper.is-light {
  width: 350px;
  color: #333333 !important;
  background: #FFFFFF !important;
  box-shadow: 0px 3px 6px rgba(0,0,0,0.16) !important;
  border-color: #FFFFFF !important;
  .item{
    margin: 0;
  }
}
.systemTooltipWidthStyle.el-tooltip__popper .popper__arrow, .el-tooltip__popper .popper__arrow::after{
  border: 0 !important;
}
//页码样式
.systemPageChange{
  .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
    background: #ffffff;
    // box-shadow: inset 0px 0px 9px 1px rgba(0,245,255,0.5);
    border: 1px solid #D2D4D9;
    border-radius: 2px;
    color: #323233;

  }
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    background-color: #0093BC;
  }
  .el-pagination.is-background .btn-next.disabled, .el-pagination.is-background .btn-next:disabled, .el-pagination.is-background .btn-prev.disabled, .el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .el-pager li.disabled{
    color: #EBEDF0;
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #0093BC;
  }
  .el-pagination.is-background .el-pager li.active:hover {
    color: #EBEDF0;
  }
}

.tooltipSystemStyle.el-tooltip__popper.is-light {
  background: #fff !important;
  box-shadow: 0px 0px 40px rgba(102,102,102,0.35);
  border-radius: 8px;
  border: 0;

  .item {
    margin: 1px 0;
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    line-height: 20px;
    color: #333333;
    text-align: center;
  }
  .item2{
    max-height: 100px;
    overflow-y: scroll;
    &_list{
      margin: 0 10px;
      font-size: 15px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      line-height: 20px;
      color: #666666;
    }
  }
}
.tooltipSystemStyle.el-tooltip__popper .popper__arrow, .el-tooltip__popper .popper__arrow::after{
  box-shadow: 0px 0px 40px rgba(102,102,102,0.35);
  border-radius: 8px;
  border: 0;
}
// *****  管理系统end  *****
// 智慧植保鼠标划入样式
.tooltipWidthStylesss.el-tooltip__popper{
  padding: 20px;
}
.tooltipWidthStylesss.el-tooltip__popper.is-dark {
    width: 300px;
    background: #00282B !important;
    color: #dfeef3 !important;
    .item {
        margin: 0;
    }
}
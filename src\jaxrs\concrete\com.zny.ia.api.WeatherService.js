/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const WeatherService = {
    /**
     * 区域光照度图表
     * @param {*} po 
     * @returns Promise 
     */
    'illuminanceChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e9d8f5046e000e7e25a9c980739d8560be6e9be5', po)
            : execute(concreteModuleName, `/WeatherService/illuminanceChart`, 'json', 'POST', po);
    }, 
    /**
     * 气象数据汇报
     * @param {*} po 
     * @returns Promise 
     */
    'weatherReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e213fee1781f0284667f9d7fe11faad39e592b27', po)
            : execute(concreteModuleName, `/WeatherService/weatherReport`, 'json', 'POST', po);
    }, 
    /**
     * 区域温湿度图表,本日调用
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'temperatureChartDay': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '9aab697c4c0ef84130d0ff699fe2822101e5ac04', areaId)
            : execute(concreteModuleName, `/WeatherService/temperatureChartDay`, 'json', 'POST', { areaId });
    }, 
    /**
     * 气象基础数据
     * @returns Promise 
     */
    'weatherBasicData': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b518e41c847b5d06e49e0d1bce83caf49c7ce730')
            : execute(concreteModuleName, `/WeatherService/weatherBasicData`, 'json', 'GET');
    }, 
    /**
     * 区域温湿度图表,近七天,近三十天调用
     * @param {*} po 
     * @returns Promise 
     */
    'temperatureChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'c29cb0b24a357ac667e9b235db3beb7453d38150', po)
            : execute(concreteModuleName, `/WeatherService/temperatureChart`, 'json', 'POST', po);
    }, 
    /**
     * 区域气象数据
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'weatherAreaData': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '8bbe94d4219f01e43b6467d3ddc794eb58074fd0', areaId)
            : execute(concreteModuleName, `/WeatherService/weatherAreaData`, 'json', 'POST', { areaId });
    }, 
    /**
     * 气象报警汇总
     * @returns Promise 
     */
    'weatherAlarmCollect': function () {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '06beb82794a3f6038ae927ddf1f3045ede90aac8')
            : execute(concreteModuleName, `/WeatherService/weatherAlarmCollect`, 'json', 'GET');
    }, 
    /**
     * 区域风向图表
     * @param {*} po 
     * @returns Promise 
     */
    'windChart': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '69e5ef21cc322e3cf413112a39b323cca1639006', po)
            : execute(concreteModuleName, `/WeatherService/windChart`, 'json', 'POST', po);
    }, 
    /**
     * 气象报警记录
     * @param {*} po 
     * @returns Promise 
     */
    'weatherAlarmRecord': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'fbfa0cc98deba347c93331b914b8f74ea878fb27', po)
            : execute(concreteModuleName, `/WeatherService/weatherAlarmRecord`, 'json', 'POST', po);
    }, 
    /**
     * 气象数据历史记录
     * @param {*} po 
     * @returns Promise 
     */
    'weatherHistory': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'e55915d2f83e0a57677a3a85e0252c1a0cfe37ed', po)
            : execute(concreteModuleName, `/WeatherService/weatherHistory`, 'json', 'POST', po);
    }
}

export default WeatherService

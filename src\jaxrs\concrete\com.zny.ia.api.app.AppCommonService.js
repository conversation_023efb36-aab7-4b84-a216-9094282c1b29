/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppCommonService = {
    /**
     * 设备信息查看
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'eqSelect': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'f9e0acd707f264378a7af45a1930cb2adc431c47', equipmentId)
            : execute(concreteModuleName, `/App/CommonService/eqSelect`, 'json', 'POST', { equipmentId });
    }
}

export default AppCommonService

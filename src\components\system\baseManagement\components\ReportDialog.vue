<template>
  <!-- 汇报弹窗 -->
  <el-dialog
    class="reportDialog"
    title="大屏展示数据汇报"
    :visible.sync="dialogVisible"
    width="23%"
    center
    :modal-append-to-body="false"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div class="close" @click="closeDialog('reportRuleForm')">
      <img src="../../../../assets/image/managementSystem/close.png" alt="">
    </div>
    <div class="formList">
      <el-form :model="reportRuleForm" :rules="reportRules" ref="reportRuleForm" label-width="100px" label-position="top">
        <el-form-item label="智慧农业覆盖率" prop="coverage">
          <el-input v-model="reportRuleForm.coverage" clearable class="systemFormStyle" placeholder="请输入智慧农业覆盖率">
            <i
              class=" el-input__icon"
              slot="suffix">
              %
            </i>
          </el-input>
        </el-form-item>
        <el-form-item label="作物年产值" prop="annualValue">
          <el-input v-model="reportRuleForm.annualValue" clearable class="systemFormStyle" placeholder="请输入作物年产值">
            <i
              class=" el-input__icon"
              slot="suffix">
              元
            </i>
          </el-input>
        </el-form-item>
        <el-form-item label="作物总产量" prop="yield">
          <el-input v-model="reportRuleForm.yield" clearable class="systemFormStyle" placeholder="请输入作物总产量">
            <i
              class=" el-input__icon"
              slot="suffix">
              公斤kg
            </i>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="btnBox">
      <div class="btnItem systemResetButtonStyle2">
        <el-button @click="cancelDialog('reportRuleForm')">取消</el-button>
      </div>
      <div class="btnItem systemSearchButtonStyle2">
        <el-button @click="submitDialog('reportRuleForm')">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import BaseManagementService from '../../../../jaxrs/concrete/com.zny.ia.api.BaseManagementService.js';

export default {
  name: 'ReportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reportRuleForm: {
        coverage: "",
        annualValue: "",
        yield: "",
      },
      reportRules: {
        coverage: [
          { required: true, message: '请输入智慧农业覆盖率', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        annualValue: [
          { required: true, message: '请输入作物年产值', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
        yield: [
          { required: true, message: '请输入作物总产量', trigger: 'blur' },
          {
            pattern: /(^[1-9]([0-9]+)?$)/,
            message: '必须为数字值且为整数',
          },
        ],
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  methods: {
    // 数据汇报关闭
    closeDialog(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    // 数据汇报取消
    cancelDialog(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    // 数据汇报确定
    submitDialog(formName) {
      const that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let po = {
            coverage: this.reportRuleForm.coverage,
            annualValue: this.reportRuleForm.annualValue,
            yield: this.reportRuleForm.yield,
          }
          BaseManagementService.areaReport(po)
          .then(() => {
            that.$message({
              message: '数据汇报成功',
              type: 'success'
            });
            that.$refs[formName].resetFields();
            that.dialogVisible = false;
            that.$emit('report-success');
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 组件特定样式
</style>

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppSoilService = {
    /**
     * 根据区域id获取实时土壤数据
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findNewInfoByAreaId': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '1620998206e6f0061f5988c172a453f1dac53e07', areaId)
            : execute(concreteModuleName, `/App/SoilService/findNewInfoByAreaId`, 'json', 'POST', { areaId });
    }, 
    /**
     * 根据设备id获取实时土壤数据
     * @param {*} equipmentId 设备id
     * @returns Promise 
     */
    'findNewInfoByEquipmentId': function (equipmentId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'b7eba2e8d4a9df0f4d20888e0f7b6d7331e06d65', equipmentId)
            : execute(concreteModuleName, `/App/SoilService/findNewInfoByEquipmentId`, 'json', 'POST', { equipmentId });
    }, 
    /**
     * 报警信息查看
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'selectAlarmInfo': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '6bf328d6a1f19d691921dc25196ebe523de531ad', areaId)
            : execute(concreteModuleName, `/App/SoilService/selectAlarmInfo`, 'json', 'POST', { areaId });
    }
}

export default AppSoilService

/* eslint-disable */
import {execute, grable} from '../concrete'

const concreteModuleName = 'concrete'

const AppWaterService = {
    /**
     * 上传水质检测报告（图片）
     * @param {*} po 
     * @returns Promise 
     */
    'addWaterPicReport': function (po) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '0bf7a2f1657c2b70f3a36daa7e82459821a6499c', po)
            : execute(concreteModuleName, `/App/WaterService/addWaterPicReport`, 'json', 'POST', po);
    }, 
    /**
     * 立即检测
     * @param {*} id 设备id  返回 结果key
     * @returns Promise 
     */
    'detection': function (id) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a9d65eb805c89725609ae5f37dc94651d25863ad', id)
            : execute(concreteModuleName, `/App/WaterService/detection`, 'text', 'POST', { id });
    }, 
    /**
     * 获取检测标准和地点
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'findWaterStandard': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'a12383b53bc3103e1fa9de4da229bc8ca1c6bd44', areaId)
            : execute(concreteModuleName, `/App/WaterService/findWaterStandard`, 'json', 'POST', { areaId });
    }, 
    /**
     * 水质检测历史记录
     * @param {*} page 第几页
     * @param {*} size 每页有几条
     * @returns Promise 
     */
    'listWaterAlarm': function (page, size) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4a00cacf3752e50b8fb9fdd32202e41612d66e69', {page, size})
            : execute(concreteModuleName, `/App/WaterService/listWaterAlarm`, 'json', 'POST', { page, size });
    }, 
    /**
     * 获取区域下的水质检测设备
     * @param {*} areaId 区域id
     * @returns Promise 
     */
    'listWaterEqByArea': function (areaId) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, '4d056411d13472057a5f2638872ceeda8902b378', areaId)
            : execute(concreteModuleName, `/App/WaterService/listWaterEqByArea`, 'json', 'POST', { areaId });
    }, 
    /**
     * 根据水质检测结果key获取检测结果 false-检测中 true-检测完成
     * @param {*} key 结果key
     * @returns Promise 
     */
    'checkResult': function (key) {
        return  grable(concreteModuleName)
            ? execute(concreteModuleName, 'd9fe8c1e5f1cfaa85a47ef4ddf2017c1f5e2bf3d', key)
            : execute(concreteModuleName, `/App/WaterService/checkResult`, 'json', 'POST', { key });
    }
}

export default AppWaterService

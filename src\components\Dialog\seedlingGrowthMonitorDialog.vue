<template>
    <div class="seedlingGrowthMonitorDialog dialogStyle" :class="{ seedlingGrowthMonitorDialog2: showAllMonitor }">
        <el-dialog
            :title="title"
            width="1256px"
            v-if="dialogVisible"
            :visible.sync="dialogVisible"
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="line"></div>
            <div class="close" @click="closeHandle">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="jumpPhoto" @click="seedlingGrowthHistoryPhotoDialogOpen()">
                历史图片>>
            </div>
            <div class="handleBox">
                <div class="handleBox_select formStyle">
                    <el-select
                        v-model="equipment"
                        @change="equipmentChange"
                        popper-class="selectStyle_list"
                        placeholder="请选择监控设备"
                    >
                        <el-option
                            v-for="item in equipmentData"
                            :key="item.equipmentId"
                            :label="item.equipmentName"
                            :value="item.equipmentId"
                        ></el-option>
                    </el-select>
                </div>
                <!--        <div class="handleBox_item searchButtonStyle2" @click="searchList()">-->
                <!--          <el-button>查看</el-button>-->
                <!--        </div>-->
                <div class="handleBox_item searchButtonStyle2" @click="takePhoto()" v-if="areaIsJudge">
                    <el-button>立即拍摄</el-button>
                </div>
                <!--        <div class="handleBox_item searchButtonStyle2" @click="seedlingGrowthUploadPhotoDialogOpen()">-->
                <!--          <el-button>上传图片</el-button>-->
                <!--        </div>-->
                <div class="handleBox_item handleBox_item2 searchButtonStyle2" @click="monitorHandle()">
                    <el-button>{{ showAllMonitor ? '收起全部监控' : '查看全部监控' }}</el-button>
                </div>
            </div>
            <div class="containerBox" v-if="!showAllMonitor && showVideo">
                <div class="containerBox_left">
                    <div
                        id="video-container1"
                        style="width:100%;height:100%"
                        v-if="dialogVisible && !showAllMonitor"
                    ></div>
                    <!-- <video
                        id="growthVideo"
                        class="video-js vjs-big-play-centered"
                        v-if="dialogVisible && !showAllMonitor"
                    >
                        <source :src="data.videoUrl" type="video/mp4" />
                    </video> -->
                </div>
                <div class="containerBox_right">
                    <div class="containerBox_right_info">
                        <div class="containerBox_right_info_image">
                            <img :src="selectedPicture.pictureUrl" alt="" />
                        </div>
                        <div class="containerBox_right_info_con">
                            <div class="info_header">
                                <div class="info_header_name">{{ detailData.cropName | cropFormat }}</div>
                                <div class="info_header_button searchButtonStyle2" @click="infoEdit()" v-if="areaIsJudge">
                                    <el-button>{{ infoIsEditor ? '提交' : '说明' }}</el-button>
                                </div>
                            </div>
                            <div class="info_text">
                                <div class="info_text_list">作物：{{ detailData.cropName | cropFormat }}</div>
                                <div class="info_text_list">生长期：{{ detailData.growingPeriod | growthFormat }}</div>
                                <div class="info_text_list">苗株高度：{{ detailData.cropHeight }}</div>
                                <div class="info_text_list">杂草覆盖率：{{ detailData.weed }}%</div>
                                <div class="info_text_list">
                                    叶色状况：
                                    <span v-for="(item, index) in detailData.leafColor" :key="index">
                                        {{ item | leafColorFormat }}，
                                    </span>
                                </div>
                                <div class="info_text_list">
                                    苗株长相：
                                    <span v-for="(item, index) in detailData.cropLooks" :key="index">
                                        {{ item | plantLookFormat }}，
                                    </span>
                                </div>
                                <div class="info_text_list" v-if="!infoIsEditor">说明：{{ detailData.annotation }}</div>
                                <div class="info_edit" v-else>
                                    <div>说明：</div>
                                    <el-input type="textarea" v-model="detailData.annotation"></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="containerBox_right_imageList">
                        <div
                            class="containerBox_right_imageList_item"
                            v-for="(item, index) in data.pictureDataVoList"
                            :key="index"
                            @click="selectPicture(item, index)"
                        >
                            <img :src="item.pictureUrl" alt="" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="monitorBox" v-if="showAllMonitor && showVideoList">
                <div class="monitorBox_list" v-for="(item, index) in videoData" :key="index">
                    <div
                        :id="'myVideo_' + index"
                        style="width:100%;height:100%"
                        v-if="dialogVisible && showAllMonitor"
                    ></div>
                    <!-- <video
                        :id="'myVideo_' + index"
                        class="video-js vjs-big-play-centered"
                        v-if="dialogVisible && showAllMonitor"
                    >
                        <source :src="item.videoUrl" type="video/mp4" />
                    </video> -->
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import EZUIKit from 'ezuikit-js'
import CropCaseService from '../../jaxrs/concrete/com.zny.ia.api.CropCaseService'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService'
// import cropData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类'
// import leafData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$叶色状况'
// import plantData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$苗株长相'
// import growthData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期'
import { debounce } from '../../js/debounce'
export default {
    name: 'seedlingGrowthMonitorDialog',
    data() {
        return {
            title: '实时监控',
            areaId: '',
            areaIsJudge:true,//判断当前用户能否操作该区域
            dialogVisible: false,
            equipment: '',
            equipmentData: [],
            cropInfo: '种植冬小麦，生长周期第4周，处',
            infoIsEditor: false,
            showAllMonitor: false,
            data: [],
            selectedPicture: '',
            selectedTime: '',
            detailData: [],
            videoData: [],
            videoOptions: {
                autoplay: false, //自动播放
                controls: true, //是否显示底部控制栏
                loop: true, //是否循环播放
                muted: true, //设置默认播放音频
                //是否自适应布局,播放器将会有流体体积。换句话说，它将缩放以适应容器。
                // 如果<video>标签有“vjs-fluid”样式时，这个选项会自动设置为true。
                fluid: false,
            },
            player: null,
            playlist: [],
            showVideo: true,
            showVideoList: true,
            takePhotoIsSuccess:false,//立即拍摄是否成功
        }
    },
    mounted() {
        this.listen('seedlingGrowthMonitorDialogOpen', areaId => {
            this.dialogVisible = true
            this.areaId = areaId
            this.showAllMonitor = false
            // this.getMonitorData()
            this.getEquipmentData()
            this.areaJudge()
        })
    },
    methods: {
        // 判断当前用户能否操作该区域
        areaJudge(){
            CommonService.areaJudge(this.areaId)
            .then(res=>{
                // console.log(res);
                this.areaIsJudge=res
            })
        },
        //打开历史图片弹窗
        seedlingGrowthHistoryPhotoDialogOpen() {
            this.zEmit('seedlingGrowthHistoryPhotoDialogOpen', this.areaId)
        },
        equipmentChange() {
            var that = this
            that.$nextTick(() => {
                setTimeout(function() {
                    if (!that.showAllMonitor) {
                        if (that.player) {
                            that.player.dispose()
                            that.player = null
                        }
                        that.showVideo = false
                        that.getMonitorData()
                    } else {
                        if (that.playlist.length > 0) {
                            for (let i = 0; i < that.playlist.length; i++) {
                                that.playlist[i].dispose() //（第四步）dispose()是官方的销毁函数
                            }
                        }
                        that.showVideoList = false
                        that.playlist = []
                        that.getAllMonitorData()
                    }
                    that.videoData = []
                    that.data = []
                }, 500)
            })
        },
        //搜索
        searchList() {
            this.getMonitorData()
        },
        //获取设备数据
        getEquipmentData() {
            CommonService.equipmentListByAreaId(this.areaId).then(res => {
                this.equipmentData = res
                this.equipment = res[0].equipmentId
                this.getMonitorData()
            })
        },
        //单个视频播放
        videoPlayer() {
            var that = this
            setTimeout(() => {
                that.player = that.$video('growthVideo', that.videoOptions, function() {})
            }, 500)
        },
        allVideoPlayer() {
            var that = this
            setTimeout(() => {
                let lang = this.videoData.length //（第一步）这是多个视频地址数组的长度
                for (let i = 0; i < lang; i++) {
                    var player = that.$video('myVideo_' + i, that.videoOptions, function() {
                        // this.play()   //取消自动播放
                    })
                    that.playlist.push(player) //（第二步）palylist是定义的空数组，存放多个视频实例
                }
            }, 500)
        },
        //操作监控展开收起按钮
        monitorHandle() {
            var that = this
            that.$nextTick(() => {
                setTimeout(function() {
                    if (!that.showAllMonitor) {
                        if (that.player) {
                            that.player.dispose()
                            that.player = null
                        }
                        that.getAllMonitorData()
                    } else {
                        if (that.playlist.length > 0) {
                            for (let i = 0; i < that.playlist.length; i++) {
                                that.playlist[i].dispose() //（第四步）dispose()是官方的销毁函数
                            }
                        }
                        that.playlist = []
                        that.getMonitorData()
                    }
                    that.videoData = []
                    that.data = []
                    that.showAllMonitor ? (that.showAllMonitor = false) : (that.showAllMonitor = true)
                }, 500)
            })
        },
        //获取所有监控信息
        getAllMonitorData() {
            var that = this

            CropCaseService.findCropCaseMonitorList(this.areaId).then(res => {
                this.videoData = res
                this.showVideoList = true
                res.forEach((item, index) => {
                    that.$nextTick(function() {
                        const player1 = new EZUIKit.EZUIKitPlayer({
                            // id: "'myVideo_' + index", // 视频容器ID
                            id: `myVideo_${index}`,
                            autoplay: true,
                            accessToken: item.token,
                            url: item.videoUrl,
                            template: 'theme',
                            header: ['capturePicture'],
                            footer: ['fullScreen'],
                            handleSuccess: function(res) {
                                console.log('播放成功', res)
                            },
                            handleError: function(err) {
                                console.log('播放失败', err)
                                document.getElementById('video-container1').innerHTML = ''
                            },
                            // fullScreenCallBack: function(res) {
                            //     // this.player1.fullScreen()
                            // },
                        })
                    })
                })
                that.$nextTick(() => {
                    that.allVideoPlayer()
                })
            })
        },
        //获取实时监控信息
        getMonitorData() {
            var that = this
            CropCaseService.cropCaseMonitoring(this.equipment).then(res => {
                if (res.pictureDataVoList && res.pictureDataVoList.length > 0) {
                    this.selectedPicture = res.pictureDataVoList[0]
                    res.pictureDataVoList.splice(0, 1)
                    this.getInfo(this.selectedPicture)
                }
                that.$nextTick(function() {
                    const player1 = new EZUIKit.EZUIKitPlayer({
                        id: 'video-container1', // 视频容器ID
                        autoplay: false,
                        accessToken: res.token,
                        url: res.videoUrl,
                        template: 'theme',
                        // header: ['capturePicture'],
                        // footer: ['fullScreen'],
                        handleSuccess: function(res) {
                            console.log('播放成功', res)
                        },
                        handleError: function(err) {
                            console.log('播放失败', err)
                            document.getElementById('video-container1').innerHTML = ''
                        },
                        // fullScreenCallBack: function(res) {
                        //     // this.player1.fullScreen()
                        // },
                    })
                })
                this.data = res
                this.showVideo = true

                that.$nextTick(() => {
                    that.videoPlayer()
                })
            })
        },
        //选择图片
        selectPicture(value, index) {
            [this.selectedPicture, this.data.pictureDataVoList[index]] = [
                this.data.pictureDataVoList[index],
                this.selectedPicture,
            ]
            this.getInfo(value)
        },
        //获取信息
        getInfo(value) {
            CropCaseService.findCropCasePicAnalyse(this.areaId, value.shootTime)
            .then(res => {
                this.detailData = res
            })
        },
        //立即拍摄
        takePhoto: debounce(function() {
            CropCaseService.realTimeShoot(this.equipment)
            .then(() => {
                this.takePhotoIsSuccess=true
                this.$message({
                    message: '拍摄成功！',
                    type: 'success',
                })
            })
        }),
        //说明提交
        infoEdit() {
            this.infoIsEditor ? (this.infoIsEditor = false) : (this.infoIsEditor = true)
            if (!this.infoIsEditor) {
                CropCaseService.updateCropCaseAnnotation(this.data.id, this.data.annotation)
                .then(() => {
                    this.$message({
                        message: '提交成功！',
                        type: 'success',
                    })
                })
            }
        },
        //页面关闭
        closeHandle() {
            var that = this
            setTimeout(function() {
                if (that.player) {
                    that.player.dispose()
                    that.player = null
                }
                if (that.playlist.length > 0) {
                    for (let i = 0; i < that.playlist.length; i++) {
                        that.playlist[i].dispose() //（第四步）dispose()是官方的销毁函数
                    }
                    that.playlist = []
                }
                that.data = []
                that.videoData = []
                that.dialogVisible = false
                if(that.takePhotoIsSuccess){
                    that.zEmit('takePhotoIsSuccess')
                }
            }, 500)
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/seedlingGrowthMonitorDialog.less';
</style>

.InsectSituationUploadPicturesDialog{
  .el-dialog{
    height: 635px;
    margin-top: 20vh !important;
    .el-dialog__body{
      height: 537px;
      .addFrom{
        height: 460px;
        margin-top: 27px;
        .el-form{
          .el-form-item:nth-child(1){
            .el-form-item__content{
              margin-left: 170px !important;
            }
          }
          .el-form-item:nth-child(2){
            .el-form-item__content{
              width: 180px;
              height: 32px;
            }
          }
          .el-form-item:nth-child(3){
            .el-form-item__content{
              position: relative;
              .tipsImg{
                position: absolute;
                top: 5px;
                left: -15px;
              }
              .formList{
                width: 555px;
                height: 200px;
                overflow: scroll;
                position: absolute;
                top: 40px;
                left: -120px;
                display: flex;
                flex-wrap: wrap; // 强制换行
                justify-content: flex-start; //所有元素右对齐
                align-content: flex-start;
                &_item{
                  height: 30px;
                  margin:0 15px 10px 0;
                  display: flex;
                  .handleBox_item{
                    height: 32px;
                    line-height: 32px;
                    margin-right: 12px;
                  }
                  .handleBox_item_select{
                    width: 100px;
                  }
                  .handleBox_item_input{
                    width: 60px;
                  }
                  .handleBox_item_text{
                    font-size: 14px;
                    font-weight: 400;
                    color: #FEFFFF;
                  }
                  .handleBox_item_img{
                    img{
                      vertical-align: middle;
                    }
                  }
                }
              }
            }
          }
          .el-form-item{
            .el-form-item__label{
              color: #DFEEF3;
            }
            
          }
        }
      }
      .btnBox{
        display: flex;
        justify-content: center;
        margin-top: 15px;
        .el-button{
          width: 120px;
          height: 42px;
          margin: 0 30px;
        }
      }
    }
  }
}
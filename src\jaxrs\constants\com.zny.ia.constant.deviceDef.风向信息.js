import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 北风
     * desc: --
     * value: 0
     */
    北风: 0,
    /**
     * label: 东北风
     * desc: --
     * value: 1
     */
    东北风: 1,
    /**
     * label: 东风
     * desc: --
     * value: 2
     */
    东风: 2,
    /**
     * label: 东南风
     * desc: --
     * value: 3
     */
    东南风: 3,
    /**
     * label: 南风
     * desc: --
     * value: 4
     */
    南风: 4,
    /**
     * label: 西南风
     * desc: --
     * value: 5
     */
    西南风: 5,
    /**
     * label: 西风
     * desc: --
     * value: 6
     */
    西风: 6,
    /**
     * label: 西北风
     * desc: --
     * value: 7
     */
    西北风: 7,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}
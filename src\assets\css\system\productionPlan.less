.productionPlan{
  width: 100%;
  height: 97%;
  &_tabList{
    margin: 22px 32px;
    display: flex;
    align-items: center;
    .tabList_item{
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-right: 40px;
      cursor: pointer;
    }
    .active{
      font-size: 22px;
      font-weight: bold;
      color: #0093BC;
    }
  }
  &_con{
    width: 96%;
    height: 91%;
    background: #FFFFFF;
    border-radius: 8px;
    margin: 20px 0 0 32px;
    .handleBox,.handleBox1{
      display: flex;
      align-items: center;
      &_item{
        height: 40px;
        margin-top: 24px;
        margin-right: 16px;
      }
      &_item:nth-child(1){
        width: 262px;
        margin-left: 24px;
      }
      &_item:nth-child(2){
        width: 380px;
      }
      &_item:nth-child(3),
      &_item:nth-child(4){
        width: 80px;
      }
      &_item:nth-child(5){
        width: 100px;
      }
      &_text{
        margin: 18px 20px 0 23px;
      }
      &_btn{
        width: 100px;
        height: 40px;
      }
    }
    .containerBox{
      width: 97%;
      height: 90%;
      margin: auto;
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      &_left{
        width: 55%;
        height: 100%;
        .tableHeader{
          padding: 0 6%;
          height: 48px;
          display: flex;
          justify-content: space-between;
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          line-height: 48px;
          color: #666666;
          background: #F7F8FA;
          border: 1px solid #DFDFDF;
          border-bottom: 0;
        }
        .tableBox{
          width: 100%;
          height: 82%;
          margin: auto;
          //margin-top: 24px;
          .tableActive_text{
            font-size: 15px;
            font-family: MicrosoftYaHei;
            line-height: 36px;
            color: #007EFF;
            cursor: pointer;
          }
          .el-table th.el-table__cell{
            background-color: #fff!important;
          }
          .cell{
            .viewData{
              font-size: 15px;
              font-weight: 400;
              color: #007EFF;
              cursor: pointer;
            }
          }
        }
        .pageBox{
          width: 97%;
          height: 5%;
          margin-top: 10px;
          text-align: center;
        }
      }
      &_right{
        width: 40%;
        height: 85%;
        padding: 2%;
        background: #F7F8FA;
        border-radius: 8px;
        .planInfo{
          width: 100%;
          height: 100%;
          &_header{
            margin-bottom: 10px;
            &_text1{
              font-size: 18px;
              font-family: Source Han Sans CN;
              font-weight: bold;
              line-height: 0px;
              color: #333333;
            }
            &_text2{
              font-size: 15px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              line-height: 0px;
              color: #333333;
              margin-left: 5%;
            }
          }
          &_container{
            width: 100%;
            height: 94%;
            overflow-y: scroll;
            .planCardNodata{
              height: 20%;
              background: #FFFFFF;
              border-radius: 4px;
              padding: 2%;
              margin-top: 10px;
              .nodataActive{
                font-size: 15px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                line-height: 0px;
                color: #007EFF;
                cursor: pointer;
              }
            }
            .planCard{
              background: #FFFFFF;
              border-radius: 4px;
              padding: 2%;
              margin-top: 10px;
              cursor: pointer;
              &_item1{
                display: flex;
                justify-content: space-between;
                &_text1{
                  width: 84%;
                  font-size: 15px;
                  font-family: Source Han Sans CN;
                  font-weight: bold;
                  line-height: 32px;
                  color: #333333;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
                &_text2{
                  font-size: 15px;
                  font-family: Source Han Sans CN;
                  font-weight: 400;
                  line-height: 32px;
                  color: #FF9C00;
                }
                &_text3{
                  color: #0DC95D;
                }
              }
              &_item2{
                display: flex;
                font-size: 15px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                line-height: 20px;
                color: #333333;
                &_text1{

                }
                &_text2{
                 margin-left: 4%;
                }
              }
              &_item3{
                font-size: 14px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                line-height: 22px;
                color: #333333;
              }
              &_item4{
                display: flex;
                flex-wrap: wrap;
                margin-top: 10px;
                &_image{
                  width: 24%;
                  height: 100px;
                  margin-right: 10px;
                  margin-bottom: 10px;
                  img{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .addPlanDialog{
    .el-dialog{
      height: 80%;
      margin-top: 12vh !important;
      .el-dialog__body{
        height: 86%;
        .textAreaInput{
          width: 100%;
          height: 100px!important;
        }
        .uploadBox{

        }
        .systemFormStyle{
          width: 100%;
          height: 48px;
        }
        .systemFormStyle2{
          .el-select{
            width: 100%;
            .el-input__inner{
              width: 100%;
              background: #F7F8FA;
              border: 1px solid #D2D4D9;
              border-radius: 4px;
              outline: 0;
              color: #666666;
            }
            input::-webkit-input-placeholder{
              font-size: 14px;
              color: #999999;
            }
          }
        }
        .formBox{
          height: 93%;
          overflow-y: scroll;
        }
        .btnBox{
          margin-top: 2%;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 160px;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }
  .planInfoDialog{
    .el-dialog{
      height: 68%;
      margin-top: 18vh !important;
      .el-dialog__body{
        height: 86%;
        .infoContainer{
          height: 90%;
          overflow-y: scroll;
          &_con{
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            line-height: 22px;
            color: #000000;
          }
          &_imageList{
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            &_imageItem{
              width: 32%;
              margin-right: 1%;
              margin-bottom: 1%;
              height: 95px;
              object-fit: cover;
            }
          }
          &_finished{
            width: 96%;
            padding: 2%;
            margin-top: 4%;
            background: #F2F2F2;
            border-radius: 4px;
            &_header{
              font-size: 15px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              line-height: 22px;
              color: #333333;
            }
            &_list{
              width: 100%;
              display: flex;
              justify-content: space-between;
              .listText{
                width: 49%;
                font-size: 14px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                line-height: 22px;
                color: #666666;
              }
            }
          }
        }
        .btnBox{
          margin-top: 2%;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 100%;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }
  .planSubmitDialog{
    .el-dialog{
      height: 68%;
      margin-top: 18vh !important;
      .el-dialog__body{
        height: 86%;
        .textAreaInput{
          width: 100%;
          height: 100px!important;
        }
        .uploadBox{
          width: 100%;
          height: 100px!important;
        }
        .systemFormStyle{
          width: 100%;
          height: 48px;
        }
        .formBox{
          height: 90%;
          overflow-y: scroll;
          .formAddIcon{
            position: absolute;
            left: 64px;
            top: -26px;
          }
          .formArray{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
          }
        }
        .btnBox{
          margin-top: 2%;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 160px;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }
  .delDialog{
    .el-dialog{
      height: 220px;
      margin-top: 30vh !important;
      .el-dialog__body{
        height: 125px;
        .text{
          height: 85px;
          text-align: center;
          div:nth-child(1){
            font-size: 15px;
            font-weight: 400;
            color: #666666;
          }
          div:nth-child(2){
            font-size: 12px;
            font-weight: 400;
            color: #0093BC;
            margin-top: 10px;
          }
        }
        .btnBox{
          margin-top: 2%;
          display: flex;
          justify-content: center;
          .btnItem{
            width: 100%;
            height: 40px;
            margin: 0 15px;
          }
        }
      }
    }
  }
}
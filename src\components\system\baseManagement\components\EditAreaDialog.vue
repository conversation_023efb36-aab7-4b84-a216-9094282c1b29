<template>
  <!-- 编辑弹窗 -->
  <el-dialog
    class="editDialog"
    title="编辑"
    :visible.sync="dialogVisible"
    width="37%"
    center
    :modal-append-to-body="false"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div class="close" @click="closeDialog('editRuleForm')">
      <img src="../../../../assets/image/managementSystem/close.png" alt="">
    </div>
    <div class="formList">
      <el-form :model="editRuleForm" :rules="editRules" ref="editRuleForm" label-width="100px" label-position="top">
        <div class="formItem">
          <el-form-item label="区域名称">
            <el-input v-model="editRuleForm.areaName" clearable class="systemFormStyle"></el-input>
          </el-form-item>
          <el-form-item label="当前作物">
            <el-input v-model="editRuleForm.crop" disabled class="systemFormStyle"></el-input>
          </el-form-item>
        </div>
        <div class="formItem1">
          <el-form-item label="包含设备">
            <!-- 加号按钮 - 保持原有位置 -->
            <div class="tips" @click="showEquipmentSelector" v-if="!showSelector">
              <img src="../../../../assets/image/managementSystem/add-icon.png" alt="">
            </div>
            <!-- 固定的第一个设备位置：下拉选择器 -->
            <div class="formItem1_con" v-if="showSelector">
              <el-select
              v-model="editRuleForm.equipment"
              placeholder="请选择设备"
              class="systemFormStyle"
              @change="handleEquipmentSelect">
                <el-option
                  v-for="item in availableEquipmentList"
                  :key="item.equipmentId"
                  :label="item.equipmentName"
                  :value="item.equipmentId">
                </el-option>
              </el-select>
              <img @click="cancelEquipmentSelector" src="../../../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
            </div>
            <!-- 占位的空 formItem1_con，确保布局一致 -->
            <div class="formItem1_con" v-if="!showSelector" style="visibility: hidden; height: 0; margin: 0; padding: 0;"></div>
            <!-- 已选设备列表 -->
            <div class="formItem1_con" v-for="(item,index) in editRuleForm.equipmentId" :key="index">
              <el-input v-model="editRuleForm.equipmentId[index].equipmentName" class="systemFormStyle" disabled></el-input>
              <img @click="removeEquipment(index)" src="../../../../assets/image/centralControlPlatform/reduce-icon.png" alt="">
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="btnBox">
      <div class="btnItem systemResetButtonStyle2">
        <el-button @click="cancelDialog('editRuleForm')">取消</el-button>
      </div>
      <div class="btnItem systemSearchButtonStyle2">
        <el-button @click="submitDialog('editRuleForm')">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import 农作物种类 from '../../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
import CommonService from '../../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
import BaseManagementService from '../../../../jaxrs/concrete/com.zny.ia.api.BaseManagementService.js';

export default {
  name: 'EditAreaDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editRuleForm: {
        areaId: "",
        areaName: "",
        crop: "",
        equipmentIds: "",
        equipmentId: [],
        equipment: "",
        equipmentList: [],
      },
      showSelector: false,
      editRules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    availableEquipmentList() {
      if (!this.editRuleForm.equipmentList) return [];
      const selectedIds = this.editRuleForm.equipmentId.map(item => item.equipmentId);
      return this.editRuleForm.equipmentList.filter(item => !selectedIds.includes(item.equipmentId));
    }
  },
  methods: {
    // 打开编辑弹窗
    openDialog(row) {
      this.dialogVisible = true;
      this.editRuleForm.areaId = row.areaVo.areaId;
      this.editRuleForm.areaName = row.areaVo.areaName;
      if (row.crop) {
        this.editRuleForm.crop = 农作物种类._lableOf(row.crop);
      }
      this.editRuleForm.equipmentId = row.equipmentSimpleInformationVo;
      this.showSelector = false; // 重置选择器状态
      this.getListEquipment();
    },
    // 获取设备列表
    getListEquipment() {
      CommonService.listEquipment()
      .then(res => {
        this.editRuleForm.equipmentList = res;
      })
    },
    // 显示设备选择器
    showEquipmentSelector() {
      this.showSelector = true;
    },
    // 取消设备选择器
    cancelEquipmentSelector() {
      this.showSelector = false;
      this.editRuleForm.equipment = ''; // 清空选择值
    },
    // 处理设备选择
    handleEquipmentSelect() {
      if (this.editRuleForm.equipment) {
        this.addEquipment();
        // 选择完设备后隐藏选择器，显示加号
        this.showSelector = false;
      }
    },
    // 点击数量增加
    addEquipment() {
      if (this.editRuleForm.equipment == "") {
        this.$message('请选择设备');
      } else {
        this.editRuleForm.equipmentList.forEach(e => {
          if (this.editRuleForm.equipment == e.equipmentId) {
            this.editRuleForm.equipmentId.push(e);
            this.editRuleForm.equipment = '';
          }
        })
      }
    },
    // 点击删除某一项
    removeEquipment(index) {
      this.editRuleForm.equipmentId.splice(index, 1);
      // 删除设备后不自动显示选择器，需要点击加号才显示
    },
    // 编辑弹窗关闭
    closeDialog(formName) {
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.dialogVisible = false;
    },
    // 编辑弹窗取消
    cancelDialog(formName) {
      this.$refs[formName].resetFields();
      this.showSelector = false;
      this.dialogVisible = false;
    },
    // 编辑弹窗确定
    submitDialog(formName) {
      const that = this;
      that.$refs[formName].validate((valid) => {
        if (valid) {
          let equipmentIds = [];
          that.editRuleForm.equipmentId.forEach(e => {
            equipmentIds.push(e.equipmentId);
          });
          let po = {
            areaId: that.editRuleForm.areaId,
            areaName: that.editRuleForm.areaName,
            equipmentIds: equipmentIds
          };
          BaseManagementService.changeAreaEquipment(po)
          .then(() => {
            that.$message({
              message: '修改成功',
              type: 'success'
            });
            that.$emit('edit-success');
            that.$refs[formName].resetFields();
            that.dialogVisible = false;
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 组件特定样式
</style>

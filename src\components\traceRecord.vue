<template>
  <div class="traceRecord">
    <div class="traceRecord_container">
      <div class="title traceRecord_container_title">
        <img src="../assets/image/monitoringCenter/title_icon.png" alt="">
        <span>溯源档案馆</span>
      </div>
      <div class="traceRecord_container_con">
        <div class="handleBox">
          <div class="handleBox_input formStyle">
            <el-input v-model="traceId" placeholder="请输入溯源码"></el-input>
          </div>
          <div class="handleBox_item searchButtonStyle2" @click="toTracePage(traceId)">
            <el-button>追溯查询</el-button>
          </div>
        </div>
        <div class="data_list">
          <div class="searchBox">
            <div class="searchBox_select">
              <div class="searchBox_select_name">区域：</div>
              <div class="searchBox_select_item formStyle">
                <el-select v-model="areaId" popper-class="selectStyle_list" placeholder="请选择区域" @change="areaChange">
                  <el-option
                          v-for="item in areaData"
                          :key="item.areaId"
                          :label="item.areaName"
                          :value="item.areaId"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="searchBox_select">
              <div class="searchBox_select_name">时间：</div>
              <div class="searchBox_select_date formStyle">
                <el-date-picker
                  v-model="time"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="timeChange"
                  popper-class='datePickerStyle'>
                </el-date-picker>
              </div>
            </div>
            <div class="searchBox_item searchButtonStyle2" @click="showDelHandle()">
              <el-button>删除档案</el-button>
            </div>
            <div class="searchBox_item searchButtonStyle2" @click="showEditHandle()">
              <el-button>编辑档案</el-button>
            </div>
          </div>
          <div class="tableContainer">
            <div class="tableList" v-for="(item,index) in tableData" :key="index" @click="toTracePage(item.id)">
              <div class="tableList_item">
                <div class="tableList_item_image">
                  <img :src=item.picUrl alt="" class="tableList_item_image_con">
                  <div class="tableList_item_image_name">{{item.serialNumber}}</div>
                </div>
                <div class="tableList_item_productName">{{item.name}}</div>
                <div class="tableList_item_info">
                  <div class="infoLeft">{{item.areaVo.areaName}}</div>
                  <div class="infoRight">{{item.dateTime}}</div>
                </div>
              </div>
              <div class="tableList_editor" v-if="showEdit" @click.stop="editHandle(item.id)">编辑</div>
              <div class="tableList_del" v-if="showDel" @click.stop="deleteHandle(item.id)">
                <img src="../assets/image/centralControlPlatform/del.png" alt="">
              </div>
            </div>
          </div>
        </div>
        <div class="pageChange">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next ,jumper " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
        </div>
      </div>
      <img @click="Back()" src="../assets/image/centralControlPlatform/back-icon.png" alt="" class="traceRecord_container_back">
    </div>
    <div class="delDialog dialogStyle">
      <el-dialog
              title="提示"
              :visible.sync="delDialog"
              width="320px"
              center
              :modal-append-to-body="false"
              :show-close="false"
              :close-on-click-modal="false"
              :close-on-press-escape="false">
        <div class="close" @click="delDialog=false">
          <img src="../assets/image/centralControlPlatform/close.png" alt="">
        </div>
        <div class="text">
          <div>确定删除这条数据？</div>
        </div>
        <div class="btnBox">
          <div class="submit_button searchButtonStyle2" @click="delDialog=false">
            <el-button>取消</el-button>
          </div>
          <div class="submit_button submitButtonStyle2" @click="delSubmit()">
            <el-button>确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import TraceSourceService from '../jaxrs/concrete/com.zny.ia.api.TraceSourceService'
  import TraceSourceCommonService from '../jaxrs/concrete/com.zny.ia.api.TraceSourceCommonService'
  import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
  export default {
    name:'traceRecord',
    data(){
      return{
        traceId:"",
        areaId:"",
        areaData:[],
        time:[],
        startTime:'',
        endTime:'',
        tableData:[],
        pageSize:10,
        currentPage:1,
        total:0,
        pageCount:1,
        showDel:false,
        showEdit:false,
        delDialog:false,
      }
    },
    mounted(){
      this.forList()
      this.getAllAreaData()
    },
    methods: {
      //返回
      Back(){
        this.$emit('trackRecordIsBack',false)
      },
      //获取所有区域
      getAllAreaData(){
        CommonService.allAreaListInformation()
        .then(res=>{
          this.areaData=res
        })
      },
      //区域改变
      areaChange(val){
        this.areaId=val
        this.currentPage=1
        this.forList()
      },
      //时间改变
      timeChange(val){
        if(val==null||val==undefined||val==""){
          this.startTime=""
          this.endTime=""
        }else{
          var arr=[]
          for(var i=0;i<val.length;i++){
            var y = val[i].getFullYear();
            var m = val[i].getMonth() + 1;
            m = m < 10 ? ('0' + m) : m;
            var d = val[i].getDate();
            d = d < 10 ? ('0' + d) : d;
            var time= y+'-'+m+'-'+d;
            arr.push(time)
          }
          this.startTime=arr[0]
          this.endTime=arr[1]
        }
        this.currentPage=1
        this.forList()
      },
      //打开溯源网页
      toTracePage(traceId){
        if(traceId==""){
          return
        }else{
          TraceSourceCommonService.traceSourceDetail(traceId)
                  .then(()=>{
                    let routerData = this.$router.resolve({
                      path: `/tracePage`,
                      query:{id:traceId}
                    });
                    window.open(routerData.href,'_blank')
                  })
        }
      },
      //获取列表
      forList(){
        var param = {
          num:this.currentPage,
          pageSize:this.pageSize,
          condition:{
            areaId:this.areaId,
            startTime:this.startTime,
            endTime:this.endTime,
          }
        }
        TraceSourceCommonService.listTraceSourceRecord(param)
        .then(res=>{
          this.tableData=res.list
          this.pageCount=res.total
          this.total=res.count
        })
      },
      //删除按钮点击
      showDelHandle(){
        this.showDel? this.showDel=false: this.showDel=true
        this.showEdit=false
      },
      //编辑按钮点击
      showEditHandle(){
        this.showEdit? this.showEdit=false: this.showEdit=true
        this.showDel=false
      },
      //删除操作
      deleteHandle(id){
        this.traceId=id
        this.delDialog=true
      },
      //删除确定
      delSubmit(){
        TraceSourceCommonService.removeTraceSourceById(this.traceId)
        .then(()=>{
          this.$message({
            message:'删除成功！',
            type:'success'
          })
          this.delDialog=false
          this.showDel=false
          this.forList()
        })
        .catch(()=>{
          this.delDialog=false
        })
      },
      //编辑
      editHandle(id){
        this.zEmit('traceRecordAddDialogOpen',id)
      },
      handleSizeChange(){

      },
      handleCurrentChange(val){
        this.currentPage = val;
      },
    },
  }
</script>
<style lang="less">
  @import '../assets/css/traceRecord.less';
</style>
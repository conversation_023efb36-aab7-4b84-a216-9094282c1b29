.baseEquipmentHistoryDialog{
  .soilHistoryDialog,
  .InsectSituationHistoryDialog,
  .meteorologicalHistoryDialog,
  .irrigationHistoryDialog,
  .seedlingGrowthHistoryDialog,
  .waterMonitorHistoryDialog{
    .el-dialog{
      height: 573px;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 478px;
        .handleBox{
          display: flex;
          align-items: center;
          &_item{
            height: 40px;
            margin-right: 16px;
          }
          &_item:nth-child(1),
          &_item:nth-child(2),
          &_item:nth-child(3){
            width: 270px;
          }
          &_item:nth-child(4){
            width: 380px;
          }
          &_item:nth-child(5){
            width: 80px;
          }
        }
        .tableBox{
          height: 418px;
          margin-top: 20px;
          .cell{
            img{
              width: 46px;
              height: 35px;
            }
          }
        }
      }
    }
  }
  .InsectSituationHistoryDialog,
  // .meteorologicalHistoryDialog,
  .seedlingGrowthHistoryDialog{
    .el-dialog{
      .el-dialog__body{
        .handleBox{
          &_item:nth-child(3){
            width: 380px;
          }
          &_item:nth-child(4){
            width: 80px;
          }
        }
      }
    }
  }
  .irrigationHistoryDialog{
    .el-dialog{
      .el-dialog__body{
        .handleBox{
          &_item:nth-child(1){
            width: 380px;
          }
          &_item:nth-child(2){
            width: 80px;
          }
        }
      }
    }
  }
  .soilHistoryDialog{
    .el-dialog{
      .el-dialog__body{
        .handleBox{
          &_item:nth-child(3){
            width: 380px;
          }
          &_item:nth-child(4){
            width: 80px;
          }
        }
      }
    }
  }
  .meteorologicalHistoryDialog{
    .el-dialog{
      .el-dialog__body{
        .handleBox{
          &_item:nth-child(2){
            width: 380px;
          }
          &_item:nth-child(3){
            width: 80px;
          }
        }
      }
    }
  }
  .waterMonitorHistoryDialog{
    .el-dialog{
      .el-dialog__body{
        .handleBox{
          &_item:nth-child(2){
            width: 380px;
          }
          &_item:nth-child(3){
            width: 80px;
          }
        }
        .tableBox{
          .cell{
            .details{
              color: #007EFF;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  .detailsDialog{
    .el-dialog{
      height: 900px;
      margin-top: 5vh !important;
      .el-dialog__body{
        height: 805px;
        .tableBox{
          height: 770px;
        }
        .tips{
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          margin-top: 17px;
          text-align: right;
        }
      }
    }
  }
}
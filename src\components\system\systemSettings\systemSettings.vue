<template>
  <div class="systemSettings systemDialogStyle">
    <!-- <div class="systemSettings_title">
      用户管理
    </div> -->
    <div class="systemSettings_con">
      <div class="handleBox">
        <div class="handleBox_item systemFormStyle">
          <el-input v-model="loginName" clearable placeholder="请输入登录名"></el-input>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="search">搜索</el-button>
        </div>
        <div class="handleBox_item systemSearchButtonStyle2">
          <el-button @click="addUser">添加用户</el-button>
        </div>
      </div>
      <div class="tableBox systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%">
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            prop="loginName"
            align="center" 
            label="登录名"
            width="180">
          </el-table-column>
          <el-table-column
            prop="userId"
            align="center" 
            label="用户ID"
            width="180">
          </el-table-column>
          <el-table-column
            prop="nickname"
            align="center" 
            label="用户昵称">
          </el-table-column>
          <el-table-column
            prop="jurisdiction"
            align="center" 
            label="角色">
          </el-table-column>
          <el-table-column
            align="center" 
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" v-if="scoped.row.userType!=0" @click="edit(scoped.row)">编辑</span>
              <span class="viewData" v-if="scoped.row.userType!=0" @click="del(scoped.row)">删除</span>
              <span class="viewData" @click="resetPassword(scoped.row)">重置密码</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    <el-dialog
      class="editDialog"
      :title="title"
      :visible.sync="dialogVisible"
      width="23%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open="dialogVisibleOpen">
        <div class="close" @click="close('ruleForm')">
          <img src="../../../assets/image/managementSystem/close.png" alt="">
        </div>
        <div class="formBox">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px" label-position="top">
            <el-form-item label="用户名" prop="loginName">
              <el-input class="systemFormStyle" clearable placeholder="请输入用户名" v-model="ruleForm.loginName"></el-input>
            </el-form-item>
            <!-- <el-form-item label="密码">
              <el-input class="systemFormStyle" clearable placeholder="请输入密码" v-model="ruleForm.name"></el-input>
            </el-form-item> -->
            <el-form-item label="手机号" prop="telephone">
              <el-input class="systemFormStyle" clearable placeholder="请输入手机号" v-model.number="ruleForm.telephone"></el-input>
            </el-form-item>
            <el-form-item label="账号角色" prop="jurisdiction">
              <div class="systemRadio">
                <el-radio v-model="ruleForm.radio" :label=2 @change="roleChange">游客</el-radio>
                <el-radio v-model="ruleForm.radio" :label=1 @change="roleChange">管理员</el-radio>
              </div>
              <div v-if="ruleForm.radio==1" class="systemCheckBox">
                <el-checkbox-group v-model="ruleForm.jurisdiction">
                  <el-checkbox v-for="item in adminList" :key="item.areaId" :label="item.areaId">{{item.areaName}}</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="btnBox">
          <div class="btnItem systemResetButtonStyle2">
            <el-button @click="cancel('ruleForm')">取消</el-button>
          </div>
          <div class="btnItem systemSearchButtonStyle2">
            <el-button @click="submit('ruleForm')">确定</el-button>
          </div>
        </div>
    </el-dialog>
    <!-- 重置密码提示弹窗 -->
    <el-dialog
      class="resetPasswordDialog"
      title="提示"
      :visible.sync="resetPasswordDialog"
      width="20%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="resetPasswordClose()">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="text">
        <div>是否将密码重置为初始密码？</div>
        <div>初始密码为123456</div>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="resetPasswordCancel()">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="resetPasswordSubmit()">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 删除提示弹窗 -->
    <el-dialog
      class="delDialog"
      title="提示"
      :visible.sync="delDialog"
      width="20%"
      center
      :modal-append-to-body="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="close" @click="delClose">
        <img src="../../../assets/image/managementSystem/close.png" alt="">
      </div>
      <div class="text">
        <div>确定删除这条数据？</div>
      </div>
      <div class="btnBox">
        <div class="btnItem systemResetButtonStyle2">
          <el-button @click="delCancel">取消</el-button>
        </div>
        <div class="btnItem systemSearchButtonStyle2">
          <el-button @click="delSubmit">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import UserService from '../../../jaxrs/concrete/com.zny.ia.api.UserService.js';
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
export default {
  data(){
    //角色选择
    const validateRole = (rule,value,callback) => {
      if(this.ruleForm.radio==1&&value.length==0){
        callback(new Error('请选择管理区域'))
      }else {
        callback();
      }
    };
    return{
      loginName:"",
      tableData: [],
      // 分页
      pageSize:20,
      currentPage:1,
      total:0,
      pageCount:1,
      // 添加用户弹窗
      title:"",
      dialogVisible:false,
      adminList:[],
      ruleForm:{
        loginName:"",
        telephone:"",
        radio:2,
        jurisdiction:[],
      },
      rules:{
        loginName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        telephone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { type: 'number', message: '联系方式必须为数字值'},
          { pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/, message: '请输入正确手机号格式' }
        ],
        jurisdiction: [
          {type:'array', required: true, trigger: 'change',validator:validateRole},
        ],
      },
      resetPasswordDialog:false,//重置密码弹窗
      delDialog:false,//删除弹窗
      userId:"",//用户id
    }
  },
  mounted(){
    this.getList()
  },
  methods:{
    // 获取列表数据
    getList(){
      let po={
        num:this.currentPage,
        pageSize:this.pageSize,
        condition:{
          loginName:this.loginName
        }
      }
      UserService.userList(po)
      .then(res=>{
        this.tableData=res.list
        this.pageCount=res.totalPage
        this.total=res.totalNum
      })
    },
    // 搜索
    search(){
      this.currentPage=1
      this.getList()
    },
    handleSizeChange(){

    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.getList()
    },
    // 点击添加按钮
    addUser(){
      this.dialogVisible=true
      this.title='添加用户'
    },
    //角色改变
    roleChange(val){
      this.ruleForm.jurisdiction=[]
    },
    // 点击编辑按钮
    edit(row){
      this.userId=row.userId
      this.dialogVisible=true
      this.title='编辑'
      this.getUserDetail()
    },
    //获取详情
    getUserDetail(){
      UserService.findUserInfo(this.userId)
      .then(res=>{
        this.ruleForm.radio=Number(res.userType.toString())
        this.ruleForm.loginName=res.loginName
        this.ruleForm.telephone=Number(res.telephone)
        this.ruleForm.jurisdiction=res.jurisdiction
      })
    },
    // 新增编辑弹窗打开时获取区域信息
    dialogVisibleOpen(){
      // 获取所有区域信息
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        this.adminList=res
      })
    },
    // 关闭新增、编辑弹窗
    close(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
      this.ruleForm.radio=2
      this.userId=''
    },
     // 取消新增、编辑弹窗
    cancel(formName){
      this.$refs[formName].resetFields();
      this.dialogVisible=false
    },
    // 确定提交新增、编辑弹窗
    submit(formName){
      const that=this
      that.$refs[formName].validate((valid) => {
        if (valid) {
          var po={
            loginName:that.ruleForm.loginName,
            telephone:that.ruleForm.telephone,
            jurisdiction:that.ruleForm.jurisdiction,
            role:[that.ruleForm.radio]
          }
          if(that.userId==""){
            UserService.userAdd(po)
            .then(()=>{
              that.$refs[formName].resetFields();
              that.dialogVisible=false
              that.getList()
            })
          }else{
            UserService.userUpdate(that.userId,po)
            .then(()=>{
              that.$refs[formName].resetFields();
              that.dialogVisible=false
              that.getList()
            })
          }
        }
      })
    },
    // 打开重置密码弹窗
    resetPassword(row){
      this.resetPasswordDialog=true
      this.userId=row.userId
    },
    // 重置密码关闭
    resetPasswordClose(){
      this.resetPasswordDialog=false
      this.userId=''
    },
    // 重置密码取消
    resetPasswordCancel(){
      this.resetPasswordDialog=false
      this.userId=''
    },
    // 重置密码确定
    resetPasswordSubmit(){
      UserService.passwordReset(this.userId)
      .then(res=>{
        // console.log(res);
        this.$message({
          message: '重置密码成功',
          type: 'success'
        });
        this.resetPasswordDialog=false
      })
    },
    // 打开删除弹窗
    del(row){
      this.delDialog=true
      this.userId=row.userId
    },
    // 删除关闭
    delClose(){
      this.delDialog=false
      this.userId=''
    },
    // 删除取消
    delCancel(){
      this.delDialog=false
      this.userId=''
    },
    // 删除确定
    delSubmit(){
      UserService.userDelete(this.userId)
      .then(()=>{
        // console.log(res);
        this.$message({
          message: '删除成功',
          type: 'success'
        });
        this.delDialog=false
        this.getList()
      })
    },
  },
}

</script>
<style lang="less">
@import '../../../assets/css/system/systemSettings.less';
@media screen and (max-width: 1280px){
  .systemSettings{
    .editDialog{
      .el-dialog{
        width: 32% !important;
      }
    }
  }
}
</style>
import { valueOf, toArray } from './constUtil'
export default {
    /**
     * label: 后台
     * desc: --
     * value: 0
     */
    后台: 0,
    /**
     * label: 前台
     * desc: --
     * value: 1
     */
    前台: 1,
    _lableOf(v) {
        const o = valueOf(this, v)
        if (o) return o.key
        throw 'not found: ' + v
    },
    _toArray(values) {
        return toArray(this, values)
    },
}